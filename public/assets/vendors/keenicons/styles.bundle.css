@font-face {
  font-family: 'keenicons-duotone';
  src:
    url('fonts/keenicons-duotone.ttf?gcn9yo') format('truetype'),
    url('fonts/keenicons-duotone.woff?gcn9yo') format('woff'),
    url('fonts/keenicons-duotone.svg?gcn9yo#keenicons-duotone') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.ki-duotone {
  line-height: 1;
  position: relative;
  display: inline-flex;
}

.ki-duotone:after, 
.ki-duotone:before {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'keenicons-duotone' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-flex;
}

.ki-abstract-1.ki-duotone:after {
  content: "\e900";
  
  opacity: 0.3;
}
.ki-abstract-1.ki-duotone:before {
  content: "\e901";
  position: absolute;
  
}
.ki-abstract-2.ki-duotone:after {
  content: "\e902";
  
  opacity: 0.3;
}
.ki-abstract-2.ki-duotone:before {
  content: "\e903";
  position: absolute;
  
}
.ki-abstract-3.ki-duotone:after {
  content: "\e904";
  
  opacity: 0.3;
}
.ki-abstract-3.ki-duotone:before {
  content: "\e905";
  position: absolute;
  
}
.ki-abstract-4.ki-duotone:after {
  content: "\e906";
  
  opacity: 0.3;
}
.ki-abstract-4.ki-duotone:before {
  content: "\e907";
  position: absolute;
  
}
.ki-abstract-5.ki-duotone:after {
  content: "\e908";
  
  opacity: 0.3;
}
.ki-abstract-5.ki-duotone:before {
  content: "\e909";
  position: absolute;
  
}
.ki-abstract-6.ki-duotone:before {
  content: "\e90a";
}
.ki-abstract-7.ki-duotone:after {
  content: "\e90b";
  
  opacity: 0.3;
}
.ki-abstract-7.ki-duotone:before {
  content: "\e90c";
  position: absolute;
  
}
.ki-abstract-8.ki-duotone:after {
  content: "\e90d";
  
  opacity: 0.3;
}
.ki-abstract-8.ki-duotone:before {
  content: "\e90e";
  position: absolute;
  
}
.ki-abstract-9.ki-duotone:after {
  content: "\e90f";
  
  opacity: 0.3;
}
.ki-abstract-9.ki-duotone:before {
  content: "\e910";
  position: absolute;
  
}
.ki-abstract-10.ki-duotone:after {
  content: "\e911";
  
  opacity: 0.3;
}
.ki-abstract-10.ki-duotone:before {
  content: "\e912";
  position: absolute;
  
}
.ki-abstract-11.ki-duotone:after {
  content: "\e913";
  
  opacity: 0.3;
}
.ki-abstract-11.ki-duotone:before {
  content: "\e914";
  position: absolute;
  
}
.ki-abstract-12.ki-duotone:after {
  content: "\e915";
  
  opacity: 0.3;
}
.ki-abstract-12.ki-duotone:before {
  content: "\e916";
  position: absolute;
  
}
.ki-abstract-13.ki-duotone:after {
  content: "\e917";
  
  opacity: 0.3;
}
.ki-abstract-13.ki-duotone:before {
  content: "\e918";
  position: absolute;
  
}
.ki-abstract-14.ki-duotone:after {
  content: "\e919";
  
}
.ki-abstract-14.ki-duotone:before {
  content: "\e91a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-15.ki-duotone:after {
  content: "\e91b";
  
  opacity: 0.3;
}
.ki-abstract-15.ki-duotone:before {
  content: "\e91c";
  position: absolute;
  
}
.ki-abstract-16.ki-duotone:after {
  content: "\e91d";
  
  opacity: 0.3;
}
.ki-abstract-16.ki-duotone:before {
  content: "\e91e";
  position: absolute;
  
}
.ki-abstract-17.ki-duotone:after {
  content: "\e91f";
  
}
.ki-abstract-17.ki-duotone:before {
  content: "\e920";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-18.ki-duotone:after {
  content: "\e921";
  
}
.ki-abstract-18.ki-duotone:before {
  content: "\e922";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-19.ki-duotone:after {
  content: "\e923";
  
}
.ki-abstract-19.ki-duotone:before {
  content: "\e924";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-20.ki-duotone:after {
  content: "\e925";
  
}
.ki-abstract-20.ki-duotone:before {
  content: "\e926";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-21.ki-duotone:after {
  content: "\e927";
  
  opacity: 0.3;
}
.ki-abstract-21.ki-duotone:before {
  content: "\e928";
  position: absolute;
  
}
.ki-abstract-22.ki-duotone:after {
  content: "\e929";
  
  opacity: 0.3;
}
.ki-abstract-22.ki-duotone:before {
  content: "\e92a";
  position: absolute;
  
}
.ki-abstract-23.ki-duotone:after {
  content: "\e92b";
  
  opacity: 0.3;
}
.ki-abstract-23.ki-duotone:before {
  content: "\e92c";
  position: absolute;
  
}
.ki-abstract-24.ki-duotone:after {
  content: "\e92d";
  
}
.ki-abstract-24.ki-duotone:before {
  content: "\e92e";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-25.ki-duotone:after {
  content: "\e92f";
  
  opacity: 0.3;
}
.ki-abstract-25.ki-duotone:before {
  content: "\e930";
  position: absolute;
  
}
.ki-abstract-26.ki-duotone:after {
  content: "\e931";
  
  opacity: 0.3;
}
.ki-abstract-26.ki-duotone:before {
  content: "\e932";
  position: absolute;
  
}
.ki-abstract-27.ki-duotone:after {
  content: "\e933";
  
}
.ki-abstract-27.ki-duotone:before {
  content: "\e934";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-28.ki-duotone:after {
  content: "\e935";
  
}
.ki-abstract-28.ki-duotone:before {
  content: "\e936";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-29.ki-duotone:after {
  content: "\e937";
  
}
.ki-abstract-29.ki-duotone:before {
  content: "\e938";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-30.ki-duotone:after {
  content: "\e939";
  
}
.ki-abstract-30.ki-duotone:before {
  content: "\e93a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-31.ki-duotone:after {
  content: "\e93b";
  
}
.ki-abstract-31.ki-duotone:before {
  content: "\e93c";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-32.ki-duotone:after {
  content: "\e93d";
  
}
.ki-abstract-32.ki-duotone:before {
  content: "\e93e";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-33.ki-duotone:after {
  content: "\e93f";
  
  opacity: 0.3;
}
.ki-abstract-33.ki-duotone:before {
  content: "\e940";
  position: absolute;
  
}
.ki-abstract-34.ki-duotone:after {
  content: "\e941";
  
  opacity: 0.3;
}
.ki-abstract-34.ki-duotone:before {
  content: "\e942";
  position: absolute;
  
}
.ki-abstract-35.ki-duotone:after {
  content: "\e943";
  
}
.ki-abstract-35.ki-duotone:before {
  content: "\e944";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-36.ki-duotone:after {
  content: "\e945";
  
}
.ki-abstract-36.ki-duotone:before {
  content: "\e946";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-37.ki-duotone:after {
  content: "\e947";
  
  opacity: 0.3;
}
.ki-abstract-37.ki-duotone:before {
  content: "\e948";
  position: absolute;
  
}
.ki-abstract-38.ki-duotone:after {
  content: "\e949";
  
}
.ki-abstract-38.ki-duotone:before {
  content: "\e94a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-39.ki-duotone:after {
  content: "\e94b";
  
  opacity: 0.3;
}
.ki-abstract-39.ki-duotone:before {
  content: "\e94c";
  position: absolute;
  
}
.ki-abstract-40.ki-duotone:after {
  content: "\e94d";
  
}
.ki-abstract-40.ki-duotone:before {
  content: "\e94e";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-41.ki-duotone:after {
  content: "\e94f";
  
}
.ki-abstract-41.ki-duotone:before {
  content: "\e950";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-42.ki-duotone:after {
  content: "\e951";
  
  opacity: 0.3;
}
.ki-abstract-42.ki-duotone:before {
  content: "\e952";
  position: absolute;
  
}
.ki-abstract-43.ki-duotone:after {
  content: "\e953";
  
  opacity: 0.3;
}
.ki-abstract-43.ki-duotone:before {
  content: "\e954";
  position: absolute;
  
}
.ki-abstract-44.ki-duotone:after {
  content: "\e955";
  
}
.ki-abstract-44.ki-duotone:before {
  content: "\e956";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-45.ki-duotone:after {
  content: "\e957";
  
}
.ki-abstract-45.ki-duotone:before {
  content: "\e958";
  position: absolute;
  
  opacity: 0.3;
}
.ki-abstract-46.ki-duotone:after {
  content: "\e959";
  
  opacity: 0.3;
}
.ki-abstract-46.ki-duotone:before {
  content: "\e95a";
  position: absolute;
  
}
.ki-abstract-47.ki-duotone:after {
  content: "\e95b";
  
  opacity: 0.3;
}
.ki-abstract-47.ki-duotone:before {
  content: "\e95c";
  position: absolute;
  
}
.ki-abstract-48.ki-duotone:after {
  content: "\e95d";
  
  opacity: 0.3;
}
.ki-abstract-48.ki-duotone:before {
  content: "\e95e";
  position: absolute;
  
}
.ki-abstract-49.ki-duotone:after {
  content: "\e95f";
  
  opacity: 0.3;
}
.ki-abstract-49.ki-duotone:before {
  content: "\e960";
  position: absolute;
  
}
.ki-abstract.ki-duotone:after {
  content: "\e961";
  
  opacity: 0.3;
}
.ki-abstract.ki-duotone:before {
  content: "\e962";
  position: absolute;
  
}
.ki-add-files.ki-duotone:after {
  content: "\e963";
  
  opacity: 0.3;
}
.ki-add-files.ki-duotone:before {
  content: "\e964";
  position: absolute;
  
}
.ki-add-folder.ki-duotone:after {
  content: "\e965";
  
  opacity: 0.3;
}
.ki-add-folder.ki-duotone:before {
  content: "\e966";
  position: absolute;
  
}
.ki-add-notepad.ki-duotone:after {
  content: "\e967";
  
  opacity: 0.3;
}
.ki-add-notepad.ki-duotone:before {
  content: "\e968";
  position: absolute;
  
}
.ki-additem.ki-duotone:after {
  content: "\e969";
  
  opacity: 0.3;
}
.ki-additem.ki-duotone:before {
  content: "\e96a";
  position: absolute;
  
}
.ki-address-book.ki-duotone:after {
  content: "\e96b";
  
}
.ki-address-book.ki-duotone:before {
  content: "\e96c";
  position: absolute;
  
  opacity: 0.3;
}
.ki-airplane-square.ki-duotone:after {
  content: "\e96d";
  
  opacity: 0.3;
}
.ki-airplane-square.ki-duotone:before {
  content: "\e96e";
  position: absolute;
  
}
.ki-airplane.ki-duotone:after {
  content: "\e96f";
  
  opacity: 0.3;
}
.ki-airplane.ki-duotone:before {
  content: "\e970";
  position: absolute;
  
}
.ki-airpod.ki-duotone:after {
  content: "\e971";
  
  opacity: 0.3;
}
.ki-airpod.ki-duotone:before {
  content: "\e972";
  position: absolute;
  
}
.ki-android.ki-duotone:after {
  content: "\e973";
  
}
.ki-android.ki-duotone:before {
  content: "\e974";
  position: absolute;
  
  opacity: 0.3;
}
.ki-angular.ki-duotone:after {
  content: "\e975";
  
}
.ki-angular.ki-duotone:before {
  content: "\e976";
  position: absolute;
  
  opacity: 0.3;
}
.ki-apple.ki-duotone:before {
  content: "\e977";
}
.ki-archive-tick.ki-duotone:after {
  content: "\e978";
  
  opacity: 0.3;
}
.ki-archive-tick.ki-duotone:before {
  content: "\e979";
  position: absolute;
  
}
.ki-archive.ki-duotone:after {
  content: "\e97a";
  
  opacity: 0.3;
}
.ki-archive.ki-duotone:before {
  content: "\e97b";
  position: absolute;
  
}
.ki-arrow-circle-left.ki-duotone:after {
  content: "\e97c";
  
  opacity: 0.3;
}
.ki-arrow-circle-left.ki-duotone:before {
  content: "\e97d";
  position: absolute;
  
}
.ki-arrow-circle-right.ki-duotone:after {
  content: "\e97e";
  
  opacity: 0.3;
}
.ki-arrow-circle-right.ki-duotone:before {
  content: "\e97f";
  position: absolute;
  
}
.ki-arrow-down-left.ki-duotone:after {
  content: "\e980";
  
  opacity: 0.3;
}
.ki-arrow-down-left.ki-duotone:before {
  content: "\e981";
  position: absolute;
  
}
.ki-arrow-down-refraction.ki-duotone:after {
  content: "\e982";
  
}
.ki-arrow-down-refraction.ki-duotone:before {
  content: "\e983";
  position: absolute;
  
  opacity: 0.3;
}
.ki-arrow-down-right.ki-duotone:after {
  content: "\e984";
  
  opacity: 0.3;
}
.ki-arrow-down-right.ki-duotone:before {
  content: "\e985";
  position: absolute;
  
}
.ki-arrow-down.ki-duotone:after {
  content: "\e986";
  
  opacity: 0.3;
}
.ki-arrow-down.ki-duotone:before {
  content: "\e987";
  position: absolute;
  
}
.ki-arrow-left.ki-duotone:after {
  content: "\e988";
  
  opacity: 0.3;
}
.ki-arrow-left.ki-duotone:before {
  content: "\e989";
  position: absolute;
  
}
.ki-arrow-mix.ki-duotone:after {
  content: "\e98a";
  
  opacity: 0.3;
}
.ki-arrow-mix.ki-duotone:before {
  content: "\e98b";
  position: absolute;
  
}
.ki-arrow-right-left.ki-duotone:after {
  content: "\e98c";
  
}
.ki-arrow-right-left.ki-duotone:before {
  content: "\e98d";
  position: absolute;
  
  opacity: 0.3;
}
.ki-arrow-right.ki-duotone:after {
  content: "\e98e";
  
  opacity: 0.3;
}
.ki-arrow-right.ki-duotone:before {
  content: "\e98f";
  position: absolute;
  
}
.ki-arrow-two-diagonals.ki-duotone:after {
  content: "\e990";
  
  opacity: 0.3;
}
.ki-arrow-two-diagonals.ki-duotone:before {
  content: "\e991";
  position: absolute;
  
}
.ki-arrow-up-down.ki-duotone:after {
  content: "\e992";
  
}
.ki-arrow-up-down.ki-duotone:before {
  content: "\e993";
  position: absolute;
  
  opacity: 0.3;
}
.ki-arrow-up-left.ki-duotone:after {
  content: "\e994";
  
  opacity: 0.3;
}
.ki-arrow-up-left.ki-duotone:before {
  content: "\e995";
  position: absolute;
  
}
.ki-arrow-up-refraction.ki-duotone:after {
  content: "\e996";
  
}
.ki-arrow-up-refraction.ki-duotone:before {
  content: "\e997";
  position: absolute;
  
  opacity: 0.3;
}
.ki-arrow-up-right.ki-duotone:after {
  content: "\e998";
  
  opacity: 0.3;
}
.ki-arrow-up-right.ki-duotone:before {
  content: "\e999";
  position: absolute;
  
}
.ki-arrow-up.ki-duotone:after {
  content: "\e99a";
  
  opacity: 0.3;
}
.ki-arrow-up.ki-duotone:before {
  content: "\e99b";
  position: absolute;
  
}
.ki-arrow-zigzag.ki-duotone:after {
  content: "\e99c";
  
}
.ki-arrow-zigzag.ki-duotone:before {
  content: "\e99d";
  position: absolute;
  
  opacity: 0.3;
}
.ki-arrows-circle.ki-duotone:after {
  content: "\e99e";
  
  opacity: 0.3;
}
.ki-arrows-circle.ki-duotone:before {
  content: "\e99f";
  position: absolute;
  
}
.ki-arrows-loop.ki-duotone:after {
  content: "\e9a0";
  
  opacity: 0.3;
}
.ki-arrows-loop.ki-duotone:before {
  content: "\e9a1";
  position: absolute;
  
}
.ki-artificial-intelligence.ki-duotone:after {
  content: "\e9a2";
  
  opacity: 0.3;
}
.ki-artificial-intelligence.ki-duotone:before {
  content: "\e9a3";
  position: absolute;
  
}
.ki-autobrightness.ki-duotone:after {
  content: "\e9a4";
  
  opacity: 0.3;
}
.ki-autobrightness.ki-duotone:before {
  content: "\e9a5";
  position: absolute;
  
}
.ki-avalanche-avax.ki-duotone:after {
  content: "\e9a6";
  
  opacity: 0.3;
}
.ki-avalanche-avax.ki-duotone:before {
  content: "\e9a7";
  position: absolute;
  
}
.ki-award.ki-duotone:after {
  content: "\e9a8";
  
  opacity: 0.3;
}
.ki-award.ki-duotone:before {
  content: "\e9a9";
  position: absolute;
  
}
.ki-badge.ki-duotone:after {
  content: "\e9aa";
  
  opacity: 0.3;
}
.ki-badge.ki-duotone:before {
  content: "\e9ab";
  position: absolute;
  
}
.ki-bandage.ki-duotone:after {
  content: "\e9ac";
  
  opacity: 0.3;
}
.ki-bandage.ki-duotone:before {
  content: "\e9ad";
  position: absolute;
  
}
.ki-bank.ki-duotone:after {
  content: "\e9ae";
  
  opacity: 0.3;
}
.ki-bank.ki-duotone:before {
  content: "\e9af";
  position: absolute;
  
}
.ki-bar-chart.ki-duotone:after {
  content: "\e9b0";
  
}
.ki-bar-chart.ki-duotone:before {
  content: "\e9b1";
  position: absolute;
  
  opacity: 0.3;
}
.ki-barcode.ki-duotone:after {
  content: "\e9b2";
  
}
.ki-barcode.ki-duotone:before {
  content: "\e9b3";
  position: absolute;
  
  opacity: 0.3;
}
.ki-basket-ok.ki-duotone:after {
  content: "\e9b4";
  
  opacity: 0.3;
}
.ki-basket-ok.ki-duotone:before {
  content: "\e9b5";
  position: absolute;
  
}
.ki-basket.ki-duotone:after {
  content: "\e9b6";
  
  opacity: 0.3;
}
.ki-basket.ki-duotone:before {
  content: "\e9b7";
  position: absolute;
  
}
.ki-behance.ki-duotone:after {
  content: "\e9b8";
  
}
.ki-behance.ki-duotone:before {
  content: "\e9b9";
  position: absolute;
  
  opacity: 0.3;
}
.ki-bill.ki-duotone:after {
  content: "\e9ba";
  
  opacity: 0.3;
}
.ki-bill.ki-duotone:before {
  content: "\e9bb";
  position: absolute;
  
}
.ki-binance-usd-busd.ki-duotone:after {
  content: "\e9bc";
  
}
.ki-binance-usd-busd.ki-duotone:before {
  content: "\e9bd";
  position: absolute;
  
  opacity: 0.3;
}
.ki-binance.ki-duotone:after {
  content: "\e9be";
  
  opacity: 0.3;
}
.ki-binance.ki-duotone:before {
  content: "\e9bf";
  position: absolute;
  
}
.ki-bitcoin.ki-duotone:after {
  content: "\e9c0";
  
  opacity: 0.3;
}
.ki-bitcoin.ki-duotone:before {
  content: "\e9c1";
  position: absolute;
  
}
.ki-black-down.ki-duotone:before {
  content: "\e9c2";
}
.ki-black-left-line.ki-duotone:after {
  content: "\e9c3";
  
}
.ki-black-left-line.ki-duotone:before {
  content: "\e9c4";
  position: absolute;
  
  opacity: 0.3;
}
.ki-black-left.ki-duotone:before {
  content: "\e9c5";
}
.ki-black-right-line.ki-duotone:after {
  content: "\e9c6";
  
}
.ki-black-right-line.ki-duotone:before {
  content: "\e9c7";
  position: absolute;
  
  opacity: 0.3;
}
.ki-black-right.ki-duotone:before {
  content: "\e9c8";
}
.ki-black-up.ki-duotone:before {
  content: "\e9c9";
}
.ki-bluetooth.ki-duotone:after {
  content: "\e9ca";
  
}
.ki-bluetooth.ki-duotone:before {
  content: "\e9cb";
  position: absolute;
  
  opacity: 0.3;
}
.ki-book-open.ki-duotone:after {
  content: "\e9cc";
  
  opacity: 0.3;
}
.ki-book-open.ki-duotone:before {
  content: "\e9cd";
  position: absolute;
  
}
.ki-book-square.ki-duotone:after {
  content: "\e9ce";
  
  opacity: 0.3;
}
.ki-book-square.ki-duotone:before {
  content: "\e9cf";
  position: absolute;
  
}
.ki-book.ki-duotone:after {
  content: "\e9d0";
  
  opacity: 0.3;
}
.ki-book.ki-duotone:before {
  content: "\e9d1";
  position: absolute;
  
}
.ki-bookmark-2.ki-duotone:after {
  content: "\e9d2";
  
  opacity: 0.3;
}
.ki-bookmark-2.ki-duotone:before {
  content: "\e9d3";
  position: absolute;
  
}
.ki-bookmark.ki-duotone:after {
  content: "\e9d4";
  
  opacity: 0.3;
}
.ki-bookmark.ki-duotone:before {
  content: "\e9d5";
  position: absolute;
  
}
.ki-bootstrap.ki-duotone:after {
  content: "\e9d6";
  
  opacity: 0.3;
}
.ki-bootstrap.ki-duotone:before {
  content: "\e9d7";
  position: absolute;
  
}
.ki-briefcase.ki-duotone:after {
  content: "\e9d8";
  
  opacity: 0.3;
}
.ki-briefcase.ki-duotone:before {
  content: "\e9d9";
  position: absolute;
  
}
.ki-brifecase-cros.ki-duotone:after {
  content: "\e9da";
  
  opacity: 0.3;
}
.ki-brifecase-cros.ki-duotone:before {
  content: "\e9db";
  position: absolute;
  
}
.ki-brifecase-tick.ki-duotone:after {
  content: "\e9dc";
  
  opacity: 0.3;
}
.ki-brifecase-tick.ki-duotone:before {
  content: "\e9dd";
  position: absolute;
  
}
.ki-brifecase-timer.ki-duotone:after {
  content: "\e9de";
  
  opacity: 0.3;
}
.ki-brifecase-timer.ki-duotone:before {
  content: "\e9df";
  position: absolute;
  
}
.ki-brush.ki-duotone:after {
  content: "\e9e0";
  
  opacity: 0.3;
}
.ki-brush.ki-duotone:before {
  content: "\e9e1";
  position: absolute;
  
}
.ki-bucket-square.ki-duotone:after {
  content: "\e9e2";
  
  opacity: 0.3;
}
.ki-bucket-square.ki-duotone:before {
  content: "\e9e3";
  position: absolute;
  
}
.ki-bucket.ki-duotone:after {
  content: "\e9e4";
  
  opacity: 0.3;
}
.ki-bucket.ki-duotone:before {
  content: "\e9e5";
  position: absolute;
  
}
.ki-burger-menu-1.ki-duotone:after {
  content: "\e9e6";
  
  opacity: 0.3;
}
.ki-burger-menu-1.ki-duotone:before {
  content: "\e9e7";
  position: absolute;
  
}
.ki-burger-menu-2.ki-duotone:after {
  content: "\e9e8";
  
  opacity: 0.3;
}
.ki-burger-menu-2.ki-duotone:before {
  content: "\e9e9";
  position: absolute;
  
}
.ki-burger-menu-3.ki-duotone:after {
  content: "\e9ea";
  
  opacity: 0.3;
}
.ki-burger-menu-3.ki-duotone:before {
  content: "\e9eb";
  position: absolute;
  
}
.ki-burger-menu-4.ki-duotone:before {
  content: "\e9ec";
}
.ki-burger-menu-5.ki-duotone:before {
  content: "\e9ed";
}
.ki-burger-menu-6.ki-duotone:before {
  content: "\e9ee";
}
.ki-burger-menu.ki-duotone:after {
  content: "\e9ef";
  
  opacity: 0.3;
}
.ki-burger-menu.ki-duotone:before {
  content: "\e9f0";
  position: absolute;
  
}
.ki-bus.ki-duotone:after {
  content: "\e9f1";
  
  opacity: 0.3;
}
.ki-bus.ki-duotone:before {
  content: "\e9f2";
  position: absolute;
  
}
.ki-calculator.ki-duotone:after {
  content: "\e9f3";
  
  opacity: 0.3;
}
.ki-calculator.ki-duotone:before {
  content: "\e9f4";
  position: absolute;
  
}
.ki-calculatoror.ki-duotone:after {
  content: "\e9f5";
  
  opacity: 0.3;
}
.ki-calculatoror.ki-duotone:before {
  content: "\e9f6";
  position: absolute;
  
}
.ki-calendar-2.ki-duotone:after {
  content: "\e9f7";
  
  opacity: 0.3;
}
.ki-calendar-2.ki-duotone:before {
  content: "\e9f8";
  position: absolute;
  
}
.ki-calendar-8.ki-duotone:after {
  content: "\e9f9";
  
  opacity: 0.3;
}
.ki-calendar-8.ki-duotone:before {
  content: "\e9fa";
  position: absolute;
  
}
.ki-calendar-add.ki-duotone:after {
  content: "\e9fb";
  
  opacity: 0.3;
}
.ki-calendar-add.ki-duotone:before {
  content: "\e9fc";
  position: absolute;
  
}
.ki-calendar-edit.ki-duotone:after {
  content: "\e9fd";
  
  opacity: 0.3;
}
.ki-calendar-edit.ki-duotone:before {
  content: "\e9fe";
  position: absolute;
  
}
.ki-calendar-remove.ki-duotone:after {
  content: "\e9ff";
  
  opacity: 0.3;
}
.ki-calendar-remove.ki-duotone:before {
  content: "\ea00";
  position: absolute;
  
}
.ki-calendar-search.ki-duotone:after {
  content: "\ea01";
  
  opacity: 0.3;
}
.ki-calendar-search.ki-duotone:before {
  content: "\ea02";
  position: absolute;
  
}
.ki-calendar-tick.ki-duotone:after {
  content: "\ea03";
  
  opacity: 0.3;
}
.ki-calendar-tick.ki-duotone:before {
  content: "\ea04";
  position: absolute;
  
}
.ki-calendar.ki-duotone:after {
  content: "\ea05";
  
  opacity: 0.3;
}
.ki-calendar.ki-duotone:before {
  content: "\ea06";
  position: absolute;
  
}
.ki-call.ki-duotone:after {
  content: "\ea07";
  
  opacity: 0.3;
}
.ki-call.ki-duotone:before {
  content: "\ea08";
  position: absolute;
  
}
.ki-capsule.ki-duotone:after {
  content: "\ea09";
  
  opacity: 0.3;
}
.ki-capsule.ki-duotone:before {
  content: "\ea0a";
  position: absolute;
  
}
.ki-car.ki-duotone:after {
  content: "\ea0b";
  
  opacity: 0.3;
}
.ki-car.ki-duotone:before {
  content: "\ea0c";
  position: absolute;
  
}
.ki-category.ki-duotone:after {
  content: "\ea0d";
  
  opacity: 0.3;
}
.ki-category.ki-duotone:before {
  content: "\ea0e";
  position: absolute;
  
}
.ki-cd.ki-duotone:after {
  content: "\ea0f";
  
}
.ki-cd.ki-duotone:before {
  content: "\ea10";
  position: absolute;
  
  opacity: 0.3;
}
.ki-celsius-cel.ki-duotone:after {
  content: "\ea11";
  
}
.ki-celsius-cel.ki-duotone:before {
  content: "\ea12";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-line-down-2.ki-duotone:after {
  content: "\ea13";
  
  opacity: 0.3;
}
.ki-chart-line-down-2.ki-duotone:before {
  content: "\ea14";
  position: absolute;
  
}
.ki-chart-line-down.ki-duotone:after {
  content: "\ea15";
  
  opacity: 0.3;
}
.ki-chart-line-down.ki-duotone:before {
  content: "\ea16";
  position: absolute;
  
}
.ki-chart-line-star.ki-duotone:after {
  content: "\ea17";
  
}
.ki-chart-line-star.ki-duotone:before {
  content: "\ea18";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-line-up-2.ki-duotone:after {
  content: "\ea19";
  
}
.ki-chart-line-up-2.ki-duotone:before {
  content: "\ea1a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-line-up.ki-duotone:after {
  content: "\ea1b";
  
}
.ki-chart-line-up.ki-duotone:before {
  content: "\ea1c";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-line.ki-duotone:after {
  content: "\ea1d";
  
  opacity: 0.3;
}
.ki-chart-line.ki-duotone:before {
  content: "\ea1e";
  position: absolute;
  
}
.ki-chart-pie-3.ki-duotone:after {
  content: "\ea1f";
  
}
.ki-chart-pie-3.ki-duotone:before {
  content: "\ea20";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-pie-4.ki-duotone:after {
  content: "\ea21";
  
}
.ki-chart-pie-4.ki-duotone:before {
  content: "\ea22";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-pie-simple.ki-duotone:after {
  content: "\ea23";
  
}
.ki-chart-pie-simple.ki-duotone:before {
  content: "\ea24";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-pie-too.ki-duotone:after {
  content: "\ea25";
  
  opacity: 0.3;
}
.ki-chart-pie-too.ki-duotone:before {
  content: "\ea26";
  position: absolute;
  
}
.ki-chart-simple-2.ki-duotone:after {
  content: "\ea27";
  
}
.ki-chart-simple-2.ki-duotone:before {
  content: "\ea28";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-simple-3.ki-duotone:after {
  content: "\ea29";
  
}
.ki-chart-simple-3.ki-duotone:before {
  content: "\ea2a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart-simple.ki-duotone:after {
  content: "\ea2b";
  
}
.ki-chart-simple.ki-duotone:before {
  content: "\ea2c";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chart.ki-duotone:after {
  content: "\ea2d";
  
}
.ki-chart.ki-duotone:before {
  content: "\ea2e";
  position: absolute;
  
  opacity: 0.3;
}
.ki-check-circle.ki-duotone:after {
  content: "\ea2f";
  
  opacity: 0.3;
}
.ki-check-circle.ki-duotone:before {
  content: "\ea30";
  position: absolute;
  
}
.ki-check-squared.ki-duotone:after {
  content: "\ea31";
  
  opacity: 0.3;
}
.ki-check-squared.ki-duotone:before {
  content: "\ea32";
  position: absolute;
  
}
.ki-check.ki-duotone:before {
  content: "\ea33";
}
.ki-cheque.ki-duotone:after {
  content: "\ea34";
  
}
.ki-cheque.ki-duotone:before {
  content: "\ea35";
  position: absolute;
  
  opacity: 0.3;
}
.ki-chrome.ki-duotone:after {
  content: "\ea36";
  
  opacity: 0.3;
}
.ki-chrome.ki-duotone:before {
  content: "\ea37";
  position: absolute;
  
}
.ki-classmates.ki-duotone:after {
  content: "\ea38";
  
}
.ki-classmates.ki-duotone:before {
  content: "\ea39";
  position: absolute;
  
  opacity: 0.3;
}
.ki-click.ki-duotone:after {
  content: "\ea3a";
  
}
.ki-click.ki-duotone:before {
  content: "\ea3b";
  position: absolute;
  
  opacity: 0.3;
}
.ki-clipboard.ki-duotone:after {
  content: "\ea3c";
  
  opacity: 0.3;
}
.ki-clipboard.ki-duotone:before {
  content: "\ea3d";
  position: absolute;
  
}
.ki-cloud-add.ki-duotone:after {
  content: "\ea3e";
  
}
.ki-cloud-add.ki-duotone:before {
  content: "\ea3f";
  position: absolute;
  
  opacity: 0.3;
}
.ki-cloud-change.ki-duotone:after {
  content: "\ea40";
  
}
.ki-cloud-change.ki-duotone:before {
  content: "\ea41";
  position: absolute;
  
  opacity: 0.3;
}
.ki-cloud-download.ki-duotone:after {
  content: "\ea42";
  
  opacity: 0.3;
}
.ki-cloud-download.ki-duotone:before {
  content: "\ea43";
  position: absolute;
  
}
.ki-cloud.ki-duotone:before {
  content: "\ea44";
}
.ki-code.ki-duotone:after {
  content: "\ea45";
  
  opacity: 0.3;
}
.ki-code.ki-duotone:before {
  content: "\ea46";
  position: absolute;
  
}
.ki-coffee.ki-duotone:after {
  content: "\ea47";
  
  opacity: 0.3;
}
.ki-coffee.ki-duotone:before {
  content: "\ea48";
  position: absolute;
  
}
.ki-color-swatch.ki-duotone:after {
  content: "\ea49";
  
  opacity: 0.3;
}
.ki-color-swatch.ki-duotone:before {
  content: "\ea4a";
  position: absolute;
  
}
.ki-colors-square.ki-duotone:after {
  content: "\ea4b";
  
  opacity: 0.3;
}
.ki-colors-square.ki-duotone:before {
  content: "\ea4c";
  position: absolute;
  
}
.ki-compass.ki-duotone:after {
  content: "\ea4d";
  
}
.ki-compass.ki-duotone:before {
  content: "\ea4e";
  position: absolute;
  
  opacity: 0.3;
}
.ki-copy-success.ki-duotone:after {
  content: "\ea4f";
  
}
.ki-copy-success.ki-duotone:before {
  content: "\ea50";
  position: absolute;
  
  opacity: 0.3;
}
.ki-copy.ki-duotone:before {
  content: "\ea51";
}
.ki-courier-express.ki-duotone:after {
  content: "\ea52";
  
}
.ki-courier-express.ki-duotone:before {
  content: "\ea53";
  position: absolute;
  
  opacity: 0.3;
}
.ki-courier.ki-duotone:after {
  content: "\ea54";
  
  opacity: 0.3;
}
.ki-courier.ki-duotone:before {
  content: "\ea55";
  position: absolute;
  
}
.ki-credit-cart.ki-duotone:after {
  content: "\ea56";
  
  opacity: 0.3;
}
.ki-credit-cart.ki-duotone:before {
  content: "\ea57";
  position: absolute;
  
}
.ki-cross-circle.ki-duotone:after {
  content: "\ea58";
  
  opacity: 0.3;
}
.ki-cross-circle.ki-duotone:before {
  content: "\ea59";
  position: absolute;
  
}
.ki-cross-square.ki-duotone:after {
  content: "\ea5a";
  
  opacity: 0.3;
}
.ki-cross-square.ki-duotone:before {
  content: "\ea5b";
  position: absolute;
  
}
.ki-cross.ki-duotone:before {
  content: "\ea5c";
}
.ki-crown-2.ki-duotone:after {
  content: "\ea5d";
  
  opacity: 0.3;
}
.ki-crown-2.ki-duotone:before {
  content: "\ea5e";
  position: absolute;
  
}
.ki-crown.ki-duotone:after {
  content: "\ea5f";
  
  opacity: 0.3;
}
.ki-crown.ki-duotone:before {
  content: "\ea60";
  position: absolute;
  
}
.ki-css.ki-duotone:after {
  content: "\ea61";
  
  opacity: 0.3;
}
.ki-css.ki-duotone:before {
  content: "\ea62";
  position: absolute;
  
}
.ki-cube-2.ki-duotone:after {
  content: "\ea63";
  
  opacity: 0.3;
}
.ki-cube-2.ki-duotone:before {
  content: "\ea64";
  position: absolute;
  
}
.ki-cube-3.ki-duotone:after {
  content: "\ea65";
  
  opacity: 0.3;
}
.ki-cube-3.ki-duotone:before {
  content: "\ea66";
  position: absolute;
  
}
.ki-cup.ki-duotone:after {
  content: "\ea67";
  
  opacity: 0.3;
}
.ki-cup.ki-duotone:before {
  content: "\ea68";
  position: absolute;
  
}
.ki-cursor.ki-duotone:after {
  content: "\ea69";
  
  opacity: 0.3;
}
.ki-cursor.ki-duotone:before {
  content: "\ea6a";
  position: absolute;
  
}
.ki-dash.ki-duotone:after {
  content: "\ea6b";
  
  opacity: 0.3;
}
.ki-dash.ki-duotone:before {
  content: "\ea6c";
  position: absolute;
  
}
.ki-data.ki-duotone:after {
  content: "\ea6d";
  
}
.ki-data.ki-duotone:before {
  content: "\ea6e";
  position: absolute;
  
  opacity: 0.3;
}
.ki-delete-files.ki-duotone:after {
  content: "\ea6f";
  
  opacity: 0.3;
}
.ki-delete-files.ki-duotone:before {
  content: "\ea70";
  position: absolute;
  
}
.ki-delete-folder.ki-duotone:after {
  content: "\ea71";
  
  opacity: 0.3;
}
.ki-delete-folder.ki-duotone:before {
  content: "\ea72";
  position: absolute;
  
}
.ki-delivery-2.ki-duotone:after {
  content: "\ea73";
  
  opacity: 0.3;
}
.ki-delivery-2.ki-duotone:before {
  content: "\ea74";
  position: absolute;
  
}
.ki-delivery-3.ki-duotone:after {
  content: "\ea75";
  
  opacity: 0.3;
}
.ki-delivery-3.ki-duotone:before {
  content: "\ea76";
  position: absolute;
  
}
.ki-delivery-24.ki-duotone:after {
  content: "\ea77";
  
  opacity: 0.3;
}
.ki-delivery-24.ki-duotone:before {
  content: "\ea78";
  position: absolute;
  
}
.ki-delivery-door.ki-duotone:after {
  content: "\ea79";
  
  opacity: 0.3;
}
.ki-delivery-door.ki-duotone:before {
  content: "\ea7a";
  position: absolute;
  
}
.ki-delivery-geolocation.ki-duotone:after {
  content: "\ea7b";
  
  opacity: 0.3;
}
.ki-delivery-geolocation.ki-duotone:before {
  content: "\ea7c";
  position: absolute;
  
}
.ki-delivery-time.ki-duotone:after {
  content: "\ea7d";
  
  opacity: 0.3;
}
.ki-delivery-time.ki-duotone:before {
  content: "\ea7e";
  position: absolute;
  
}
.ki-delivery.ki-duotone:after {
  content: "\ea7f";
  
  opacity: 0.3;
}
.ki-delivery.ki-duotone:before {
  content: "\ea80";
  position: absolute;
  
}
.ki-design-1.ki-duotone:after {
  content: "\ea81";
  
  opacity: 0.3;
}
.ki-design-1.ki-duotone:before {
  content: "\ea82";
  position: absolute;
  
}
.ki-design-2.ki-duotone:after {
  content: "\ea83";
  
  opacity: 0.3;
}
.ki-design-2.ki-duotone:before {
  content: "\ea84";
  position: absolute;
  
}
.ki-desktop-mobile.ki-duotone:after {
  content: "\ea85";
  
  opacity: 0.3;
}
.ki-desktop-mobile.ki-duotone:before {
  content: "\ea86";
  position: absolute;
  
}
.ki-devices-2.ki-duotone:after {
  content: "\ea87";
  
  opacity: 0.3;
}
.ki-devices-2.ki-duotone:before {
  content: "\ea88";
  position: absolute;
  
}
.ki-devices.ki-duotone:after {
  content: "\ea89";
  
  opacity: 0.3;
}
.ki-devices.ki-duotone:before {
  content: "\ea8a";
  position: absolute;
  
}
.ki-diamonds.ki-duotone:after {
  content: "\ea8b";
  
}
.ki-diamonds.ki-duotone:before {
  content: "\ea8c";
  position: absolute;
  
  opacity: 0.3;
}
.ki-directbox-default.ki-duotone:after {
  content: "\ea8d";
  
  opacity: 0.3;
}
.ki-directbox-default.ki-duotone:before {
  content: "\ea8e";
  position: absolute;
  
}
.ki-disconnect.ki-duotone:after {
  content: "\ea8f";
  
}
.ki-disconnect.ki-duotone:before {
  content: "\ea90";
  position: absolute;
  
  opacity: 0.3;
}
.ki-discount.ki-duotone:after {
  content: "\ea91";
  
  opacity: 0.3;
}
.ki-discount.ki-duotone:before {
  content: "\ea92";
  position: absolute;
  
}
.ki-disguise.ki-duotone:after {
  content: "\ea93";
  
  opacity: 0.3;
}
.ki-disguise.ki-duotone:before {
  content: "\ea94";
  position: absolute;
  
}
.ki-disk.ki-duotone:after {
  content: "\ea95";
  
  opacity: 0.3;
}
.ki-disk.ki-duotone:before {
  content: "\ea96";
  position: absolute;
  
}
.ki-dislike.ki-duotone:after {
  content: "\ea97";
  
}
.ki-dislike.ki-duotone:before {
  content: "\ea98";
  position: absolute;
  
  opacity: 0.3;
}
.ki-dj.ki-duotone:before {
  content: "\ea99";
}
.ki-document.ki-duotone:after {
  content: "\ea9a";
  
  opacity: 0.3;
}
.ki-document.ki-duotone:before {
  content: "\ea9b";
  position: absolute;
  
}
.ki-double-check.ki-duotone:after {
  content: "\ea9c";
  
}
.ki-double-check.ki-duotone:before {
  content: "\ea9d";
  position: absolute;
  
  opacity: 0.3;
}
.ki-dollar.ki-duotone:after {
  content: "\ea9e";
  
  opacity: 0.3;
}
.ki-dollar.ki-duotone:before {
  content: "\ea9f";
  position: absolute;
  
}
.ki-dots-circle-vertical.ki-duotone:after {
  content: "\eaa0";
  
  opacity: 0.3;
}
.ki-dots-circle-vertical.ki-duotone:before {
  content: "\eaa1";
  position: absolute;
  
}
.ki-dots-circle.ki-duotone:after {
  content: "\eaa2";
  
  opacity: 0.3;
}
.ki-dots-circle.ki-duotone:before {
  content: "\eaa3";
  position: absolute;
  
}
.ki-dots-horizontal.ki-duotone:before {
  content: "\eaa4";
}
.ki-dots-square-vertical.ki-duotone:after {
  content: "\eaa5";
  
  opacity: 0.3;
}
.ki-dots-square-vertical.ki-duotone:before {
  content: "\eaa6";
  position: absolute;
  
}
.ki-dots-square.ki-duotone:after {
  content: "\eaa7";
  
  opacity: 0.3;
}
.ki-dots-square.ki-duotone:before {
  content: "\eaa8";
  position: absolute;
  
}
.ki-dots-vertical.ki-duotone:after {
  content: "\eaa9";
  
}
.ki-dots-vertical.ki-duotone:before {
  content: "\eaaa";
  position: absolute;
  
  opacity: 0.3;
}
.ki-double-check-circle.ki-duotone:after {
  content: "\eaab";
  
  opacity: 0.3;
}
.ki-double-check-circle.ki-duotone:before {
  content: "\eaac";
  position: absolute;
  
}
.ki-double-down.ki-duotone:after {
  content: "\eaad";
  
}
.ki-double-down.ki-duotone:before {
  content: "\eaae";
  position: absolute;
  
  opacity: 0.3;
}
.ki-double-left-arrow.ki-duotone:after {
  content: "\eaaf";
  
  opacity: 0.3;
}
.ki-double-left-arrow.ki-duotone:before {
  content: "\eab0";
  position: absolute;
  
}
.ki-double-left.ki-duotone:after {
  content: "\eab1";
  
}
.ki-double-left.ki-duotone:before {
  content: "\eab2";
  position: absolute;
  
  opacity: 0.3;
}
.ki-double-right-arrow.ki-duotone:after {
  content: "\eab3";
  
  opacity: 0.3;
}
.ki-double-right-arrow.ki-duotone:before {
  content: "\eab4";
  position: absolute;
  
}
.ki-double-right.ki-duotone:after {
  content: "\eab5";
  
}
.ki-double-right.ki-duotone:before {
  content: "\eab6";
  position: absolute;
  
  opacity: 0.3;
}
.ki-double-up.ki-duotone:after {
  content: "\eab7";
  
}
.ki-double-up.ki-duotone:before {
  content: "\eab8";
  position: absolute;
  
  opacity: 0.3;
}
.ki-down-square.ki-duotone:after {
  content: "\eab9";
  
  opacity: 0.3;
}
.ki-down-square.ki-duotone:before {
  content: "\eaba";
  position: absolute;
  
}
.ki-down.ki-duotone:before {
  content: "\eabb";
}
.ki-dribbble.ki-duotone:after {
  content: "\eabc";
  
}
.ki-dribbble.ki-duotone:before {
  content: "\eabd";
  position: absolute;
  
  opacity: 0.3;
}
.ki-drop.ki-duotone:after {
  content: "\eabe";
  
}
.ki-drop.ki-duotone:before {
  content: "\eabf";
  position: absolute;
  
  opacity: 0.3;
}
.ki-dropbox.ki-duotone:after {
  content: "\eac0";
  
  opacity: 0.3;
}
.ki-dropbox.ki-duotone:before {
  content: "\eac1";
  position: absolute;
  
}
.ki-educare-ekt.ki-duotone:after {
  content: "\eac2";
  
  opacity: 0.3;
}
.ki-educare-ekt.ki-duotone:before {
  content: "\eac3";
  position: absolute;
  
}
.ki-electricity.ki-duotone:after {
  content: "\eac4";
  
  opacity: 0.3;
}
.ki-electricity.ki-duotone:before {
  content: "\eac5";
  position: absolute;
  
}
.ki-electronic-clock.ki-duotone:after {
  content: "\eac6";
  
  opacity: 0.3;
}
.ki-electronic-clock.ki-duotone:before {
  content: "\eac7";
  position: absolute;
  
}
.ki-element-1.ki-duotone:after {
  content: "\eac8";
  
  opacity: 0.3;
}
.ki-element-1.ki-duotone:before {
  content: "\eac9";
  position: absolute;
  
}
.ki-element-2.ki-duotone:after {
  content: "\eaca";
  
}
.ki-element-2.ki-duotone:before {
  content: "\eacb";
  position: absolute;
  
  opacity: 0.3;
}
.ki-element-3.ki-duotone:after {
  content: "\eacc";
  
  opacity: 0.3;
}
.ki-element-3.ki-duotone:before {
  content: "\eacd";
  position: absolute;
  
}
.ki-element-4.ki-duotone:after {
  content: "\eace";
  
}
.ki-element-4.ki-duotone:before {
  content: "\eacf";
  position: absolute;
  
  opacity: 0.3;
}
.ki-element-5.ki-duotone:after {
  content: "\ead0";
  
}
.ki-element-5.ki-duotone:before {
  content: "\ead1";
  position: absolute;
  
  opacity: 0.3;
}
.ki-element-6.ki-duotone:after {
  content: "\ead2";
  
  opacity: 0.3;
}
.ki-element-6.ki-duotone:before {
  content: "\ead3";
  position: absolute;
  
}
.ki-element-7.ki-duotone:after {
  content: "\ead4";
  
}
.ki-element-7.ki-duotone:before {
  content: "\ead5";
  position: absolute;
  
  opacity: 0.3;
}
.ki-element-8.ki-duotone:after {
  content: "\ead6";
  
  opacity: 0.3;
}
.ki-element-8.ki-duotone:before {
  content: "\ead7";
  position: absolute;
  
}
.ki-element-9.ki-duotone:after {
  content: "\ead8";
  
  opacity: 0.3;
}
.ki-element-9.ki-duotone:before {
  content: "\ead9";
  position: absolute;
  
}
.ki-element-10.ki-duotone:after {
  content: "\eada";
  
}
.ki-element-10.ki-duotone:before {
  content: "\eadb";
  position: absolute;
  
  opacity: 0.3;
}
.ki-element-11.ki-duotone:after {
  content: "\eadc";
  
}
.ki-element-11.ki-duotone:before {
  content: "\eadd";
  position: absolute;
  
  opacity: 0.3;
}
.ki-element-12.ki-duotone:after {
  content: "\eade";
  
  opacity: 0.3;
}
.ki-element-12.ki-duotone:before {
  content: "\eadf";
  position: absolute;
  
}
.ki-element-equal.ki-duotone:after {
  content: "\eae0";
  
  opacity: 0.3;
}
.ki-element-equal.ki-duotone:before {
  content: "\eae1";
  position: absolute;
  
}
.ki-element-plus.ki-duotone:after {
  content: "\eae2";
  
}
.ki-element-plus.ki-duotone:before {
  content: "\eae3";
  position: absolute;
  
  opacity: 0.3;
}
.ki-emoji-happy.ki-duotone:after {
  content: "\eae4";
  
  opacity: 0.3;
}
.ki-emoji-happy.ki-duotone:before {
  content: "\eae5";
  position: absolute;
  
}
.ki-enjin-coin-enj.ki-duotone:after {
  content: "\eae6";
  
}
.ki-enjin-coin-enj.ki-duotone:before {
  content: "\eae7";
  position: absolute;
  
  opacity: 0.3;
}
.ki-ensure.ki-duotone:after {
  content: "\eae8";
  
}
.ki-ensure.ki-duotone:before {
  content: "\eae9";
  position: absolute;
  
  opacity: 0.3;
}
.ki-entrance-left.ki-duotone:after {
  content: "\eaea";
  
}
.ki-entrance-left.ki-duotone:before {
  content: "\eaeb";
  position: absolute;
  
  opacity: 0.3;
}
.ki-entrance-right.ki-duotone:after {
  content: "\eaec";
  
  opacity: 0.3;
}
.ki-entrance-right.ki-duotone:before {
  content: "\eaed";
  position: absolute;
  
}
.ki-eraser.ki-duotone:after {
  content: "\eaee";
  
  opacity: 0.3;
}
.ki-eraser.ki-duotone:before {
  content: "\eaef";
  position: absolute;
  
}
.ki-euro.ki-duotone:after {
  content: "\eaf0";
  
  opacity: 0.3;
}
.ki-euro.ki-duotone:before {
  content: "\eaf1";
  position: absolute;
  
}
.ki-exit-down.ki-duotone:after {
  content: "\eaf2";
  
  opacity: 0.3;
}
.ki-exit-down.ki-duotone:before {
  content: "\eaf3";
  position: absolute;
  
}
.ki-exit-left.ki-duotone:after {
  content: "\eaf4";
  
  opacity: 0.3;
}
.ki-exit-left.ki-duotone:before {
  content: "\eaf5";
  position: absolute;
  
}
.ki-exit-right-corner.ki-duotone:after {
  content: "\eaf6";
  
  opacity: 0.3;
}
.ki-exit-right-corner.ki-duotone:before {
  content: "\eaf7";
  position: absolute;
  
}
.ki-exit-right.ki-duotone:after {
  content: "\eaf8";
  
  opacity: 0.3;
}
.ki-exit-right.ki-duotone:before {
  content: "\eaf9";
  position: absolute;
  
}
.ki-exit-up.ki-duotone:after {
  content: "\eafa";
  
  opacity: 0.3;
}
.ki-exit-up.ki-duotone:before {
  content: "\eafb";
  position: absolute;
  
}
.ki-external-drive.ki-duotone:after {
  content: "\eafc";
  
}
.ki-external-drive.ki-duotone:before {
  content: "\eafd";
  position: absolute;
  
  opacity: 0.3;
}
.ki-eye-slash.ki-duotone:after {
  content: "\eafe";
  
}
.ki-eye-slash.ki-duotone:before {
  content: "\eaff";
  position: absolute;
  
  opacity: 0.3;
}
.ki-eye.ki-duotone:after {
  content: "\eb00";
  
}
.ki-eye.ki-duotone:before {
  content: "\eb01";
  position: absolute;
  
  opacity: 0.3;
}
.ki-face-id.ki-duotone:after {
  content: "\eb02";
  
  opacity: 0.3;
}
.ki-face-id.ki-duotone:before {
  content: "\eb03";
  position: absolute;
  
}
.ki-facebook.ki-duotone:after {
  content: "\eb04";
  
  opacity: 0.3;
}
.ki-facebook.ki-duotone:before {
  content: "\eb05";
  position: absolute;
  
}
.ki-fasten.ki-duotone:after {
  content: "\eb06";
  
  opacity: 0.3;
}
.ki-fasten.ki-duotone:before {
  content: "\eb07";
  position: absolute;
  
}
.ki-fatrows.ki-duotone:after {
  content: "\eb08";
  
}
.ki-fatrows.ki-duotone:before {
  content: "\eb09";
  position: absolute;
  
  opacity: 0.3;
}
.ki-feather.ki-duotone:after {
  content: "\eb0a";
  
  opacity: 0.3;
}
.ki-feather.ki-duotone:before {
  content: "\eb0b";
  position: absolute;
  
}
.ki-figma.ki-duotone:after {
  content: "\eb0c";
  
  opacity: 0.3;
}
.ki-figma.ki-duotone:before {
  content: "\eb0d";
  position: absolute;
  
}
.ki-file-added.ki-duotone:after {
  content: "\eb0e";
  
}
.ki-file-added.ki-duotone:before {
  content: "\eb0f";
  position: absolute;
  
  opacity: 0.3;
}
.ki-file-deleted.ki-duotone:after {
  content: "\eb10";
  
}
.ki-file-deleted.ki-duotone:before {
  content: "\eb11";
  position: absolute;
  
  opacity: 0.3;
}
.ki-file-down.ki-duotone:after {
  content: "\eb12";
  
  opacity: 0.3;
}
.ki-file-down.ki-duotone:before {
  content: "\eb13";
  position: absolute;
  
}
.ki-file-left.ki-duotone:after {
  content: "\eb14";
  
  opacity: 0.3;
}
.ki-file-left.ki-duotone:before {
  content: "\eb15";
  position: absolute;
  
}
.ki-file-right.ki-duotone:after {
  content: "\eb16";
  
  opacity: 0.3;
}
.ki-file-right.ki-duotone:before {
  content: "\eb17";
  position: absolute;
  
}
.ki-file-sheet.ki-duotone:after {
  content: "\eb18";
  
}
.ki-file-sheet.ki-duotone:before {
  content: "\eb19";
  position: absolute;
  
  opacity: 0.3;
}
.ki-file-up.ki-duotone:after {
  content: "\eb1a";
  
  opacity: 0.3;
}
.ki-file-up.ki-duotone:before {
  content: "\eb1b";
  position: absolute;
  
}
.ki-files.ki-duotone:after {
  content: "\eb1c";
  
}
.ki-files.ki-duotone:before {
  content: "\eb1d";
  position: absolute;
  
  opacity: 0.3;
}
.ki-filter-edit.ki-duotone:after {
  content: "\eb1e";
  
  opacity: 0.3;
}
.ki-filter-edit.ki-duotone:before {
  content: "\eb1f";
  position: absolute;
  
}
.ki-filter-search.ki-duotone:after {
  content: "\eb20";
  
  opacity: 0.3;
}
.ki-filter-search.ki-duotone:before {
  content: "\eb21";
  position: absolute;
  
}
.ki-filter-square.ki-duotone:after {
  content: "\eb22";
  
}
.ki-filter-square.ki-duotone:before {
  content: "\eb23";
  position: absolute;
  
  opacity: 0.3;
}
.ki-filter-tablet.ki-duotone:after {
  content: "\eb24";
  
  opacity: 0.3;
}
.ki-filter-tablet.ki-duotone:before {
  content: "\eb25";
  position: absolute;
  
}
.ki-filter-tick.ki-duotone:after {
  content: "\eb26";
  
  opacity: 0.3;
}
.ki-filter-tick.ki-duotone:before {
  content: "\eb27";
  position: absolute;
  
}
.ki-filter.ki-duotone:after {
  content: "\eb28";
  
}
.ki-filter.ki-duotone:before {
  content: "\eb29";
  position: absolute;
  
  opacity: 0.3;
}
.ki-financial-schedule.ki-duotone:after {
  content: "\eb2a";
  
  opacity: 0.3;
}
.ki-financial-schedule.ki-duotone:before {
  content: "\eb2b";
  position: absolute;
  
}
.ki-fingerprint-scanning.ki-duotone:after {
  content: "\eb2c";
  
}
.ki-fingerprint-scanning.ki-duotone:before {
  content: "\eb2d";
  position: absolute;
  
  opacity: 0.3;
}
.ki-flag.ki-duotone:after {
  content: "\eb2e";
  
  opacity: 0.3;
}
.ki-flag.ki-duotone:before {
  content: "\eb2f";
  position: absolute;
  
}
.ki-flash-circle.ki-duotone:after {
  content: "\eb30";
  
  opacity: 0.3;
}
.ki-flash-circle.ki-duotone:before {
  content: "\eb31";
  position: absolute;
  
}
.ki-flask.ki-duotone:after {
  content: "\eb32";
  
  opacity: 0.3;
}
.ki-flask.ki-duotone:before {
  content: "\eb33";
  position: absolute;
  
}
.ki-focus.ki-duotone:after {
  content: "\eb34";
  
  opacity: 0.3;
}
.ki-focus.ki-duotone:before {
  content: "\eb35";
  position: absolute;
  
}
.ki-folder-added.ki-duotone:after {
  content: "\eb36";
  
  opacity: 0.3;
}
.ki-folder-added.ki-duotone:before {
  content: "\eb37";
  position: absolute;
  
}
.ki-folder-down.ki-duotone:after {
  content: "\eb38";
  
  opacity: 0.3;
}
.ki-folder-down.ki-duotone:before {
  content: "\eb39";
  position: absolute;
  
}
.ki-folder-up.ki-duotone:after {
  content: "\eb3a";
  
  opacity: 0.3;
}
.ki-folder-up.ki-duotone:before {
  content: "\eb3b";
  position: absolute;
  
}
.ki-folder.ki-duotone:after {
  content: "\eb3c";
  
  opacity: 0.3;
}
.ki-folder.ki-duotone:before {
  content: "\eb3d";
  position: absolute;
  
}
.ki-frame.ki-duotone:after {
  content: "\eb3e";
  
  opacity: 0.3;
}
.ki-frame.ki-duotone:before {
  content: "\eb3f";
  position: absolute;
  
}
.ki-geolocation-home.ki-duotone:after {
  content: "\eb40";
  
  opacity: 0.3;
}
.ki-geolocation-home.ki-duotone:before {
  content: "\eb41";
  position: absolute;
  
}
.ki-geolocation.ki-duotone:after {
  content: "\eb42";
  
  opacity: 0.3;
}
.ki-geolocation.ki-duotone:before {
  content: "\eb43";
  position: absolute;
  
}
.ki-ghost.ki-duotone:after {
  content: "\eb44";
  
}
.ki-ghost.ki-duotone:before {
  content: "\eb45";
  position: absolute;
  
  opacity: 0.3;
}
.ki-gift.ki-duotone:after {
  content: "\eb46";
  
}
.ki-gift.ki-duotone:before {
  content: "\eb47";
  position: absolute;
  
  opacity: 0.3;
}
.ki-github.ki-duotone:after {
  content: "\eb48";
  
  opacity: 0.3;
}
.ki-github.ki-duotone:before {
  content: "\eb49";
  position: absolute;
  
}
.ki-glass.ki-duotone:after {
  content: "\eb4a";
  
  opacity: 0.3;
}
.ki-glass.ki-duotone:before {
  content: "\eb4b";
  position: absolute;
  
}
.ki-google-play.ki-duotone:after {
  content: "\eb4c";
  
  opacity: 0.3;
}
.ki-google-play.ki-duotone:before {
  content: "\eb4d";
  position: absolute;
  
}
.ki-google.ki-duotone:after {
  content: "\eb4e";
  
  opacity: 0.3;
}
.ki-google.ki-duotone:before {
  content: "\eb4f";
  position: absolute;
  
}
.ki-graph-2.ki-duotone:after {
  content: "\eb50";
  
  opacity: 0.3;
}
.ki-graph-2.ki-duotone:before {
  content: "\eb51";
  position: absolute;
  
}
.ki-graph-3.ki-duotone:after {
  content: "\eb52";
  
  opacity: 0.3;
}
.ki-graph-3.ki-duotone:before {
  content: "\eb53";
  position: absolute;
  
}
.ki-graph-4.ki-duotone:after {
  content: "\eb54";
  
}
.ki-graph-4.ki-duotone:before {
  content: "\eb55";
  position: absolute;
  
  opacity: 0.3;
}
.ki-graph-up.ki-duotone:after {
  content: "\eb56";
  
  opacity: 0.3;
}
.ki-graph-up.ki-duotone:before {
  content: "\eb57";
  position: absolute;
  
}
.ki-graph.ki-duotone:after {
  content: "\eb58";
  
  opacity: 0.3;
}
.ki-graph.ki-duotone:before {
  content: "\eb59";
  position: absolute;
  
}
.ki-grid-2.ki-duotone:after {
  content: "\eb5a";
  
  opacity: 0.3;
}
.ki-grid-2.ki-duotone:before {
  content: "\eb5b";
  position: absolute;
  
}
.ki-grid.ki-duotone:after {
  content: "\eb5c";
  
  opacity: 0.3;
}
.ki-grid.ki-duotone:before {
  content: "\eb5d";
  position: absolute;
  
}
.ki-handcart.ki-duotone:before {
  content: "\eb5e";
}
.ki-happyemoji.ki-duotone:after {
  content: "\eb5f";
  
  opacity: 0.3;
}
.ki-happyemoji.ki-duotone:before {
  content: "\eb60";
  position: absolute;
  
}
.ki-heart-circle.ki-duotone:after {
  content: "\eb61";
  
  opacity: 0.3;
}
.ki-heart-circle.ki-duotone:before {
  content: "\eb62";
  position: absolute;
  
}
.ki-heart.ki-duotone:after {
  content: "\eb63";
  
  opacity: 0.3;
}
.ki-heart.ki-duotone:before {
  content: "\eb64";
  position: absolute;
  
}
.ki-home-1.ki-duotone:after {
  content: "\eb65";
  
  opacity: 0.3;
}
.ki-home-1.ki-duotone:before {
  content: "\eb66";
  position: absolute;
  
}
.ki-home-2.ki-duotone:after {
  content: "\eb67";
  
  opacity: 0.3;
}
.ki-home-2.ki-duotone:before {
  content: "\eb68";
  position: absolute;
  
}
.ki-home-3.ki-duotone:after {
  content: "\eb69";
  
  opacity: 0.3;
}
.ki-home-3.ki-duotone:before {
  content: "\eb6a";
  position: absolute;
  
}
.ki-home.ki-duotone:before {
  content: "\eb6b";
}
.ki-html.ki-duotone:after {
  content: "\eb6c";
  
  opacity: 0.3;
}
.ki-html.ki-duotone:before {
  content: "\eb6d";
  position: absolute;
  
}
.ki-icon.ki-duotone:after {
  content: "\eb6e";
  
  opacity: 0.3;
}
.ki-icon.ki-duotone:before {
  content: "\eb6f";
  position: absolute;
  
}
.ki-illustrator.ki-duotone:after {
  content: "\eb70";
  
  opacity: 0.3;
}
.ki-illustrator.ki-duotone:before {
  content: "\eb71";
  position: absolute;
  
}
.ki-information-1.ki-duotone:after {
  content: "\eb72";
  
  opacity: 0.3;
}
.ki-information-1.ki-duotone:before {
  content: "\eb73";
  position: absolute;
  
}
.ki-information-2.ki-duotone:after {
  content: "\eb74";
  
  opacity: 0.3;
}
.ki-information-2.ki-duotone:before {
  content: "\eb75";
  position: absolute;
  
}
.ki-information-3.ki-duotone:after {
  content: "\eb76";
  
  opacity: 0.3;
}
.ki-information-3.ki-duotone:before {
  content: "\eb77";
  position: absolute;
  
}
.ki-information-4.ki-duotone:after {
  content: "\eb78";
  
  opacity: 0.3;
}
.ki-information-4.ki-duotone:before {
  content: "\eb79";
  position: absolute;
  
}
.ki-information.ki-duotone:after {
  content: "\eb7a";
  
  opacity: 0.3;
}
.ki-information.ki-duotone:before {
  content: "\eb7b";
  position: absolute;
  
}
.ki-instagram.ki-duotone:after {
  content: "\eb7c";
  
  opacity: 0.3;
}
.ki-instagram.ki-duotone:before {
  content: "\eb7d";
  position: absolute;
  
}
.ki-joystick.ki-duotone:after {
  content: "\eb7e";
  
}
.ki-joystick.ki-duotone:before {
  content: "\eb7f";
  position: absolute;
  
  opacity: 0.3;
}
.ki-js-2.ki-duotone:after {
  content: "\eb80";
  
}
.ki-js-2.ki-duotone:before {
  content: "\eb81";
  position: absolute;
  
  opacity: 0.3;
}
.ki-js.ki-duotone:after {
  content: "\eb82";
  
}
.ki-js.ki-duotone:before {
  content: "\eb83";
  position: absolute;
  
  opacity: 0.3;
}
.ki-kanban.ki-duotone:after {
  content: "\eb84";
  
}
.ki-kanban.ki-duotone:before {
  content: "\eb85";
  position: absolute;
  
  opacity: 0.3;
}
.ki-key-square.ki-duotone:after {
  content: "\eb86";
  
  opacity: 0.3;
}
.ki-key-square.ki-duotone:before {
  content: "\eb87";
  position: absolute;
  
}
.ki-key.ki-duotone:after {
  content: "\eb88";
  
  opacity: 0.3;
}
.ki-key.ki-duotone:before {
  content: "\eb89";
  position: absolute;
  
}
.ki-keyboard.ki-duotone:after {
  content: "\eb8a";
  
}
.ki-keyboard.ki-duotone:before {
  content: "\eb8b";
  position: absolute;
  
  opacity: 0.3;
}
.ki-laptop.ki-duotone:after {
  content: "\eb8c";
  
  opacity: 0.3;
}
.ki-laptop.ki-duotone:before {
  content: "\eb8d";
  position: absolute;
  
}
.ki-laravel.ki-duotone:after {
  content: "\eb8e";
  
  opacity: 0.3;
}
.ki-laravel.ki-duotone:before {
  content: "\eb8f";
  position: absolute;
  
}
.ki-left-square.ki-duotone:after {
  content: "\eb90";
  
  opacity: 0.3;
}
.ki-left-square.ki-duotone:before {
  content: "\eb91";
  position: absolute;
  
}
.ki-left.ki-duotone:before {
  content: "\eb92";
}
.ki-like-2.ki-duotone:after {
  content: "\eb93";
  
  opacity: 0.3;
}
.ki-like-2.ki-duotone:before {
  content: "\eb94";
  position: absolute;
  
}
.ki-like-folder.ki-duotone:after {
  content: "\eb95";
  
  opacity: 0.3;
}
.ki-like-folder.ki-duotone:before {
  content: "\eb96";
  position: absolute;
  
}
.ki-like-shapes.ki-duotone:after {
  content: "\eb97";
  
  opacity: 0.3;
}
.ki-like-shapes.ki-duotone:before {
  content: "\eb98";
  position: absolute;
  
}
.ki-like-tag.ki-duotone:after {
  content: "\eb99";
  
  opacity: 0.3;
}
.ki-like-tag.ki-duotone:before {
  content: "\eb9a";
  position: absolute;
  
}
.ki-like.ki-duotone:after {
  content: "\eb9b";
  
}
.ki-like.ki-duotone:before {
  content: "\eb9c";
  position: absolute;
  
  opacity: 0.3;
}
.ki-loading.ki-duotone:after {
  content: "\eb9d";
  
}
.ki-loading.ki-duotone:before {
  content: "\eb9e";
  position: absolute;
  
  opacity: 0.3;
}
.ki-lock-2.ki-duotone:after {
  content: "\eb9f";
  
}
.ki-lock-2.ki-duotone:before {
  content: "\eba0";
  position: absolute;
  
  opacity: 0.3;
}
.ki-lock-3.ki-duotone:after {
  content: "\eba1";
  
  opacity: 0.3;
}
.ki-lock-3.ki-duotone:before {
  content: "\eba2";
  position: absolute;
  
}
.ki-lock.ki-duotone:after {
  content: "\eba3";
  
  opacity: 0.3;
}
.ki-lock.ki-duotone:before {
  content: "\eba4";
  position: absolute;
  
}
.ki-logistic.ki-duotone:after {
  content: "\eba5";
  
  opacity: 0.3;
}
.ki-logistic.ki-duotone:before {
  content: "\eba6";
  position: absolute;
  
}
.ki-lots-shopping.ki-duotone:after {
  content: "\eba7";
  
  opacity: 0.3;
}
.ki-lots-shopping.ki-duotone:before {
  content: "\eba8";
  position: absolute;
  
}
.ki-lovely.ki-duotone:after {
  content: "\eba9";
  
  opacity: 0.3;
}
.ki-lovely.ki-duotone:before {
  content: "\ebaa";
  position: absolute;
  
}
.ki-lts.ki-duotone:after {
  content: "\ebab";
  
}
.ki-lts.ki-duotone:before {
  content: "\ebac";
  position: absolute;
  
  opacity: 0.3;
}
.ki-magnifier.ki-duotone:after {
  content: "\ebad";
  
  opacity: 0.3;
}
.ki-magnifier.ki-duotone:before {
  content: "\ebae";
  position: absolute;
  
}
.ki-map.ki-duotone:after {
  content: "\ebaf";
  
  opacity: 0.3;
}
.ki-map.ki-duotone:before {
  content: "\ebb0";
  position: absolute;
  
}
.ki-mask.ki-duotone:after {
  content: "\ebb1";
  
  opacity: 0.3;
}
.ki-mask.ki-duotone:before {
  content: "\ebb2";
  position: absolute;
  
}
.ki-maximize.ki-duotone:after {
  content: "\ebb3";
  
  opacity: 0.3;
}
.ki-maximize.ki-duotone:before {
  content: "\ebb4";
  position: absolute;
  
}
.ki-medal-star.ki-duotone:after {
  content: "\ebb5";
  
  opacity: 0.3;
}
.ki-medal-star.ki-duotone:before {
  content: "\ebb6";
  position: absolute;
  
}
.ki-menu.ki-duotone:after {
  content: "\ebb7";
  
  opacity: 0.3;
}
.ki-menu.ki-duotone:before {
  content: "\ebb8";
  position: absolute;
  
}
.ki-message-add.ki-duotone:after {
  content: "\ebb9";
  
  opacity: 0.3;
}
.ki-message-add.ki-duotone:before {
  content: "\ebba";
  position: absolute;
  
}
.ki-message-edit.ki-duotone:after {
  content: "\ebbb";
  
  opacity: 0.3;
}
.ki-message-edit.ki-duotone:before {
  content: "\ebbc";
  position: absolute;
  
}
.ki-message-minus.ki-duotone:after {
  content: "\ebbd";
  
  opacity: 0.3;
}
.ki-message-minus.ki-duotone:before {
  content: "\ebbe";
  position: absolute;
  
}
.ki-message-notify.ki-duotone:after {
  content: "\ebbf";
  
  opacity: 0.3;
}
.ki-message-notify.ki-duotone:before {
  content: "\ebc0";
  position: absolute;
  
}
.ki-message-programming.ki-duotone:after {
  content: "\ebc1";
  
  opacity: 0.3;
}
.ki-message-programming.ki-duotone:before {
  content: "\ebc2";
  position: absolute;
  
}
.ki-message-question.ki-duotone:after {
  content: "\ebc3";
  
  opacity: 0.3;
}
.ki-message-question.ki-duotone:before {
  content: "\ebc4";
  position: absolute;
  
}
.ki-message-text-2.ki-duotone:after {
  content: "\ebc5";
  
  opacity: 0.3;
}
.ki-message-text-2.ki-duotone:before {
  content: "\ebc6";
  position: absolute;
  
}
.ki-message-text.ki-duotone:after {
  content: "\ebc7";
  
  opacity: 0.3;
}
.ki-message-text.ki-duotone:before {
  content: "\ebc8";
  position: absolute;
  
}
.ki-messages.ki-duotone:after {
  content: "\ebc9";
  
  opacity: 0.3;
}
.ki-messages.ki-duotone:before {
  content: "\ebca";
  position: absolute;
  
}
.ki-microsoft.ki-duotone:after {
  content: "\ebcb";
  
}
.ki-microsoft.ki-duotone:before {
  content: "\ebcc";
  position: absolute;
  
  opacity: 0.3;
}
.ki-milk.ki-duotone:after {
  content: "\ebcd";
  
  opacity: 0.3;
}
.ki-milk.ki-duotone:before {
  content: "\ebce";
  position: absolute;
  
}
.ki-minus-circle.ki-duotone:after {
  content: "\ebcf";
  
  opacity: 0.3;
}
.ki-minus-circle.ki-duotone:before {
  content: "\ebd0";
  position: absolute;
  
}
.ki-minus-folder.ki-duotone:after {
  content: "\ebd1";
  
  opacity: 0.3;
}
.ki-minus-folder.ki-duotone:before {
  content: "\ebd2";
  position: absolute;
  
}
.ki-minus-squared.ki-duotone:after {
  content: "\ebd3";
  
  opacity: 0.3;
}
.ki-minus-squared.ki-duotone:before {
  content: "\ebd4";
  position: absolute;
  
}
.ki-minus.ki-duotone:before {
  content: "\ebd5";
}
.ki-moon.ki-duotone:after {
  content: "\ebd6";
  
}
.ki-moon.ki-duotone:before {
  content: "\ebd7";
  position: absolute;
  
  opacity: 0.3;
}
.ki-more-2.ki-duotone:after {
  content: "\ebd8";
  
  opacity: 0.3;
}
.ki-more-2.ki-duotone:before {
  content: "\ebd9";
  position: absolute;
  
}
.ki-mouse-circle.ki-duotone:after {
  content: "\ebda";
  
}
.ki-mouse-circle.ki-duotone:before {
  content: "\ebdb";
  position: absolute;
  
  opacity: 0.3;
}
.ki-mouse-square.ki-duotone:after {
  content: "\ebdc";
  
  opacity: 0.3;
}
.ki-mouse-square.ki-duotone:before {
  content: "\ebdd";
  position: absolute;
  
}
.ki-mouse.ki-duotone:after {
  content: "\ebde";
  
  opacity: 0.3;
}
.ki-mouse.ki-duotone:before {
  content: "\ebdf";
  position: absolute;
  
}
.ki-nexo.ki-duotone:after {
  content: "\ebe0";
  
  opacity: 0.3;
}
.ki-nexo.ki-duotone:before {
  content: "\ebe1";
  position: absolute;
  
}
.ki-night-day.ki-duotone:after {
  content: "\ebe2";
  
  opacity: 0.3;
}
.ki-night-day.ki-duotone:before {
  content: "\ebe3";
  position: absolute;
  
}
.ki-note-2.ki-duotone:after {
  content: "\ebe4";
  
  opacity: 0.3;
}
.ki-note-2.ki-duotone:before {
  content: "\ebe5";
  position: absolute;
  
}
.ki-note.ki-duotone:after {
  content: "\ebe6";
  
  opacity: 0.3;
}
.ki-note.ki-duotone:before {
  content: "\ebe7";
  position: absolute;
  
}
.ki-notepad-bookmark.ki-duotone:after {
  content: "\ebe8";
  
}
.ki-notepad-bookmark.ki-duotone:before {
  content: "\ebe9";
  position: absolute;
  
  opacity: 0.3;
}
.ki-notepad-edit.ki-duotone:after {
  content: "\ebea";
  
  opacity: 0.3;
}
.ki-notepad-edit.ki-duotone:before {
  content: "\ebeb";
  position: absolute;
  
}
.ki-notepad.ki-duotone:after {
  content: "\ebec";
  
  opacity: 0.3;
}
.ki-notepad.ki-duotone:before {
  content: "\ebed";
  position: absolute;
  
}
.ki-notification-1.ki-duotone:after {
  content: "\ebee";
  
}
.ki-notification-1.ki-duotone:before {
  content: "\ebef";
  position: absolute;
  
  opacity: 0.3;
}
.ki-notification-bing.ki-duotone:after {
  content: "\ebf0";
  
}
.ki-notification-bing.ki-duotone:before {
  content: "\ebf1";
  position: absolute;
  
  opacity: 0.3;
}
.ki-notification-circle.ki-duotone:after {
  content: "\ebf2";
  
}
.ki-notification-circle.ki-duotone:before {
  content: "\ebf3";
  position: absolute;
  
  opacity: 0.3;
}
.ki-notification-favorite.ki-duotone:after {
  content: "\ebf4";
  
}
.ki-notification-favorite.ki-duotone:before {
  content: "\ebf5";
  position: absolute;
  
  opacity: 0.3;
}
.ki-notification-on.ki-duotone:after {
  content: "\ebf6";
  
  opacity: 0.3;
}
.ki-notification-on.ki-duotone:before {
  content: "\ebf7";
  position: absolute;
  
}
.ki-notification-status.ki-duotone:after {
  content: "\ebf8";
  
  opacity: 0.3;
}
.ki-notification-status.ki-duotone:before {
  content: "\ebf9";
  position: absolute;
  
}
.ki-notification.ki-duotone:after {
  content: "\ebfa";
  
  opacity: 0.3;
}
.ki-notification.ki-duotone:before {
  content: "\ebfb";
  position: absolute;
  
}
.ki-ocean.ki-duotone:after {
  content: "\ebfc";
  
}
.ki-ocean.ki-duotone:before {
  content: "\ebfd";
  position: absolute;
  
  opacity: 0.3;
}
.ki-office-bag.ki-duotone:after {
  content: "\ebfe";
  
  opacity: 0.3;
}
.ki-office-bag.ki-duotone:before {
  content: "\ebff";
  position: absolute;
  
}
.ki-package.ki-duotone:after {
  content: "\ec00";
  
  opacity: 0.3;
}
.ki-package.ki-duotone:before {
  content: "\ec01";
  position: absolute;
  
}
.ki-pad.ki-duotone:after {
  content: "\ec02";
  
  opacity: 0.3;
}
.ki-pad.ki-duotone:before {
  content: "\ec03";
  position: absolute;
  
}
.ki-pails.ki-duotone:after {
  content: "\ec04";
  
  opacity: 0.3;
}
.ki-pails.ki-duotone:before {
  content: "\ec05";
  position: absolute;
  
}
.ki-paintbucket.ki-duotone:after {
  content: "\ec06";
  
  opacity: 0.3;
}
.ki-paintbucket.ki-duotone:before {
  content: "\ec07";
  position: absolute;
  
}
.ki-paper-clip.ki-duotone:before {
  content: "\ec08";
}
.ki-paper-plane.ki-duotone:after {
  content: "\ec09";
  
}
.ki-paper-plane.ki-duotone:before {
  content: "\ec0a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-parcel-tracking.ki-duotone:after {
  content: "\ec0b";
  
}
.ki-parcel-tracking.ki-duotone:before {
  content: "\ec0c";
  position: absolute;
  
  opacity: 0.3;
}
.ki-parcel.ki-duotone:after {
  content: "\ec0d";
  
  opacity: 0.3;
}
.ki-parcel.ki-duotone:before {
  content: "\ec0e";
  position: absolute;
  
}
.ki-password-check.ki-duotone:after {
  content: "\ec0f";
  
}
.ki-password-check.ki-duotone:before {
  content: "\ec10";
  position: absolute;
  
  opacity: 0.3;
}
.ki-paypal.ki-duotone:after {
  content: "\ec11";
  
}
.ki-paypal.ki-duotone:before {
  content: "\ec12";
  position: absolute;
  
  opacity: 0.3;
}
.ki-pencil.ki-duotone:after {
  content: "\ec13";
  
  opacity: 0.3;
}
.ki-pencil.ki-duotone:before {
  content: "\ec14";
  position: absolute;
  
}
.ki-people.ki-duotone:after {
  content: "\ec15";
  
}
.ki-people.ki-duotone:before {
  content: "\ec16";
  position: absolute;
  
  opacity: 0.3;
}
.ki-percentage.ki-duotone:after {
  content: "\ec17";
  
}
.ki-percentage.ki-duotone:before {
  content: "\ec18";
  position: absolute;
  
  opacity: 0.3;
}
.ki-phone.ki-duotone:after {
  content: "\ec19";
  
  opacity: 0.3;
}
.ki-phone.ki-duotone:before {
  content: "\ec1a";
  position: absolute;
  
}
.ki-photoshop.ki-duotone:after {
  content: "\ec1b";
  
  opacity: 0.3;
}
.ki-photoshop.ki-duotone:before {
  content: "\ec1c";
  position: absolute;
  
}
.ki-picture.ki-duotone:after {
  content: "\ec1d";
  
  opacity: 0.3;
}
.ki-picture.ki-duotone:before {
  content: "\ec1e";
  position: absolute;
  
}
.ki-pill.ki-duotone:before {
  content: "\ec1f";
}
.ki-pin.ki-duotone:after {
  content: "\ec20";
  
  opacity: 0.3;
}
.ki-pin.ki-duotone:before {
  content: "\ec21";
  position: absolute;
  
}
.ki-plus-circle.ki-duotone:after {
  content: "\ec22";
  
  opacity: 0.3;
}
.ki-plus-circle.ki-duotone:before {
  content: "\ec23";
  position: absolute;
  
}
.ki-plus-squared.ki-duotone:after {
  content: "\ec24";
  
  opacity: 0.3;
}
.ki-plus-squared.ki-duotone:before {
  content: "\ec25";
  position: absolute;
  
}
.ki-plus.ki-duotone:before {
  content: "\ec26";
}
.ki-pointers.ki-duotone:after {
  content: "\ec27";
  
  opacity: 0.3;
}
.ki-pointers.ki-duotone:before {
  content: "\ec28";
  position: absolute;
  
}
.ki-price-tag.ki-duotone:after {
  content: "\ec29";
  
  opacity: 0.3;
}
.ki-price-tag.ki-duotone:before {
  content: "\ec2a";
  position: absolute;
  
}
.ki-printer.ki-duotone:after {
  content: "\ec2b";
  
  opacity: 0.3;
}
.ki-printer.ki-duotone:before {
  content: "\ec2c";
  position: absolute;
  
}
.ki-profile-circle.ki-duotone:after {
  content: "\ec2d";
  
  opacity: 0.3;
}
.ki-profile-circle.ki-duotone:before {
  content: "\ec2e";
  position: absolute;
  
}
.ki-pulse.ki-duotone:after {
  content: "\ec2f";
  
  opacity: 0.3;
}
.ki-pulse.ki-duotone:before {
  content: "\ec30";
  position: absolute;
  
}
.ki-purchase.ki-duotone:after {
  content: "\ec31";
  
  opacity: 0.3;
}
.ki-purchase.ki-duotone:before {
  content: "\ec32";
  position: absolute;
  
}
.ki-python.ki-duotone:after {
  content: "\ec33";
  
  opacity: 0.3;
}
.ki-python.ki-duotone:before {
  content: "\ec34";
  position: absolute;
  
}
.ki-question-2.ki-duotone:after {
  content: "\ec35";
  
  opacity: 0.3;
}
.ki-question-2.ki-duotone:before {
  content: "\ec36";
  position: absolute;
  
}
.ki-question.ki-duotone:after {
  content: "\ec37";
  
  opacity: 0.3;
}
.ki-question.ki-duotone:before {
  content: "\ec38";
  position: absolute;
  
}
.ki-questionnaire-tablet.ki-duotone:after {
  content: "\ec39";
  
  opacity: 0.3;
}
.ki-questionnaire-tablet.ki-duotone:before {
  content: "\ec3a";
  position: absolute;
  
}
.ki-ranking.ki-duotone:after {
  content: "\ec3b";
  
  opacity: 0.3;
}
.ki-ranking.ki-duotone:before {
  content: "\ec3c";
  position: absolute;
  
}
.ki-react.ki-duotone:after {
  content: "\ec3d";
  
  opacity: 0.3;
}
.ki-react.ki-duotone:before {
  content: "\ec3e";
  position: absolute;
  
}
.ki-receipt-square.ki-duotone:after {
  content: "\ec3f";
  
  opacity: 0.3;
}
.ki-receipt-square.ki-duotone:before {
  content: "\ec40";
  position: absolute;
  
}
.ki-rescue.ki-duotone:after {
  content: "\ec41";
  
  opacity: 0.3;
}
.ki-rescue.ki-duotone:before {
  content: "\ec42";
  position: absolute;
  
}
.ki-right-left.ki-duotone:after {
  content: "\ec43";
  
}
.ki-right-left.ki-duotone:before {
  content: "\ec44";
  position: absolute;
  
  opacity: 0.3;
}
.ki-right-square.ki-duotone:after {
  content: "\ec45";
  
  opacity: 0.3;
}
.ki-right-square.ki-duotone:before {
  content: "\ec46";
  position: absolute;
  
}
.ki-right.ki-duotone:before {
  content: "\ec47";
}
.ki-rocket.ki-duotone:after {
  content: "\ec48";
  
  opacity: 0.3;
}
.ki-rocket.ki-duotone:before {
  content: "\ec49";
  position: absolute;
  
}
.ki-route.ki-duotone:after {
  content: "\ec4a";
  
}
.ki-route.ki-duotone:before {
  content: "\ec4b";
  position: absolute;
  
  opacity: 0.3;
}
.ki-router.ki-duotone:after {
  content: "\ec4c";
  
  opacity: 0.3;
}
.ki-router.ki-duotone:before {
  content: "\ec4d";
  position: absolute;
  
}
.ki-row-horizontal.ki-duotone:after {
  content: "\ec4e";
  
}
.ki-row-horizontal.ki-duotone:before {
  content: "\ec4f";
  position: absolute;
  
  opacity: 0.3;
}
.ki-row-vertical.ki-duotone:after {
  content: "\ec50";
  
}
.ki-row-vertical.ki-duotone:before {
  content: "\ec51";
  position: absolute;
  
  opacity: 0.3;
}
.ki-safe-home.ki-duotone:after {
  content: "\ec52";
  
  opacity: 0.3;
}
.ki-safe-home.ki-duotone:before {
  content: "\ec53";
  position: absolute;
  
}
.ki-satellite.ki-duotone:after {
  content: "\ec54";
  
  opacity: 0.3;
}
.ki-satellite.ki-duotone:before {
  content: "\ec55";
  position: absolute;
  
}
.ki-save-2.ki-duotone:after {
  content: "\ec56";
  
  opacity: 0.3;
}
.ki-save-2.ki-duotone:before {
  content: "\ec57";
  position: absolute;
  
}
.ki-save-deposit.ki-duotone:after {
  content: "\ec58";
  
}
.ki-save-deposit.ki-duotone:before {
  content: "\ec59";
  position: absolute;
  
  opacity: 0.3;
}
.ki-scan-barcode.ki-duotone:after {
  content: "\ec5a";
  
}
.ki-scan-barcode.ki-duotone:before {
  content: "\ec5b";
  position: absolute;
  
  opacity: 0.3;
}
.ki-screen.ki-duotone:after {
  content: "\ec5c";
  
  opacity: 0.3;
}
.ki-screen.ki-duotone:before {
  content: "\ec5d";
  position: absolute;
  
}
.ki-scroll.ki-duotone:after {
  content: "\ec5e";
  
  opacity: 0.3;
}
.ki-scroll.ki-duotone:before {
  content: "\ec5f";
  position: absolute;
  
}
.ki-search-list.ki-duotone:after {
  content: "\ec60";
  
  opacity: 0.3;
}
.ki-search-list.ki-duotone:before {
  content: "\ec61";
  position: absolute;
  
}
.ki-security-user.ki-duotone:after {
  content: "\ec62";
  
  opacity: 0.3;
}
.ki-security-user.ki-duotone:before {
  content: "\ec63";
  position: absolute;
  
}
.ki-setting-2.ki-duotone:after {
  content: "\ec64";
  
  opacity: 0.3;
}
.ki-setting-2.ki-duotone:before {
  content: "\ec65";
  position: absolute;
  
}
.ki-setting-3.ki-duotone:after {
  content: "\ec66";
  
  opacity: 0.3;
}
.ki-setting-3.ki-duotone:before {
  content: "\ec67";
  position: absolute;
  
}
.ki-setting-4.ki-duotone:before {
  content: "\ec68";
}
.ki-setting.ki-duotone:after {
  content: "\ec69";
  
  opacity: 0.3;
}
.ki-setting.ki-duotone:before {
  content: "\ec6a";
  position: absolute;
  
}
.ki-share.ki-duotone:after {
  content: "\ec6b";
  
  opacity: 0.3;
}
.ki-share.ki-duotone:before {
  content: "\ec6c";
  position: absolute;
  
}
.ki-shield-cross.ki-duotone:after {
  content: "\ec6d";
  
  opacity: 0.3;
}
.ki-shield-cross.ki-duotone:before {
  content: "\ec6e";
  position: absolute;
  
}
.ki-shield-search.ki-duotone:after {
  content: "\ec6f";
  
  opacity: 0.3;
}
.ki-shield-search.ki-duotone:before {
  content: "\ec70";
  position: absolute;
  
}
.ki-shield-slash.ki-duotone:after {
  content: "\ec71";
  
}
.ki-shield-slash.ki-duotone:before {
  content: "\ec72";
  position: absolute;
  
  opacity: 0.3;
}
.ki-shield-tick.ki-duotone:after {
  content: "\ec73";
  
  opacity: 0.3;
}
.ki-shield-tick.ki-duotone:before {
  content: "\ec74";
  position: absolute;
  
}
.ki-shield.ki-duotone:after {
  content: "\ec75";
  
  opacity: 0.3;
}
.ki-shield.ki-duotone:before {
  content: "\ec76";
  position: absolute;
  
}
.ki-ship.ki-duotone:after {
  content: "\ec77";
  
  opacity: 0.3;
}
.ki-ship.ki-duotone:before {
  content: "\ec78";
  position: absolute;
  
}
.ki-shop.ki-duotone:after {
  content: "\ec79";
  
}
.ki-shop.ki-duotone:before {
  content: "\ec7a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-simcard-2.ki-duotone:after {
  content: "\ec7b";
  
  opacity: 0.3;
}
.ki-simcard-2.ki-duotone:before {
  content: "\ec7c";
  position: absolute;
  
}
.ki-simcard.ki-duotone:after {
  content: "\ec7d";
  
  opacity: 0.3;
}
.ki-simcard.ki-duotone:before {
  content: "\ec7e";
  position: absolute;
  
}
.ki-size.ki-duotone:after {
  content: "\ec7f";
  
  opacity: 0.3;
}
.ki-size.ki-duotone:before {
  content: "\ec80";
  position: absolute;
  
}
.ki-slack.ki-duotone:after {
  content: "\ec81";
  
}
.ki-slack.ki-duotone:before {
  content: "\ec82";
  position: absolute;
  
  opacity: 0.3;
}
.ki-slider-horizontal-2.ki-duotone:after {
  content: "\ec83";
  
}
.ki-slider-horizontal-2.ki-duotone:before {
  content: "\ec84";
  position: absolute;
  
  opacity: 0.3;
}
.ki-slider-horizontal.ki-duotone:after {
  content: "\ec85";
  
  opacity: 0.3;
}
.ki-slider-horizontal.ki-duotone:before {
  content: "\ec86";
  position: absolute;
  
}
.ki-slider-vertica.ki-duotone:after {
  content: "\ec87";
  
  opacity: 0.3;
}
.ki-slider-vertica.ki-duotone:before {
  content: "\ec88";
  position: absolute;
  
}
.ki-slider-vertical.ki-duotone:after {
  content: "\ec89";
  
}
.ki-slider-vertical.ki-duotone:before {
  content: "\ec8a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-slider.ki-duotone:after {
  content: "\ec8b";
  
  opacity: 0.3;
}
.ki-slider.ki-duotone:before {
  content: "\ec8c";
  position: absolute;
  
}
.ki-sms.ki-duotone:after {
  content: "\ec8d";
  
  opacity: 0.3;
}
.ki-sms.ki-duotone:before {
  content: "\ec8e";
  position: absolute;
  
}
.ki-snapchat.ki-duotone:after {
  content: "\ec8f";
  
}
.ki-snapchat.ki-duotone:before {
  content: "\ec90";
  position: absolute;
  
  opacity: 0.3;
}
.ki-social-media.ki-duotone:after {
  content: "\ec91";
  
}
.ki-social-media.ki-duotone:before {
  content: "\ec92";
  position: absolute;
  
  opacity: 0.3;
}
.ki-soft-2.ki-duotone:after {
  content: "\ec93";
  
  opacity: 0.3;
}
.ki-soft-2.ki-duotone:before {
  content: "\ec94";
  position: absolute;
  
}
.ki-soft-3.ki-duotone:after {
  content: "\ec95";
  
}
.ki-soft-3.ki-duotone:before {
  content: "\ec96";
  position: absolute;
  
  opacity: 0.3;
}
.ki-soft.ki-duotone:after {
  content: "\ec97";
  
  opacity: 0.3;
}
.ki-soft.ki-duotone:before {
  content: "\ec98";
  position: absolute;
  
}
.ki-some-files.ki-duotone:after {
  content: "\ec99";
  
  opacity: 0.3;
}
.ki-some-files.ki-duotone:before {
  content: "\ec9a";
  position: absolute;
  
}
.ki-sort.ki-duotone:after {
  content: "\ec9b";
  
  opacity: 0.3;
}
.ki-sort.ki-duotone:before {
  content: "\ec9c";
  position: absolute;
  
}
.ki-speaker.ki-duotone:after {
  content: "\ec9d";
  
  opacity: 0.3;
}
.ki-speaker.ki-duotone:before {
  content: "\ec9e";
  position: absolute;
  
}
.ki-spotify.ki-duotone:after {
  content: "\ec9f";
  
}
.ki-spotify.ki-duotone:before {
  content: "\eca0";
  position: absolute;
  
  opacity: 0.3;
}
.ki-spring-framework.ki-duotone:before {
  content: "\eca1";
}
.ki-square-brackets.ki-duotone:after {
  content: "\eca2";
  
  opacity: 0.3;
}
.ki-square-brackets.ki-duotone:before {
  content: "\eca3";
  position: absolute;
  
}
.ki-star.ki-duotone:before {
  content: "\eca4";
}
.ki-status.ki-duotone:after {
  content: "\eca5";
  
  opacity: 0.3;
}
.ki-status.ki-duotone:before {
  content: "\eca6";
  position: absolute;
  
}
.ki-subtitle.ki-duotone:after {
  content: "\eca7";
  
  opacity: 0.3;
}
.ki-subtitle.ki-duotone:before {
  content: "\eca8";
  position: absolute;
  
}
.ki-sun.ki-duotone:after {
  content: "\eca9";
  
  opacity: 0.3;
}
.ki-sun.ki-duotone:before {
  content: "\ecaa";
  position: absolute;
  
}
.ki-support.ki-duotone:after {
  content: "\ecab";
  
  opacity: 0.3;
}
.ki-support.ki-duotone:before {
  content: "\ecac";
  position: absolute;
  
}
.ki-switch.ki-duotone:after {
  content: "\ecad";
  
}
.ki-switch.ki-duotone:before {
  content: "\ecae";
  position: absolute;
  
  opacity: 0.3;
}
.ki-syringe.ki-duotone:after {
  content: "\ecaf";
  
  opacity: 0.3;
}
.ki-syringe.ki-duotone:before {
  content: "\ecb0";
  position: absolute;
  
}
.ki-tab-tablet.ki-duotone:after {
  content: "\ecb1";
  
  opacity: 0.3;
}
.ki-tab-tablet.ki-duotone:before {
  content: "\ecb2";
  position: absolute;
  
}
.ki-tablet-delete.ki-duotone:after {
  content: "\ecb3";
  
  opacity: 0.3;
}
.ki-tablet-delete.ki-duotone:before {
  content: "\ecb4";
  position: absolute;
  
}
.ki-tablet-down.ki-duotone:after {
  content: "\ecb5";
  
  opacity: 0.3;
}
.ki-tablet-down.ki-duotone:before {
  content: "\ecb6";
  position: absolute;
  
}
.ki-tablet-ok.ki-duotone:after {
  content: "\ecb7";
  
  opacity: 0.3;
}
.ki-tablet-ok.ki-duotone:before {
  content: "\ecb8";
  position: absolute;
  
}
.ki-tablet-text-down.ki-duotone:after {
  content: "\ecb9";
  
  opacity: 0.3;
}
.ki-tablet-text-down.ki-duotone:before {
  content: "\ecba";
  position: absolute;
  
}
.ki-tablet-text-up.ki-duotone:after {
  content: "\ecbb";
  
}
.ki-tablet-text-up.ki-duotone:before {
  content: "\ecbc";
  position: absolute;
  
  opacity: 0.3;
}
.ki-tablet-up.ki-duotone:after {
  content: "\ecbd";
  
  opacity: 0.3;
}
.ki-tablet-up.ki-duotone:before {
  content: "\ecbe";
  position: absolute;
  
}
.ki-tablet.ki-duotone:after {
  content: "\ecbf";
  
}
.ki-tablet.ki-duotone:before {
  content: "\ecc0";
  position: absolute;
  
  opacity: 0.3;
}
.ki-tag-cross.ki-duotone:after {
  content: "\ecc1";
  
}
.ki-tag-cross.ki-duotone:before {
  content: "\ecc2";
  position: absolute;
  
  opacity: 0.3;
}
.ki-tag.ki-duotone:after {
  content: "\ecc3";
  
  opacity: 0.3;
}
.ki-tag.ki-duotone:before {
  content: "\ecc4";
  position: absolute;
  
}
.ki-teacher.ki-duotone:after {
  content: "\ecc5";
  
  opacity: 0.3;
}
.ki-teacher.ki-duotone:before {
  content: "\ecc6";
  position: absolute;
  
}
.ki-technology-1.ki-duotone:after {
  content: "\ecc7";
  
}
.ki-technology-1.ki-duotone:before {
  content: "\ecc8";
  position: absolute;
  
  opacity: 0.3;
}
.ki-technology-2.ki-duotone:after {
  content: "\ecc9";
  
  opacity: 0.3;
}
.ki-technology-2.ki-duotone:before {
  content: "\ecca";
  position: absolute;
  
}
.ki-technology-3.ki-duotone:after {
  content: "\eccb";
  
  opacity: 0.3;
}
.ki-technology-3.ki-duotone:before {
  content: "\eccc";
  position: absolute;
  
}
.ki-technology-4.ki-duotone:after {
  content: "\eccd";
  
  opacity: 0.3;
}
.ki-technology-4.ki-duotone:before {
  content: "\ecce";
  position: absolute;
  
}
.ki-telephone-geolocation.ki-duotone:after {
  content: "\eccf";
  
  opacity: 0.3;
}
.ki-telephone-geolocation.ki-duotone:before {
  content: "\ecd0";
  position: absolute;
  
}
.ki-test-tubes.ki-duotone:after {
  content: "\ecd1";
  
  opacity: 0.3;
}
.ki-test-tubes.ki-duotone:before {
  content: "\ecd2";
  position: absolute;
  
}
.ki-text-bold.ki-duotone:after {
  content: "\ecd3";
  
  opacity: 0.3;
}
.ki-text-bold.ki-duotone:before {
  content: "\ecd4";
  position: absolute;
  
}
.ki-text-circle.ki-duotone:after {
  content: "\ecd5";
  
  opacity: 0.3;
}
.ki-text-circle.ki-duotone:before {
  content: "\ecd6";
  position: absolute;
  
}
.ki-text-italic.ki-duotone:after {
  content: "\ecd7";
  
  opacity: 0.3;
}
.ki-text-italic.ki-duotone:before {
  content: "\ecd8";
  position: absolute;
  
}
.ki-text-number.ki-duotone:after {
  content: "\ecd9";
  
  opacity: 0.3;
}
.ki-text-number.ki-duotone:before {
  content: "\ecda";
  position: absolute;
  
}
.ki-text-strikethrough.ki-duotone:after {
  content: "\ecdb";
  
  opacity: 0.3;
}
.ki-text-strikethrough.ki-duotone:before {
  content: "\ecdc";
  position: absolute;
  
}
.ki-text-underline.ki-duotone:after {
  content: "\ecdd";
  
  opacity: 0.3;
}
.ki-text-underline.ki-duotone:before {
  content: "\ecde";
  position: absolute;
  
}
.ki-text.ki-duotone:before {
  content: "\ecdf";
}
.ki-textalign-center.ki-duotone:after {
  content: "\ece0";
  
}
.ki-textalign-center.ki-duotone:before {
  content: "\ece1";
  position: absolute;
  
  opacity: 0.3;
}
.ki-textalign-justifycenter.ki-duotone:after {
  content: "\ece2";
  
}
.ki-textalign-justifycenter.ki-duotone:before {
  content: "\ece3";
  position: absolute;
  
  opacity: 0.3;
}
.ki-textalign-left.ki-duotone:after {
  content: "\ece4";
  
}
.ki-textalign-left.ki-duotone:before {
  content: "\ece5";
  position: absolute;
  
  opacity: 0.3;
}
.ki-textalign-right.ki-duotone:after {
  content: "\ece6";
  
}
.ki-textalign-right.ki-duotone:before {
  content: "\ece7";
  position: absolute;
  
  opacity: 0.3;
}
.ki-thermometer.ki-duotone:after {
  content: "\ece8";
  
  opacity: 0.3;
}
.ki-thermometer.ki-duotone:before {
  content: "\ece9";
  position: absolute;
  
}
.ki-theta-theta.ki-duotone:after {
  content: "\ecea";
  
}
.ki-theta-theta.ki-duotone:before {
  content: "\eceb";
  position: absolute;
  
  opacity: 0.3;
}
.ki-tiktok.ki-duotone:after {
  content: "\ecec";
  
  opacity: 0.3;
}
.ki-tiktok.ki-duotone:before {
  content: "\eced";
  position: absolute;
  
}
.ki-time.ki-duotone:after {
  content: "\ecee";
  
  opacity: 0.3;
}
.ki-time.ki-duotone:before {
  content: "\ecef";
  position: absolute;
  
}
.ki-timer.ki-duotone:after {
  content: "\ecf0";
  
  opacity: 0.3;
}
.ki-timer.ki-duotone:before {
  content: "\ecf1";
  position: absolute;
  
}
.ki-to-left.ki-duotone:before {
  content: "\ecf2";
}
.ki-to-right.ki-duotone:before {
  content: "\ecf3";
}
.ki-toggle-off-circle.ki-duotone:after {
  content: "\ecf4";
  
  opacity: 0.3;
}
.ki-toggle-off-circle.ki-duotone:before {
  content: "\ecf5";
  position: absolute;
  
}
.ki-toggle-off.ki-duotone:after {
  content: "\ecf6";
  
  opacity: 0.3;
}
.ki-toggle-off.ki-duotone:before {
  content: "\ecf7";
  position: absolute;
  
}
.ki-toggle-on-circle.ki-duotone:after {
  content: "\ecf8";
  
  opacity: 0.3;
}
.ki-toggle-on-circle.ki-duotone:before {
  content: "\ecf9";
  position: absolute;
  
}
.ki-toggle-on.ki-duotone:after {
  content: "\ecfa";
  
  opacity: 0.3;
}
.ki-toggle-on.ki-duotone:before {
  content: "\ecfb";
  position: absolute;
  
}
.ki-trash-square.ki-duotone:after {
  content: "\ecfc";
  
  opacity: 0.3;
}
.ki-trash-square.ki-duotone:before {
  content: "\ecfd";
  position: absolute;
  
}
.ki-trash.ki-duotone:after {
  content: "\ecfe";
  
  opacity: 0.3;
}
.ki-trash.ki-duotone:before {
  content: "\ecff";
  position: absolute;
  
}
.ki-tree.ki-duotone:after {
  content: "\ed00";
  
  opacity: 0.3;
}
.ki-tree.ki-duotone:before {
  content: "\ed01";
  position: absolute;
  
}
.ki-trello.ki-duotone:after {
  content: "\ed02";
  
  opacity: 0.3;
}
.ki-trello.ki-duotone:before {
  content: "\ed03";
  position: absolute;
  
}
.ki-ts.ki-duotone:after {
  content: "\ed04";
  
  opacity: 0.3;
}
.ki-ts.ki-duotone:before {
  content: "\ed05";
  position: absolute;
  
}
.ki-twitch.ki-duotone:after {
  content: "\ed06";
  
  opacity: 0.3;
}
.ki-twitch.ki-duotone:before {
  content: "\ed07";
  position: absolute;
  
}
.ki-twitter.ki-duotone:after {
  content: "\ed08";
  
  opacity: 0.3;
}
.ki-twitter.ki-duotone:before {
  content: "\ed09";
  position: absolute;
  
}
.ki-two-credit-cart.ki-duotone:after {
  content: "\ed0a";
  
  opacity: 0.3;
}
.ki-two-credit-cart.ki-duotone:before {
  content: "\ed0b";
  position: absolute;
  
}
.ki-underlining.ki-duotone:after {
  content: "\ed0c";
  
  opacity: 0.3;
}
.ki-underlining.ki-duotone:before {
  content: "\ed0d";
  position: absolute;
  
}
.ki-up-diagonal.ki-duotone:after {
  content: "\ed0e";
  
  opacity: 0.3;
}
.ki-up-diagonal.ki-duotone:before {
  content: "\ed0f";
  position: absolute;
  
}
.ki-up-down.ki-duotone:after {
  content: "\ed10";
  
  opacity: 0.3;
}
.ki-up-down.ki-duotone:before {
  content: "\ed11";
  position: absolute;
  
}
.ki-up-square.ki-duotone:after {
  content: "\ed12";
  
  opacity: 0.3;
}
.ki-up-square.ki-duotone:before {
  content: "\ed13";
  position: absolute;
  
}
.ki-up.ki-duotone:before {
  content: "\ed14";
}
.ki-update-file.ki-duotone:after {
  content: "\ed15";
  
  opacity: 0.3;
}
.ki-update-file.ki-duotone:before {
  content: "\ed16";
  position: absolute;
  
}
.ki-update-folder.ki-duotone:after {
  content: "\ed17";
  
  opacity: 0.3;
}
.ki-update-folder.ki-duotone:before {
  content: "\ed18";
  position: absolute;
  
}
.ki-user-edit.ki-duotone:after {
  content: "\ed19";
  
  opacity: 0.3;
}
.ki-user-edit.ki-duotone:before {
  content: "\ed1a";
  position: absolute;
  
}
.ki-user-square.ki-duotone:after {
  content: "\ed1b";
  
  opacity: 0.3;
}
.ki-user-square.ki-duotone:before {
  content: "\ed1c";
  position: absolute;
  
}
.ki-user-tick.ki-duotone:after {
  content: "\ed1d";
  
}
.ki-user-tick.ki-duotone:before {
  content: "\ed1e";
  position: absolute;
  
  opacity: 0.3;
}
.ki-user.ki-duotone:after {
  content: "\ed1f";
  
  opacity: 0.3;
}
.ki-user.ki-duotone:before {
  content: "\ed20";
  position: absolute;
  
}
.ki-users.ki-duotone:after {
  content: "\ed21";
  
  opacity: 0.3;
}
.ki-users.ki-duotone:before {
  content: "\ed22";
  position: absolute;
  
}
.ki-verify.ki-duotone:after {
  content: "\ed23";
  
  opacity: 0.3;
}
.ki-verify.ki-duotone:before {
  content: "\ed24";
  position: absolute;
  
}
.ki-vibe-vibe.ki-duotone:after {
  content: "\ed25";
  
}
.ki-vibe-vibe.ki-duotone:before {
  content: "\ed26";
  position: absolute;
  
  opacity: 0.3;
}
.ki-virus.ki-duotone:after {
  content: "\ed27";
  
  opacity: 0.3;
}
.ki-virus.ki-duotone:before {
  content: "\ed28";
  position: absolute;
  
}
.ki-vue.ki-duotone:after {
  content: "\ed29";
  
  opacity: 0.3;
}
.ki-vue.ki-duotone:before {
  content: "\ed2a";
  position: absolute;
  
}
.ki-vuesax.ki-duotone:after {
  content: "\ed2b";
  
  opacity: 0.4;
}
.ki-vuesax.ki-duotone:before {
  content: "\ed2c";
  position: absolute;
  
}
.ki-wallet.ki-duotone:after {
  content: "\ed2d";
  
  opacity: 0.3;
}
.ki-wallet.ki-duotone:before {
  content: "\ed2e";
  position: absolute;
  
}
.ki-wanchain-wan.ki-duotone:after {
  content: "\ed2f";
  
}
.ki-wanchain-wan.ki-duotone:before {
  content: "\ed30";
  position: absolute;
  
  opacity: 0.3;
}
.ki-watch.ki-duotone:after {
  content: "\ed31";
  
  opacity: 0.3;
}
.ki-watch.ki-duotone:before {
  content: "\ed32";
  position: absolute;
  
}
.ki-whatsapp.ki-duotone:after {
  content: "\ed33";
  
  opacity: 0.4;
}
.ki-whatsapp.ki-duotone:before {
  content: "\ed34";
  position: absolute;
  
}
.ki-wifi-home.ki-duotone:after {
  content: "\ed35";
  
  opacity: 0.3;
}
.ki-wifi-home.ki-duotone:before {
  content: "\ed36";
  position: absolute;
  
}
.ki-wifi-square.ki-duotone:after {
  content: "\ed37";
  
  opacity: 0.3;
}
.ki-wifi-square.ki-duotone:before {
  content: "\ed38";
  position: absolute;
  
}
.ki-wifi.ki-duotone:after {
  content: "\ed39";
  
}
.ki-wifi.ki-duotone:before {
  content: "\ed3a";
  position: absolute;
  
  opacity: 0.3;
}
.ki-wireframe.ki-duotone:after {
  content: "\ed3b";
  
}
.ki-wireframe.ki-duotone:before {
  content: "\ed3c";
  position: absolute;
  
  opacity: 0.3;
}
.ki-wlan.ki-duotone:after {
  content: "\ed3d";
  
  opacity: 0.3;
}
.ki-wlan.ki-duotone:before {
  content: "\ed3e";
  position: absolute;
  
}
.ki-wrench.ki-duotone:after {
  content: "\ed3f";
  
  opacity: 0.3;
}
.ki-wrench.ki-duotone:before {
  content: "\ed40";
  position: absolute;
  
}
.ki-xaomi.ki-duotone:after {
  content: "\ed41";
  
}
.ki-xaomi.ki-duotone:before {
  content: "\ed42";
  position: absolute;
  
  opacity: 0.3;
}
.ki-xd.ki-duotone:after {
  content: "\ed43";
  
  opacity: 0.3;
}
.ki-xd.ki-duotone:before {
  content: "\ed44";
  position: absolute;
  
}
.ki-xmr.ki-duotone:after {
  content: "\ed45";
  
}
.ki-xmr.ki-duotone:before {
  content: "\ed46";
  position: absolute;
  
  opacity: 0.3;
}
.ki-yii.ki-duotone:after {
  content: "\ed47";
  
  opacity: 0.3;
}
.ki-yii.ki-duotone:before {
  content: "\ed48";
  position: absolute;
  
}
.ki-youtube.ki-duotone:after {
  content: "\ed49";
  
}
.ki-youtube.ki-duotone:before {
  content: "\ed4a";
  position: absolute;
  
  opacity: 0.3;
}

@font-face {
  font-family: 'keenicons-filled';
  src:
    url('fonts/keenicons-filled.ttf?nz57rx') format('truetype'),
    url('fonts/keenicons-filled.woff?nz57rx') format('woff'),
    url('fonts/keenicons-filled.svg?nz57rx#keenicons-filled') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.ki-filled {
  line-height: 1;
  position: relative;
  display: inline-flex;
}

.ki-filled:after, 
.ki-filled:before {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'keenicons-filled' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-flex;
}

.ki-abstract-1.ki-filled:after {
  content: "\e900";
  
  opacity: 0.1;
}
.ki-abstract-1.ki-filled:before {
  content: "\e901";
  position: absolute;
  
}
.ki-abstract-2.ki-filled:after {
  content: "\e902";
  
  opacity: 0.1;
}
.ki-abstract-2.ki-filled:before {
  content: "\e903";
  position: absolute;
  
}
.ki-abstract-3.ki-filled:after {
  content: "\e904";
  
  opacity: 0.1;
}
.ki-abstract-3.ki-filled:before {
  content: "\e905";
  position: absolute;
  
}
.ki-abstract-4.ki-filled:after {
  content: "\e906";
  
  opacity: 0.1;
}
.ki-abstract-4.ki-filled:before {
  content: "\e907";
  position: absolute;
  
}
.ki-abstract-5.ki-filled:after {
  content: "\e908";
  
  opacity: 0.1;
}
.ki-abstract-5.ki-filled:before {
  content: "\e909";
  position: absolute;
  
}
.ki-abstract-6.ki-filled:after {
  content: "\e90a";
  
  opacity: 0.1;
}
.ki-abstract-6.ki-filled:before {
  content: "\e90b";
  position: absolute;
  
}
.ki-abstract-7.ki-filled:after {
  content: "\e90c";
  
}
.ki-abstract-7.ki-filled:before {
  content: "\e90d";
  position: absolute;
  
  opacity: 0.1;
}
.ki-abstract-8.ki-filled:after {
  content: "\e90e";
  
  opacity: 0.1;
}
.ki-abstract-8.ki-filled:before {
  content: "\e90f";
  position: absolute;
  
}
.ki-abstract-9.ki-filled:after {
  content: "\e910";
  
  opacity: 0.1;
}
.ki-abstract-9.ki-filled:before {
  content: "\e911";
  position: absolute;
  
}
.ki-abstract-10.ki-filled:after {
  content: "\e912";
  
  opacity: 0.1;
}
.ki-abstract-10.ki-filled:before {
  content: "\e913";
  position: absolute;
  
}
.ki-abstract-11.ki-filled:after {
  content: "\e914";
  
  opacity: 0.1;
}
.ki-abstract-11.ki-filled:before {
  content: "\e915";
  position: absolute;
  
}
.ki-abstract-12.ki-filled:after {
  content: "\e916";
  
  opacity: 0.1;
}
.ki-abstract-12.ki-filled:before {
  content: "\e917";
  position: absolute;
  
}
.ki-abstract-13.ki-filled:after {
  content: "\e918";
  
  opacity: 0.1;
}
.ki-abstract-13.ki-filled:before {
  content: "\e919";
  position: absolute;
  
}
.ki-abstract-14.ki-filled:after {
  content: "\e91a";
  
  opacity: 0.1;
}
.ki-abstract-14.ki-filled:before {
  content: "\e91b";
  position: absolute;
  
}
.ki-abstract-15.ki-filled:after {
  content: "\e91c";
  
  opacity: 0.1;
}
.ki-abstract-15.ki-filled:before {
  content: "\e91d";
  position: absolute;
  
}
.ki-abstract-16.ki-filled:after {
  content: "\e91e";
  
  opacity: 0.1;
}
.ki-abstract-16.ki-filled:before {
  content: "\e91f";
  position: absolute;
  
}
.ki-abstract-17.ki-filled:after {
  content: "\e920";
  
  opacity: 0.1;
}
.ki-abstract-17.ki-filled:before {
  content: "\e921";
  position: absolute;
  
}
.ki-abstract-18.ki-filled:after {
  content: "\e922";
  
  opacity: 0.1;
}
.ki-abstract-18.ki-filled:before {
  content: "\e923";
  position: absolute;
  
}
.ki-abstract-19.ki-filled:after {
  content: "\e924";
  
  opacity: 0.1;
}
.ki-abstract-19.ki-filled:before {
  content: "\e925";
  position: absolute;
  
}
.ki-abstract-20.ki-filled:after {
  content: "\e926";
  
  opacity: 0.1;
}
.ki-abstract-20.ki-filled:before {
  content: "\e927";
  position: absolute;
  
}
.ki-abstract-21.ki-filled:after {
  content: "\e928";
  
  opacity: 0.1;
}
.ki-abstract-21.ki-filled:before {
  content: "\e929";
  position: absolute;
  
}
.ki-abstract-22.ki-filled:after {
  content: "\e92a";
  
  opacity: 0.1;
}
.ki-abstract-22.ki-filled:before {
  content: "\e92b";
  position: absolute;
  
}
.ki-abstract-23.ki-filled:after {
  content: "\e92c";
  
  opacity: 0.1;
}
.ki-abstract-23.ki-filled:before {
  content: "\e92d";
  position: absolute;
  
}
.ki-abstract-24.ki-filled:after {
  content: "\e92e";
  
  opacity: 0.1;
}
.ki-abstract-24.ki-filled:before {
  content: "\e92f";
  position: absolute;
  
}
.ki-abstract-25.ki-filled:after {
  content: "\e930";
  
  opacity: 0.1;
}
.ki-abstract-25.ki-filled:before {
  content: "\e931";
  position: absolute;
  
}
.ki-abstract-26.ki-filled:after {
  content: "\e932";
  
  opacity: 0.1;
}
.ki-abstract-26.ki-filled:before {
  content: "\e933";
  position: absolute;
  
}
.ki-abstract-27.ki-filled:after {
  content: "\e934";
  
  opacity: 0.1;
}
.ki-abstract-27.ki-filled:before {
  content: "\e935";
  position: absolute;
  
}
.ki-abstract-28.ki-filled:after {
  content: "\e936";
  
  opacity: 0.1;
}
.ki-abstract-28.ki-filled:before {
  content: "\e937";
  position: absolute;
  
}
.ki-abstract-29.ki-filled:after {
  content: "\e938";
  
  opacity: 0.1;
}
.ki-abstract-29.ki-filled:before {
  content: "\e939";
  position: absolute;
  
}
.ki-abstract-30.ki-filled:after {
  content: "\e93a";
  
  opacity: 0.1;
}
.ki-abstract-30.ki-filled:before {
  content: "\e93b";
  position: absolute;
  
}
.ki-abstract-31.ki-filled:after {
  content: "\e93c";
  
  opacity: 0.1;
}
.ki-abstract-31.ki-filled:before {
  content: "\e93d";
  position: absolute;
  
}
.ki-abstract-32.ki-filled:after {
  content: "\e93e";
  
  opacity: 0.1;
}
.ki-abstract-32.ki-filled:before {
  content: "\e93f";
  position: absolute;
  
}
.ki-abstract-33.ki-filled:after {
  content: "\e940";
  
  opacity: 0.1;
}
.ki-abstract-33.ki-filled:before {
  content: "\e941";
  position: absolute;
  
}
.ki-abstract-34.ki-filled:after {
  content: "\e942";
  
  opacity: 0.1;
}
.ki-abstract-34.ki-filled:before {
  content: "\e943";
  position: absolute;
  
}
.ki-abstract-35.ki-filled:after {
  content: "\e944";
  
}
.ki-abstract-35.ki-filled:before {
  content: "\e945";
  position: absolute;
  
  opacity: 0.1;
}
.ki-abstract-36.ki-filled:after {
  content: "\e946";
  
  opacity: 0.1;
}
.ki-abstract-36.ki-filled:before {
  content: "\e947";
  position: absolute;
  
}
.ki-abstract-37.ki-filled:after {
  content: "\e948";
  
  opacity: 0.1;
}
.ki-abstract-37.ki-filled:before {
  content: "\e949";
  position: absolute;
  
}
.ki-abstract-38.ki-filled:after {
  content: "\e94a";
  
  opacity: 0.1;
}
.ki-abstract-38.ki-filled:before {
  content: "\e94b";
  position: absolute;
  
}
.ki-abstract-39.ki-filled:after {
  content: "\e94c";
  
  opacity: 0.1;
}
.ki-abstract-39.ki-filled:before {
  content: "\e94d";
  position: absolute;
  
}
.ki-abstract-40.ki-filled:after {
  content: "\e94e";
  
  opacity: 0.1;
}
.ki-abstract-40.ki-filled:before {
  content: "\e94f";
  position: absolute;
  
}
.ki-abstract-41.ki-filled:after {
  content: "\e950";
  
  opacity: 0.1;
}
.ki-abstract-41.ki-filled:before {
  content: "\e951";
  position: absolute;
  
}
.ki-abstract-42.ki-filled:after {
  content: "\e952";
  
  opacity: 0.1;
}
.ki-abstract-42.ki-filled:before {
  content: "\e953";
  position: absolute;
  
}
.ki-abstract-43.ki-filled:after {
  content: "\e954";
  
  opacity: 0.1;
}
.ki-abstract-43.ki-filled:before {
  content: "\e955";
  position: absolute;
  
}
.ki-abstract-44.ki-filled:after {
  content: "\e956";
  
  opacity: 0.1;
}
.ki-abstract-44.ki-filled:before {
  content: "\e957";
  position: absolute;
  
}
.ki-abstract-45.ki-filled:before {
  content: "\e958";
}
.ki-abstract-46.ki-filled:after {
  content: "\e959";
  
  opacity: 0.1;
}
.ki-abstract-46.ki-filled:before {
  content: "\e95a";
  position: absolute;
  
}
.ki-abstract-47.ki-filled:after {
  content: "\e95b";
  
  opacity: 0.1;
}
.ki-abstract-47.ki-filled:before {
  content: "\e95c";
  position: absolute;
  
}
.ki-abstract-48.ki-filled:after {
  content: "\e95d";
  
  opacity: 0.1;
}
.ki-abstract-48.ki-filled:before {
  content: "\e95e";
  position: absolute;
  
}
.ki-abstract-49.ki-filled:after {
  content: "\e95f";
  
  opacity: 0.1;
}
.ki-abstract-49.ki-filled:before {
  content: "\e960";
  position: absolute;
  
}
.ki-abstract.ki-filled:after {
  content: "\e961";
  
  opacity: 0.1;
}
.ki-abstract.ki-filled:before {
  content: "\e962";
  position: absolute;
  
}
.ki-add-files.ki-filled:after {
  content: "\e963";
  
  opacity: 0.1;
}
.ki-add-files.ki-filled:before {
  content: "\e964";
  position: absolute;
  
}
.ki-add-folder.ki-filled:after {
  content: "\e965";
  
  opacity: 0.1;
}
.ki-add-folder.ki-filled:before {
  content: "\e966";
  position: absolute;
  
}
.ki-add-notepad.ki-filled:after {
  content: "\e967";
  
  opacity: 0.1;
}
.ki-add-notepad.ki-filled:before {
  content: "\e968";
  position: absolute;
  
}
.ki-additem.ki-filled:after {
  content: "\e969";
  
  opacity: 0.1;
}
.ki-additem.ki-filled:before {
  content: "\e96a";
  position: absolute;
  
}
.ki-address-book.ki-filled:after {
  content: "\e96b";
  
  opacity: 0.1;
}
.ki-address-book.ki-filled:before {
  content: "\e96c";
  position: absolute;
  
}
.ki-airplane-square.ki-filled:after {
  content: "\e96d";
  
  opacity: 0.1;
}
.ki-airplane-square.ki-filled:before {
  content: "\e96e";
  position: absolute;
  
}
.ki-airplane.ki-filled:after {
  content: "\e96f";
  
  opacity: 0.1;
}
.ki-airplane.ki-filled:before {
  content: "\e970";
  position: absolute;
  
}
.ki-airpod.ki-filled:after {
  content: "\e971";
  
  opacity: 0.1;
}
.ki-airpod.ki-filled:before {
  content: "\e972";
  position: absolute;
  
}
.ki-android.ki-filled:after {
  content: "\e973";
  
  opacity: 0.1;
}
.ki-android.ki-filled:before {
  content: "\e974";
  position: absolute;
  
}
.ki-angular.ki-filled:after {
  content: "\e975";
  
  opacity: 0.1;
}
.ki-angular.ki-filled:before {
  content: "\e976";
  position: absolute;
  
}
.ki-apple.ki-filled:after {
  content: "\e977";
  
  opacity: 0.1;
}
.ki-apple.ki-filled:before {
  content: "\e978";
  position: absolute;
  
}
.ki-archive-tick.ki-filled:after {
  content: "\e979";
  
  opacity: 0.1;
}
.ki-archive-tick.ki-filled:before {
  content: "\e97a";
  position: absolute;
  
}
.ki-archive.ki-filled:after {
  content: "\e97b";
  
  opacity: 0.1;
}
.ki-archive.ki-filled:before {
  content: "\e97c";
  position: absolute;
  
}
.ki-arrow-circle-left.ki-filled:after {
  content: "\e97d";
  
  opacity: 0.1;
}
.ki-arrow-circle-left.ki-filled:before {
  content: "\e97e";
  position: absolute;
  
}
.ki-arrow-circle-right.ki-filled:after {
  content: "\e97f";
  
  opacity: 0.1;
}
.ki-arrow-circle-right.ki-filled:before {
  content: "\e980";
  position: absolute;
  
}
.ki-arrow-down-left.ki-filled:before {
  content: "\e981";
}
.ki-arrow-down-refraction.ki-filled:before {
  content: "\e982";
}
.ki-arrow-down-right.ki-filled:before {
  content: "\e983";
}
.ki-arrow-down.ki-filled:before {
  content: "\e984";
}
.ki-arrow-left.ki-filled:before {
  content: "\e985";
}
.ki-arrow-mix.ki-filled:before {
  content: "\e986";
}
.ki-arrow-right-left.ki-filled:before {
  content: "\e987";
}
.ki-arrow-right.ki-filled:before {
  content: "\e988";
}
.ki-arrow-two-diagonals.ki-filled:before {
  content: "\e989";
}
.ki-arrow-up-down.ki-filled:before {
  content: "\e98a";
}
.ki-arrow-up-left.ki-filled:before {
  content: "\e98b";
}
.ki-arrow-up-refraction.ki-filled:before {
  content: "\e98c";
}
.ki-arrow-up-right.ki-filled:before {
  content: "\e98d";
}
.ki-arrow-up.ki-filled:before {
  content: "\e98e";
}
.ki-arrow-zigzag.ki-filled:before {
  content: "\e98f";
}
.ki-arrows-circle.ki-filled:before {
  content: "\e990";
}
.ki-arrows-loop.ki-filled:before {
  content: "\e991";
}
.ki-artificial-intelligence.ki-filled:after {
  content: "\e992";
  
  opacity: 0.1;
}
.ki-artificial-intelligence.ki-filled:before {
  content: "\e993";
  position: absolute;
  
}
.ki-autobrightness.ki-filled:after {
  content: "\e994";
  
  opacity: 0.1;
}
.ki-autobrightness.ki-filled:before {
  content: "\e995";
  position: absolute;
  
}
.ki-avalanche-avax.ki-filled:after {
  content: "\e996";
  
  opacity: 0.1;
}
.ki-avalanche-avax.ki-filled:before {
  content: "\e997";
  position: absolute;
  
}
.ki-award.ki-filled:after {
  content: "\e998";
  
  opacity: 0.1;
}
.ki-award.ki-filled:before {
  content: "\e999";
  position: absolute;
  
}
.ki-badge.ki-filled:after {
  content: "\e99a";
  
  opacity: 0.1;
}
.ki-badge.ki-filled:before {
  content: "\e99b";
  position: absolute;
  
}
.ki-bandage.ki-filled:after {
  content: "\e99c";
  
  opacity: 0.1;
}
.ki-bandage.ki-filled:before {
  content: "\e99d";
  position: absolute;
  
}
.ki-bank.ki-filled:after {
  content: "\e99e";
  
  opacity: 0.1;
}
.ki-bank.ki-filled:before {
  content: "\e99f";
  position: absolute;
  
}
.ki-bar-chart.ki-filled:after {
  content: "\e9a0";
  
  opacity: 0.1;
}
.ki-bar-chart.ki-filled:before {
  content: "\e9a1";
  position: absolute;
  
}
.ki-barcode.ki-filled:after {
  content: "\e9a2";
  
  opacity: 0.1;
}
.ki-barcode.ki-filled:before {
  content: "\e9a3";
  position: absolute;
  
}
.ki-basket-ok.ki-filled:after {
  content: "\e9a4";
  
  opacity: 0.1;
}
.ki-basket-ok.ki-filled:before {
  content: "\e9a5";
  position: absolute;
  
}
.ki-basket.ki-filled:after {
  content: "\e9a6";
  
  opacity: 0.1;
}
.ki-basket.ki-filled:before {
  content: "\e9a7";
  position: absolute;
  
}
.ki-behance.ki-filled:before {
  content: "\e9a8";
}
.ki-bill.ki-filled:after {
  content: "\e9a9";
  
  opacity: 0.1;
}
.ki-bill.ki-filled:before {
  content: "\e9aa";
  position: absolute;
  
}
.ki-binance-usd-busd.ki-filled:after {
  content: "\e9ab";
  
  opacity: 0.1;
}
.ki-binance-usd-busd.ki-filled:before {
  content: "\e9ac";
  position: absolute;
  
}
.ki-binance.ki-filled:after {
  content: "\e9ad";
  
  opacity: 0.1;
}
.ki-binance.ki-filled:before {
  content: "\e9ae";
  position: absolute;
  
}
.ki-bitcoin.ki-filled:after {
  content: "\e9af";
  
  opacity: 0.1;
}
.ki-bitcoin.ki-filled:before {
  content: "\e9b0";
  position: absolute;
  
}
.ki-black-down.ki-filled:before {
  content: "\e9b1";
}
.ki-black-left-line.ki-filled:before {
  content: "\e9b2";
}
.ki-black-left.ki-filled:before {
  content: "\e9b3";
}
.ki-black-right-line.ki-filled:before {
  content: "\e9b4";
}
.ki-black-right.ki-filled:before {
  content: "\e9b5";
}
.ki-black-up.ki-filled:before {
  content: "\e9b6";
}
.ki-bluetooth.ki-filled:after {
  content: "\e9b7";
  
  opacity: 0.1;
}
.ki-bluetooth.ki-filled:before {
  content: "\e9b8";
  position: absolute;
  
}
.ki-book-open.ki-filled:after {
  content: "\e9b9";
  
  opacity: 0.1;
}
.ki-book-open.ki-filled:before {
  content: "\e9ba";
  position: absolute;
  
}
.ki-book-square.ki-filled:after {
  content: "\e9bb";
  
  opacity: 0.1;
}
.ki-book-square.ki-filled:before {
  content: "\e9bc";
  position: absolute;
  
}
.ki-book.ki-filled:after {
  content: "\e9bd";
  
  opacity: 0.1;
}
.ki-book.ki-filled:before {
  content: "\e9be";
  position: absolute;
  
}
.ki-bookmark-2.ki-filled:after {
  content: "\e9bf";
  
  opacity: 0.1;
}
.ki-bookmark-2.ki-filled:before {
  content: "\e9c0";
  position: absolute;
  
}
.ki-bookmark.ki-filled:after {
  content: "\e9c1";
  
  opacity: 0.1;
}
.ki-bookmark.ki-filled:before {
  content: "\e9c2";
  position: absolute;
  
}
.ki-bootstrap.ki-filled:after {
  content: "\e9c3";
  
  opacity: 0.1;
}
.ki-bootstrap.ki-filled:before {
  content: "\e9c4";
  position: absolute;
  
}
.ki-briefcase.ki-filled:after {
  content: "\e9c5";
  
  opacity: 0.1;
}
.ki-briefcase.ki-filled:before {
  content: "\e9c6";
  position: absolute;
  
}
.ki-brifecase-cros.ki-filled:after {
  content: "\e9c7";
  
  opacity: 0.1;
}
.ki-brifecase-cros.ki-filled:before {
  content: "\e9c8";
  position: absolute;
  
}
.ki-brifecase-tick.ki-filled:after {
  content: "\e9c9";
  
  opacity: 0.1;
}
.ki-brifecase-tick.ki-filled:before {
  content: "\e9ca";
  position: absolute;
  
}
.ki-brifecase-timer.ki-filled:after {
  content: "\e9cb";
  
  opacity: 0.1;
}
.ki-brifecase-timer.ki-filled:before {
  content: "\e9cc";
  position: absolute;
  
}
.ki-brush.ki-filled:after {
  content: "\e9cd";
  
  opacity: 0.1;
}
.ki-brush.ki-filled:before {
  content: "\e9ce";
  position: absolute;
  
}
.ki-bucket-square.ki-filled:after {
  content: "\e9cf";
  
  opacity: 0.1;
}
.ki-bucket-square.ki-filled:before {
  content: "\e9d0";
  position: absolute;
  
}
.ki-bucket.ki-filled:after {
  content: "\e9d1";
  
  opacity: 0.1;
}
.ki-bucket.ki-filled:before {
  content: "\e9d2";
  position: absolute;
  
}
.ki-burger-menu-1.ki-filled:after {
  content: "\e9d3";
  
  opacity: 0.1;
}
.ki-burger-menu-1.ki-filled:before {
  content: "\e9d4";
  position: absolute;
  
}
.ki-burger-menu-2.ki-filled:after {
  content: "\e9d5";
  
  opacity: 0.1;
}
.ki-burger-menu-2.ki-filled:before {
  content: "\e9d6";
  position: absolute;
  
}
.ki-burger-menu-3.ki-filled:after {
  content: "\e9d7";
  
  opacity: 0.1;
}
.ki-burger-menu-3.ki-filled:before {
  content: "\e9d8";
  position: absolute;
  
}
.ki-burger-menu-4.ki-filled:before {
  content: "\e9d9";
}
.ki-burger-menu-5.ki-filled:before {
  content: "\e9da";
}
.ki-burger-menu-6.ki-filled:before {
  content: "\e9db";
}
.ki-burger-menu.ki-filled:after {
  content: "\e9dc";
  
  opacity: 0.1;
}
.ki-burger-menu.ki-filled:before {
  content: "\e9dd";
  position: absolute;
  
}
.ki-bus.ki-filled:after {
  content: "\e9de";
  
  opacity: 0.1;
}
.ki-bus.ki-filled:before {
  content: "\e9df";
  position: absolute;
  
}
.ki-calculator.ki-filled:after {
  content: "\e9e0";
  
  opacity: 0.1;
}
.ki-calculator.ki-filled:before {
  content: "\e9e1";
  position: absolute;
  
}
.ki-calculatoror.ki-filled:after {
  content: "\e9e2";
  
  opacity: 0.1;
}
.ki-calculatoror.ki-filled:before {
  content: "\e9e3";
  position: absolute;
  
}
.ki-calendar-2.ki-filled:after {
  content: "\e9e4";
  
  opacity: 0.1;
}
.ki-calendar-2.ki-filled:before {
  content: "\e9e5";
  position: absolute;
  
}
.ki-calendar-8.ki-filled:after {
  content: "\e9e6";
  
  opacity: 0.1;
}
.ki-calendar-8.ki-filled:before {
  content: "\e9e7";
  position: absolute;
  
}
.ki-calendar-add.ki-filled:after {
  content: "\e9e8";
  
  opacity: 0.1;
}
.ki-calendar-add.ki-filled:before {
  content: "\e9e9";
  position: absolute;
  
}
.ki-calendar-edit.ki-filled:after {
  content: "\e9ea";
  
  opacity: 0.1;
}
.ki-calendar-edit.ki-filled:before {
  content: "\e9eb";
  position: absolute;
  
}
.ki-calendar-remove.ki-filled:after {
  content: "\e9ec";
  
  opacity: 0.1;
}
.ki-calendar-remove.ki-filled:before {
  content: "\e9ed";
  position: absolute;
  
}
.ki-calendar-search.ki-filled:after {
  content: "\e9ee";
  
  opacity: 0.1;
}
.ki-calendar-search.ki-filled:before {
  content: "\e9ef";
  position: absolute;
  
}
.ki-calendar-tick.ki-filled:after {
  content: "\e9f0";
  
  opacity: 0.1;
}
.ki-calendar-tick.ki-filled:before {
  content: "\e9f1";
  position: absolute;
  
}
.ki-calendar.ki-filled:after {
  content: "\e9f2";
  
  opacity: 0.1;
}
.ki-calendar.ki-filled:before {
  content: "\e9f3";
  position: absolute;
  
}
.ki-call.ki-filled:after {
  content: "\e9f4";
  
}
.ki-call.ki-filled:before {
  content: "\e9f5";
  position: absolute;
  
  opacity: 0.1;
}
.ki-capsule.ki-filled:after {
  content: "\e9f6";
  
  opacity: 0.1;
}
.ki-capsule.ki-filled:before {
  content: "\e9f7";
  position: absolute;
  
}
.ki-car.ki-filled:after {
  content: "\e9f8";
  
  opacity: 0.1;
}
.ki-car.ki-filled:before {
  content: "\e9f9";
  position: absolute;
  
}
.ki-category.ki-filled:after {
  content: "\e9fa";
  
  opacity: 0.1;
}
.ki-category.ki-filled:before {
  content: "\e9fb";
  position: absolute;
  
}
.ki-cd.ki-filled:after {
  content: "\e9fc";
  
}
.ki-cd.ki-filled:before {
  content: "\e9fd";
  position: absolute;
  
  opacity: 0.1;
}
.ki-celsius-cel.ki-filled:after {
  content: "\e9fe";
  
  opacity: 0.1;
}
.ki-celsius-cel.ki-filled:before {
  content: "\e9ff";
  position: absolute;
  
}
.ki-chart-line-down-2.ki-filled:after {
  content: "\ea00";
  
  opacity: 0.1;
}
.ki-chart-line-down-2.ki-filled:before {
  content: "\ea01";
  position: absolute;
  
}
.ki-chart-line-down.ki-filled:before {
  content: "\ea02";
}
.ki-chart-line-star.ki-filled:after {
  content: "\ea03";
  
  opacity: 0.1;
}
.ki-chart-line-star.ki-filled:before {
  content: "\ea04";
  position: absolute;
  
}
.ki-chart-line-up-2.ki-filled:after {
  content: "\ea05";
  
  opacity: 0.1;
}
.ki-chart-line-up-2.ki-filled:before {
  content: "\ea06";
  position: absolute;
  
}
.ki-chart-line-up.ki-filled:before {
  content: "\ea07";
}
.ki-chart-line.ki-filled:after {
  content: "\ea08";
  
  opacity: 0.1;
}
.ki-chart-line.ki-filled:before {
  content: "\ea09";
  position: absolute;
  
}
.ki-chart-pie-3.ki-filled:after {
  content: "\ea0a";
  
  opacity: 0.1;
}
.ki-chart-pie-3.ki-filled:before {
  content: "\ea0b";
  position: absolute;
  
}
.ki-chart-pie-4.ki-filled:after {
  content: "\ea0c";
  
  opacity: 0.1;
}
.ki-chart-pie-4.ki-filled:before {
  content: "\ea0d";
  position: absolute;
  
}
.ki-chart-pie-simple.ki-filled:after {
  content: "\ea0e";
  
  opacity: 0.1;
}
.ki-chart-pie-simple.ki-filled:before {
  content: "\ea0f";
  position: absolute;
  
}
.ki-chart-pie-too.ki-filled:after {
  content: "\ea10";
  
  opacity: 0.1;
}
.ki-chart-pie-too.ki-filled:before {
  content: "\ea11";
  position: absolute;
  
}
.ki-chart-simple-2.ki-filled:after {
  content: "\ea12";
  
  opacity: 0.1;
}
.ki-chart-simple-2.ki-filled:before {
  content: "\ea13";
  position: absolute;
  
}
.ki-chart-simple-3.ki-filled:after {
  content: "\ea14";
  
  opacity: 0.1;
}
.ki-chart-simple-3.ki-filled:before {
  content: "\ea15";
  position: absolute;
  
}
.ki-chart-simple.ki-filled:after {
  content: "\ea16";
  
  opacity: 0.1;
}
.ki-chart-simple.ki-filled:before {
  content: "\ea17";
  position: absolute;
  
}
.ki-chart.ki-filled:after {
  content: "\ea18";
  
  opacity: 0.1;
}
.ki-chart.ki-filled:before {
  content: "\ea19";
  position: absolute;
  
}
.ki-check-circle.ki-filled:after {
  content: "\ea1a";
  
  opacity: 0.1;
}
.ki-check-circle.ki-filled:before {
  content: "\ea1b";
  position: absolute;
  
}
.ki-check-squared.ki-filled:after {
  content: "\ea1c";
  
  opacity: 0.1;
}
.ki-check-squared.ki-filled:before {
  content: "\ea1d";
  position: absolute;
  
}
.ki-check.ki-filled:before {
  content: "\ea1e";
}
.ki-cheque.ki-filled:after {
  content: "\ea1f";
  
  opacity: 0.1;
}
.ki-cheque.ki-filled:before {
  content: "\ea20";
  position: absolute;
  
}
.ki-chrome.ki-filled:after {
  content: "\ea21";
  
  opacity: 0.1;
}
.ki-chrome.ki-filled:before {
  content: "\ea22";
  position: absolute;
  
}
.ki-classmates.ki-filled:after {
  content: "\ea23";
  
  opacity: 0.1;
}
.ki-classmates.ki-filled:before {
  content: "\ea24";
  position: absolute;
  
}
.ki-click.ki-filled:after {
  content: "\ea25";
  
  opacity: 0.1;
}
.ki-click.ki-filled:before {
  content: "\ea26";
  position: absolute;
  
}
.ki-clipboard.ki-filled:after {
  content: "\ea27";
  
  opacity: 0.1;
}
.ki-clipboard.ki-filled:before {
  content: "\ea28";
  position: absolute;
  
}
.ki-cloud-add.ki-filled:after {
  content: "\ea29";
  
  opacity: 0.1;
}
.ki-cloud-add.ki-filled:before {
  content: "\ea2a";
  position: absolute;
  
}
.ki-cloud-change.ki-filled:after {
  content: "\ea2b";
  
  opacity: 0.1;
}
.ki-cloud-change.ki-filled:before {
  content: "\ea2c";
  position: absolute;
  
}
.ki-cloud-download.ki-filled:after {
  content: "\ea2d";
  
  opacity: 0.1;
}
.ki-cloud-download.ki-filled:before {
  content: "\ea2e";
  position: absolute;
  
}
.ki-cloud.ki-filled:after {
  content: "\ea2f";
  
  opacity: 0.1;
}
.ki-cloud.ki-filled:before {
  content: "\ea30";
  position: absolute;
  
}
.ki-code.ki-filled:after {
  content: "\ea31";
  
  opacity: 0.1;
}
.ki-code.ki-filled:before {
  content: "\ea32";
  position: absolute;
  
}
.ki-coffee.ki-filled:after {
  content: "\ea33";
  
  opacity: 0.1;
}
.ki-coffee.ki-filled:before {
  content: "\ea34";
  position: absolute;
  
}
.ki-color-swatch.ki-filled:after {
  content: "\ea35";
  
  opacity: 0.1;
}
.ki-color-swatch.ki-filled:before {
  content: "\ea36";
  position: absolute;
  
}
.ki-colors-square.ki-filled:after {
  content: "\ea37";
  
  opacity: 0.1;
}
.ki-colors-square.ki-filled:before {
  content: "\ea38";
  position: absolute;
  
}
.ki-compass.ki-filled:after {
  content: "\ea39";
  
  opacity: 0.1;
}
.ki-compass.ki-filled:before {
  content: "\ea3a";
  position: absolute;
  
}
.ki-copy-success.ki-filled:after {
  content: "\ea3b";
  
  opacity: 0.1;
}
.ki-copy-success.ki-filled:before {
  content: "\ea3c";
  position: absolute;
  
}
.ki-copy.ki-filled:after {
  content: "\ea3d";
  
  opacity: 0.1;
}
.ki-copy.ki-filled:before {
  content: "\ea3e";
  position: absolute;
  
}
.ki-courier-express.ki-filled:after {
  content: "\ea3f";
  
  opacity: 0.1;
}
.ki-courier-express.ki-filled:before {
  content: "\ea40";
  position: absolute;
  
}
.ki-courier.ki-filled:after {
  content: "\ea41";
  
  opacity: 0.1;
}
.ki-courier.ki-filled:before {
  content: "\ea42";
  position: absolute;
  
}
.ki-credit-cart.ki-filled:after {
  content: "\ea43";
  
  opacity: 0.1;
}
.ki-credit-cart.ki-filled:before {
  content: "\ea44";
  position: absolute;
  
}
.ki-cross-circle.ki-filled:after {
  content: "\ea45";
  
  opacity: 0.1;
}
.ki-cross-circle.ki-filled:before {
  content: "\ea46";
  position: absolute;
  
}
.ki-cross-square.ki-filled:after {
  content: "\ea47";
  
  opacity: 0.1;
}
.ki-cross-square.ki-filled:before {
  content: "\ea48";
  position: absolute;
  
}
.ki-cross.ki-filled:before {
  content: "\ea49";
}
.ki-crown-2.ki-filled:after {
  content: "\ea4a";
  
  opacity: 0.1;
}
.ki-crown-2.ki-filled:before {
  content: "\ea4b";
  position: absolute;
  
}
.ki-crown.ki-filled:after {
  content: "\ea4c";
  
  opacity: 0.1;
}
.ki-crown.ki-filled:before {
  content: "\ea4d";
  position: absolute;
  
}
.ki-css.ki-filled:after {
  content: "\ea4e";
  
  opacity: 0.1;
}
.ki-css.ki-filled:before {
  content: "\ea4f";
  position: absolute;
  
}
.ki-cube-2.ki-filled:after {
  content: "\ea50";
  
  opacity: 0.1;
}
.ki-cube-2.ki-filled:before {
  content: "\ea51";
  position: absolute;
  
}
.ki-cube-3.ki-filled:after {
  content: "\ea52";
  
  opacity: 0.1;
}
.ki-cube-3.ki-filled:before {
  content: "\ea53";
  position: absolute;
  
}
.ki-cup.ki-filled:after {
  content: "\ea54";
  
  opacity: 0.1;
}
.ki-cup.ki-filled:before {
  content: "\ea55";
  position: absolute;
  
}
.ki-cursor.ki-filled:after {
  content: "\ea56";
  
  opacity: 0.1;
}
.ki-cursor.ki-filled:before {
  content: "\ea57";
  position: absolute;
  
}
.ki-dash.ki-filled:after {
  content: "\ea58";
  
  opacity: 0.1;
}
.ki-dash.ki-filled:before {
  content: "\ea59";
  position: absolute;
  
}
.ki-data.ki-filled:after {
  content: "\ea5a";
  
  opacity: 0.1;
}
.ki-data.ki-filled:before {
  content: "\ea5b";
  position: absolute;
  
}
.ki-delete-files.ki-filled:after {
  content: "\ea5c";
  
  opacity: 0.1;
}
.ki-delete-files.ki-filled:before {
  content: "\ea5d";
  position: absolute;
  
}
.ki-delete-folder.ki-filled:after {
  content: "\ea5e";
  
  opacity: 0.1;
}
.ki-delete-folder.ki-filled:before {
  content: "\ea5f";
  position: absolute;
  
}
.ki-delivery-2.ki-filled:after {
  content: "\ea60";
  
  opacity: 0.1;
}
.ki-delivery-2.ki-filled:before {
  content: "\ea61";
  position: absolute;
  
}
.ki-delivery-3.ki-filled:after {
  content: "\ea62";
  
  opacity: 0.1;
}
.ki-delivery-3.ki-filled:before {
  content: "\ea63";
  position: absolute;
  
}
.ki-delivery-24.ki-filled:after {
  content: "\ea64";
  
  opacity: 0.1;
}
.ki-delivery-24.ki-filled:before {
  content: "\ea65";
  position: absolute;
  
}
.ki-delivery-door.ki-filled:after {
  content: "\ea66";
  
  opacity: 0.1;
}
.ki-delivery-door.ki-filled:before {
  content: "\ea67";
  position: absolute;
  
}
.ki-delivery-geolocation.ki-filled:after {
  content: "\ea68";
  
  opacity: 0.1;
}
.ki-delivery-geolocation.ki-filled:before {
  content: "\ea69";
  position: absolute;
  
}
.ki-delivery-time.ki-filled:after {
  content: "\ea6a";
  
  opacity: 0.1;
}
.ki-delivery-time.ki-filled:before {
  content: "\ea6b";
  position: absolute;
  
}
.ki-delivery.ki-filled:after {
  content: "\ea6c";
  
  opacity: 0.1;
}
.ki-delivery.ki-filled:before {
  content: "\ea6d";
  position: absolute;
  
}
.ki-design-1.ki-filled:after {
  content: "\ea6e";
  
  opacity: 0.1;
}
.ki-design-1.ki-filled:before {
  content: "\ea6f";
  position: absolute;
  
}
.ki-design-2.ki-filled:after {
  content: "\ea70";
  
  opacity: 0.1;
}
.ki-design-2.ki-filled:before {
  content: "\ea71";
  position: absolute;
  
}
.ki-desktop-mobile.ki-filled:after {
  content: "\ea72";
  
  opacity: 0.1;
}
.ki-desktop-mobile.ki-filled:before {
  content: "\ea73";
  position: absolute;
  
}
.ki-devices-2.ki-filled:after {
  content: "\ea74";
  
  opacity: 0.1;
}
.ki-devices-2.ki-filled:before {
  content: "\ea75";
  position: absolute;
  
}
.ki-devices.ki-filled:after {
  content: "\ea76";
  
  opacity: 0.1;
}
.ki-devices.ki-filled:before {
  content: "\ea77";
  position: absolute;
  
}
.ki-diamonds.ki-filled:after {
  content: "\ea78";
  
  opacity: 0.1;
}
.ki-diamonds.ki-filled:before {
  content: "\ea79";
  position: absolute;
  
}
.ki-directbox-default.ki-filled:after {
  content: "\ea7a";
  
  opacity: 0.1;
}
.ki-directbox-default.ki-filled:before {
  content: "\ea7b";
  position: absolute;
  
}
.ki-disconnect.ki-filled:after {
  content: "\ea7c";
  
  opacity: 0.1;
}
.ki-disconnect.ki-filled:before {
  content: "\ea7d";
  position: absolute;
  
}
.ki-discount.ki-filled:after {
  content: "\ea7e";
  
  opacity: 0.1;
}
.ki-discount.ki-filled:before {
  content: "\ea7f";
  position: absolute;
  
}
.ki-disguise.ki-filled:after {
  content: "\ea80";
  
  opacity: 0.1;
}
.ki-disguise.ki-filled:before {
  content: "\ea81";
  position: absolute;
  
}
.ki-disk.ki-filled:after {
  content: "\ea82";
  
  opacity: 0.1;
}
.ki-disk.ki-filled:before {
  content: "\ea83";
  position: absolute;
  
}
.ki-dislike.ki-filled:after {
  content: "\ea84";
  
  opacity: 0.1;
}
.ki-dislike.ki-filled:before {
  content: "\ea85";
  position: absolute;
  
}
.ki-dj.ki-filled:after {
  content: "\ea86";
  
  opacity: 0.1;
}
.ki-dj.ki-filled:before {
  content: "\ea87";
  position: absolute;
  
}
.ki-document.ki-filled:after {
  content: "\ea88";
  
  opacity: 0.1;
}
.ki-document.ki-filled:before {
  content: "\ea89";
  position: absolute;
  
}
.ki-double-check.ki-filled:before {
  content: "\ea8a";
}
.ki-dollar.ki-filled:after {
  content: "\ea8b";
  
  opacity: 0.1;
}
.ki-dollar.ki-filled:before {
  content: "\ea8c";
  position: absolute;
  
}
.ki-dots-circle-vertical.ki-filled:after {
  content: "\ea8d";
  
  opacity: 0.1;
}
.ki-dots-circle-vertical.ki-filled:before {
  content: "\ea8e";
  position: absolute;
  
}
.ki-dots-circle.ki-filled:after {
  content: "\ea8f";
  
  opacity: 0.1;
}
.ki-dots-circle.ki-filled:before {
  content: "\ea90";
  position: absolute;
  
}
.ki-dots-horizontal.ki-filled:before {
  content: "\ea91";
}
.ki-dots-square-vertical.ki-filled:after {
  content: "\ea92";
  
  opacity: 0.1;
}
.ki-dots-square-vertical.ki-filled:before {
  content: "\ea93";
  position: absolute;
  
}
.ki-dots-square.ki-filled:after {
  content: "\ea94";
  
  opacity: 0.1;
}
.ki-dots-square.ki-filled:before {
  content: "\ea95";
  position: absolute;
  
}
.ki-dots-vertical.ki-filled:before {
  content: "\ea96";
}
.ki-double-check-circle.ki-filled:after {
  content: "\ea97";
  
  opacity: 0.1;
}
.ki-double-check-circle.ki-filled:before {
  content: "\ea98";
  position: absolute;
  
}
.ki-double-down.ki-filled:before {
  content: "\ea99";
}
.ki-double-left-arrow.ki-filled:after {
  content: "\ea9a";
  
  opacity: 0.1;
}
.ki-double-left-arrow.ki-filled:before {
  content: "\ea9b";
  position: absolute;
  
}
.ki-double-left.ki-filled:before {
  content: "\ea9c";
}
.ki-double-right-arrow.ki-filled:after {
  content: "\ea9d";
  
  opacity: 0.1;
}
.ki-double-right-arrow.ki-filled:before {
  content: "\ea9e";
  position: absolute;
  
}
.ki-double-right.ki-filled:before {
  content: "\ea9f";
}
.ki-double-up.ki-filled:before {
  content: "\eaa0";
}
.ki-down-square.ki-filled:after {
  content: "\eaa1";
  
  opacity: 0.1;
}
.ki-down-square.ki-filled:before {
  content: "\eaa2";
  position: absolute;
  
}
.ki-down.ki-filled:before {
  content: "\eaa3";
}
.ki-dribbble.ki-filled:after {
  content: "\eaa4";
  
  opacity: 0.1;
}
.ki-dribbble.ki-filled:before {
  content: "\eaa5";
  position: absolute;
  
}
.ki-drop.ki-filled:after {
  content: "\eaa6";
  
  opacity: 0.1;
}
.ki-drop.ki-filled:before {
  content: "\eaa7";
  position: absolute;
  
}
.ki-dropbox.ki-filled:after {
  content: "\eaa8";
  
  opacity: 0.1;
}
.ki-dropbox.ki-filled:before {
  content: "\eaa9";
  position: absolute;
  
}
.ki-educare-ekt.ki-filled:after {
  content: "\eaaa";
  
  opacity: 0.1;
}
.ki-educare-ekt.ki-filled:before {
  content: "\eaab";
  position: absolute;
  
}
.ki-electricity.ki-filled:after {
  content: "\eaac";
  
  opacity: 0.1;
}
.ki-electricity.ki-filled:before {
  content: "\eaad";
  position: absolute;
  
}
.ki-electronic-clock.ki-filled:after {
  content: "\eaae";
  
  opacity: 0.1;
}
.ki-electronic-clock.ki-filled:before {
  content: "\eaaf";
  position: absolute;
  
}
.ki-element-1.ki-filled:after {
  content: "\eab0";
  
  opacity: 0.1;
}
.ki-element-1.ki-filled:before {
  content: "\eab1";
  position: absolute;
  
}
.ki-element-2.ki-filled:after {
  content: "\eab2";
  
  opacity: 0.1;
}
.ki-element-2.ki-filled:before {
  content: "\eab3";
  position: absolute;
  
}
.ki-element-3.ki-filled:after {
  content: "\eab4";
  
  opacity: 0.1;
}
.ki-element-3.ki-filled:before {
  content: "\eab5";
  position: absolute;
  
}
.ki-element-4.ki-filled:after {
  content: "\eab6";
  
  opacity: 0.1;
}
.ki-element-4.ki-filled:before {
  content: "\eab7";
  position: absolute;
  
}
.ki-element-5.ki-filled:after {
  content: "\eab8";
  
  opacity: 0.1;
}
.ki-element-5.ki-filled:before {
  content: "\eab9";
  position: absolute;
  
}
.ki-element-6.ki-filled:after {
  content: "\eaba";
  
  opacity: 0.1;
}
.ki-element-6.ki-filled:before {
  content: "\eabb";
  position: absolute;
  
}
.ki-element-7.ki-filled:after {
  content: "\eabc";
  
  opacity: 0.1;
}
.ki-element-7.ki-filled:before {
  content: "\eabd";
  position: absolute;
  
}
.ki-element-8.ki-filled:after {
  content: "\eabe";
  
  opacity: 0.1;
}
.ki-element-8.ki-filled:before {
  content: "\eabf";
  position: absolute;
  
}
.ki-element-9.ki-filled:after {
  content: "\eac0";
  
  opacity: 0.1;
}
.ki-element-9.ki-filled:before {
  content: "\eac1";
  position: absolute;
  
}
.ki-element-10.ki-filled:after {
  content: "\eac2";
  
  opacity: 0.1;
}
.ki-element-10.ki-filled:before {
  content: "\eac3";
  position: absolute;
  
}
.ki-element-11.ki-filled:after {
  content: "\eac4";
  
  opacity: 0.1;
}
.ki-element-11.ki-filled:before {
  content: "\eac5";
  position: absolute;
  
}
.ki-element-12.ki-filled:after {
  content: "\eac6";
  
  opacity: 0.1;
}
.ki-element-12.ki-filled:before {
  content: "\eac7";
  position: absolute;
  
}
.ki-element-equal.ki-filled:after {
  content: "\eac8";
  
  opacity: 0.1;
}
.ki-element-equal.ki-filled:before {
  content: "\eac9";
  position: absolute;
  
}
.ki-element-plus.ki-filled:after {
  content: "\eaca";
  
  opacity: 0.1;
}
.ki-element-plus.ki-filled:before {
  content: "\eacb";
  position: absolute;
  
}
.ki-emoji-happy.ki-filled:after {
  content: "\eacc";
  
  opacity: 0.1;
}
.ki-emoji-happy.ki-filled:before {
  content: "\eacd";
  position: absolute;
  
}
.ki-enjin-coin-enj.ki-filled:after {
  content: "\eace";
  
  opacity: 0.1;
}
.ki-enjin-coin-enj.ki-filled:before {
  content: "\eacf";
  position: absolute;
  
}
.ki-ensure.ki-filled:after {
  content: "\ead0";
  
  opacity: 0.1;
}
.ki-ensure.ki-filled:before {
  content: "\ead1";
  position: absolute;
  
}
.ki-entrance-left.ki-filled:after {
  content: "\ead2";
  
  opacity: 0.1;
}
.ki-entrance-left.ki-filled:before {
  content: "\ead3";
  position: absolute;
  
}
.ki-entrance-right.ki-filled:after {
  content: "\ead4";
  
  opacity: 0.1;
}
.ki-entrance-right.ki-filled:before {
  content: "\ead5";
  position: absolute;
  
}
.ki-eraser.ki-filled:after {
  content: "\ead6";
  
  opacity: 0.1;
}
.ki-eraser.ki-filled:before {
  content: "\ead7";
  position: absolute;
  
}
.ki-euro.ki-filled:after {
  content: "\ead8";
  
  opacity: 0.1;
}
.ki-euro.ki-filled:before {
  content: "\ead9";
  position: absolute;
  
}
.ki-exit-down.ki-filled:after {
  content: "\eada";
  
  opacity: 0.1;
}
.ki-exit-down.ki-filled:before {
  content: "\eadb";
  position: absolute;
  
}
.ki-exit-left.ki-filled:after {
  content: "\eadc";
  
  opacity: 0.1;
}
.ki-exit-left.ki-filled:before {
  content: "\eadd";
  position: absolute;
  
}
.ki-exit-right-corner.ki-filled:after {
  content: "\eade";
  
  opacity: 0.1;
}
.ki-exit-right-corner.ki-filled:before {
  content: "\eadf";
  position: absolute;
  
}
.ki-exit-right.ki-filled:after {
  content: "\eae0";
  
  opacity: 0.1;
}
.ki-exit-right.ki-filled:before {
  content: "\eae1";
  position: absolute;
  
}
.ki-exit-up.ki-filled:after {
  content: "\eae2";
  
  opacity: 0.1;
}
.ki-exit-up.ki-filled:before {
  content: "\eae3";
  position: absolute;
  
}
.ki-external-drive.ki-filled:after {
  content: "\eae4";
  
  opacity: 0.1;
}
.ki-external-drive.ki-filled:before {
  content: "\eae5";
  position: absolute;
  
}
.ki-eye-slash.ki-filled:after {
  content: "\eae6";
  
  opacity: 0.1;
}
.ki-eye-slash.ki-filled:before {
  content: "\eae7";
  position: absolute;
  
}
.ki-eye.ki-filled:after {
  content: "\eae8";
  
  opacity: 0.1;
}
.ki-eye.ki-filled:before {
  content: "\eae9";
  position: absolute;
  
}
.ki-face-id.ki-filled:after {
  content: "\eaea";
  
  opacity: 0.1;
}
.ki-face-id.ki-filled:before {
  content: "\eaeb";
  position: absolute;
  
}
.ki-facebook.ki-filled:after {
  content: "\eaec";
  
  opacity: 0.1;
}
.ki-facebook.ki-filled:before {
  content: "\eaed";
  position: absolute;
  
}
.ki-fasten.ki-filled:after {
  content: "\eaee";
  
  opacity: 0.1;
}
.ki-fasten.ki-filled:before {
  content: "\eaef";
  position: absolute;
  
}
.ki-fatrows.ki-filled:after {
  content: "\eaf0";
  
  opacity: 0.1;
}
.ki-fatrows.ki-filled:before {
  content: "\eaf1";
  position: absolute;
  
}
.ki-feather.ki-filled:after {
  content: "\eaf2";
  
  opacity: 0.1;
}
.ki-feather.ki-filled:before {
  content: "\eaf3";
  position: absolute;
  
}
.ki-figma.ki-filled:after {
  content: "\eaf4";
  
  opacity: 0.1;
}
.ki-figma.ki-filled:before {
  content: "\eaf5";
  position: absolute;
  
}
.ki-file-added.ki-filled:after {
  content: "\eaf6";
  
  opacity: 0.1;
}
.ki-file-added.ki-filled:before {
  content: "\eaf7";
  position: absolute;
  
}
.ki-file-deleted.ki-filled:after {
  content: "\eaf8";
  
  opacity: 0.1;
}
.ki-file-deleted.ki-filled:before {
  content: "\eaf9";
  position: absolute;
  
}
.ki-file-down.ki-filled:after {
  content: "\eafa";
  
  opacity: 0.1;
}
.ki-file-down.ki-filled:before {
  content: "\eafb";
  position: absolute;
  
}
.ki-file-left.ki-filled:after {
  content: "\eafc";
  
  opacity: 0.1;
}
.ki-file-left.ki-filled:before {
  content: "\eafd";
  position: absolute;
  
}
.ki-file-right.ki-filled:after {
  content: "\eafe";
  
  opacity: 0.1;
}
.ki-file-right.ki-filled:before {
  content: "\eaff";
  position: absolute;
  
}
.ki-file-sheet.ki-filled:after {
  content: "\eb00";
  
  opacity: 0.1;
}
.ki-file-sheet.ki-filled:before {
  content: "\eb01";
  position: absolute;
  
}
.ki-file-up.ki-filled:after {
  content: "\eb02";
  
  opacity: 0.1;
}
.ki-file-up.ki-filled:before {
  content: "\eb03";
  position: absolute;
  
}
.ki-files.ki-filled:after {
  content: "\eb04";
  
  opacity: 0.1;
}
.ki-files.ki-filled:before {
  content: "\eb05";
  position: absolute;
  
}
.ki-filter-edit.ki-filled:after {
  content: "\eb06";
  
  opacity: 0.1;
}
.ki-filter-edit.ki-filled:before {
  content: "\eb07";
  position: absolute;
  
}
.ki-filter-search.ki-filled:after {
  content: "\eb08";
  
  opacity: 0.1;
}
.ki-filter-search.ki-filled:before {
  content: "\eb09";
  position: absolute;
  
}
.ki-filter-square.ki-filled:after {
  content: "\eb0a";
  
  opacity: 0.1;
}
.ki-filter-square.ki-filled:before {
  content: "\eb0b";
  position: absolute;
  
}
.ki-filter-tablet.ki-filled:after {
  content: "\eb0c";
  
  opacity: 0.1;
}
.ki-filter-tablet.ki-filled:before {
  content: "\eb0d";
  position: absolute;
  
}
.ki-filter-tick.ki-filled:after {
  content: "\eb0e";
  
  opacity: 0.1;
}
.ki-filter-tick.ki-filled:before {
  content: "\eb0f";
  position: absolute;
  
}
.ki-filter.ki-filled:after {
  content: "\eb10";
  
  opacity: 0.1;
}
.ki-filter.ki-filled:before {
  content: "\eb11";
  position: absolute;
  
}
.ki-financial-schedule.ki-filled:after {
  content: "\eb12";
  
  opacity: 0.1;
}
.ki-financial-schedule.ki-filled:before {
  content: "\eb13";
  position: absolute;
  
}
.ki-fingerprint-scanning.ki-filled:before {
  content: "\eb14";
}
.ki-flag.ki-filled:after {
  content: "\eb15";
  
  opacity: 0.1;
}
.ki-flag.ki-filled:before {
  content: "\eb16";
  position: absolute;
  
}
.ki-flash-circle.ki-filled:after {
  content: "\eb17";
  
  opacity: 0.1;
}
.ki-flash-circle.ki-filled:before {
  content: "\eb18";
  position: absolute;
  
}
.ki-flask.ki-filled:after {
  content: "\eb19";
  
  opacity: 0.1;
}
.ki-flask.ki-filled:before {
  content: "\eb1a";
  position: absolute;
  
}
.ki-focus.ki-filled:after {
  content: "\eb1b";
  
  opacity: 0.1;
}
.ki-focus.ki-filled:before {
  content: "\eb1c";
  position: absolute;
  
}
.ki-folder-added.ki-filled:after {
  content: "\eb1d";
  
  opacity: 0.1;
}
.ki-folder-added.ki-filled:before {
  content: "\eb1e";
  position: absolute;
  
}
.ki-folder-down.ki-filled:after {
  content: "\eb1f";
  
  opacity: 0.1;
}
.ki-folder-down.ki-filled:before {
  content: "\eb20";
  position: absolute;
  
}
.ki-folder-up.ki-filled:after {
  content: "\eb21";
  
  opacity: 0.1;
}
.ki-folder-up.ki-filled:before {
  content: "\eb22";
  position: absolute;
  
}
.ki-folder.ki-filled:after {
  content: "\eb23";
  
  opacity: 0.1;
}
.ki-folder.ki-filled:before {
  content: "\eb24";
  position: absolute;
  
}
.ki-frame.ki-filled:after {
  content: "\eb25";
  
  opacity: 0.1;
}
.ki-frame.ki-filled:before {
  content: "\eb26";
  position: absolute;
  
}
.ki-geolocation-home.ki-filled:after {
  content: "\eb27";
  
  opacity: 0.1;
}
.ki-geolocation-home.ki-filled:before {
  content: "\eb28";
  position: absolute;
  
}
.ki-geolocation.ki-filled:after {
  content: "\eb29";
  
  opacity: 0.1;
}
.ki-geolocation.ki-filled:before {
  content: "\eb2a";
  position: absolute;
  
}
.ki-ghost.ki-filled:after {
  content: "\eb2b";
  
  opacity: 0.1;
}
.ki-ghost.ki-filled:before {
  content: "\eb2c";
  position: absolute;
  
}
.ki-gift.ki-filled:after {
  content: "\eb2d";
  
  opacity: 0.1;
}
.ki-gift.ki-filled:before {
  content: "\eb2e";
  position: absolute;
  
}
.ki-github.ki-filled:after {
  content: "\eb2f";
  
  opacity: 0.1;
}
.ki-github.ki-filled:before {
  content: "\eb30";
  position: absolute;
  
}
.ki-glass.ki-filled:after {
  content: "\eb31";
  
  opacity: 0.1;
}
.ki-glass.ki-filled:before {
  content: "\eb32";
  position: absolute;
  
}
.ki-google-play.ki-filled:after {
  content: "\eb33";
  
  opacity: 0.1;
}
.ki-google-play.ki-filled:before {
  content: "\eb34";
  position: absolute;
  
}
.ki-google.ki-filled:after {
  content: "\eb35";
  
  opacity: 0.1;
}
.ki-google.ki-filled:before {
  content: "\eb36";
  position: absolute;
  
}
.ki-graph-2.ki-filled:after {
  content: "\eb37";
  
  opacity: 0.1;
}
.ki-graph-2.ki-filled:before {
  content: "\eb38";
  position: absolute;
  
}
.ki-graph-3.ki-filled:after {
  content: "\eb39";
  
  opacity: 0.1;
}
.ki-graph-3.ki-filled:before {
  content: "\eb3a";
  position: absolute;
  
}
.ki-graph-4.ki-filled:after {
  content: "\eb3b";
  
  opacity: 0.1;
}
.ki-graph-4.ki-filled:before {
  content: "\eb3c";
  position: absolute;
  
}
.ki-graph-up.ki-filled:after {
  content: "\eb3d";
  
  opacity: 0.1;
}
.ki-graph-up.ki-filled:before {
  content: "\eb3e";
  position: absolute;
  
}
.ki-graph.ki-filled:after {
  content: "\eb3f";
  
  opacity: 0.1;
}
.ki-graph.ki-filled:before {
  content: "\eb40";
  position: absolute;
  
}
.ki-grid-2.ki-filled:after {
  content: "\eb41";
  
  opacity: 0.1;
}
.ki-grid-2.ki-filled:before {
  content: "\eb42";
  position: absolute;
  
}
.ki-grid.ki-filled:after {
  content: "\eb43";
  
  opacity: 0.1;
}
.ki-grid.ki-filled:before {
  content: "\eb44";
  position: absolute;
  
}
.ki-handcart.ki-filled:after {
  content: "\eb45";
  
  opacity: 0.1;
}
.ki-handcart.ki-filled:before {
  content: "\eb46";
  position: absolute;
  
}
.ki-happyemoji.ki-filled:after {
  content: "\eb47";
  
  opacity: 0.1;
}
.ki-happyemoji.ki-filled:before {
  content: "\eb48";
  position: absolute;
  
}
.ki-heart-circle.ki-filled:after {
  content: "\eb49";
  
  opacity: 0.1;
}
.ki-heart-circle.ki-filled:before {
  content: "\eb4a";
  position: absolute;
  
}
.ki-heart.ki-filled:after {
  content: "\eb4b";
  
  opacity: 0.1;
}
.ki-heart.ki-filled:before {
  content: "\eb4c";
  position: absolute;
  
}
.ki-home-1.ki-filled:after {
  content: "\eb4d";
  
  opacity: 0.1;
}
.ki-home-1.ki-filled:before {
  content: "\eb4e";
  position: absolute;
  
}
.ki-home-2.ki-filled:after {
  content: "\eb4f";
  
  opacity: 0.1;
}
.ki-home-2.ki-filled:before {
  content: "\eb50";
  position: absolute;
  
}
.ki-home-3.ki-filled:after {
  content: "\eb51";
  
  opacity: 0.1;
}
.ki-home-3.ki-filled:before {
  content: "\eb52";
  position: absolute;
  
}
.ki-home.ki-filled:after {
  content: "\eb53";
  
  opacity: 0.1;
}
.ki-home.ki-filled:before {
  content: "\eb54";
  position: absolute;
  
}
.ki-html.ki-filled:after {
  content: "\eb55";
  
  opacity: 0.1;
}
.ki-html.ki-filled:before {
  content: "\eb56";
  position: absolute;
  
}
.ki-icon.ki-filled:after {
  content: "\eb57";
  
  opacity: 0.1;
}
.ki-icon.ki-filled:before {
  content: "\eb58";
  position: absolute;
  
}
.ki-illustrator.ki-filled:after {
  content: "\eb59";
  
  opacity: 0.1;
}
.ki-illustrator.ki-filled:before {
  content: "\eb5a";
  position: absolute;
  
}
.ki-information-1.ki-filled:after {
  content: "\eb5b";
  
  opacity: 0.1;
}
.ki-information-1.ki-filled:before {
  content: "\eb5c";
  position: absolute;
  
}
.ki-information-2.ki-filled:after {
  content: "\eb5d";
  
  opacity: 0.1;
}
.ki-information-2.ki-filled:before {
  content: "\eb5e";
  position: absolute;
  
}
.ki-information-3.ki-filled:after {
  content: "\eb5f";
  
  opacity: 0.1;
}
.ki-information-3.ki-filled:before {
  content: "\eb60";
  position: absolute;
  
}
.ki-information-4.ki-filled:after {
  content: "\eb61";
  
  opacity: 0.1;
}
.ki-information-4.ki-filled:before {
  content: "\eb62";
  position: absolute;
  
}
.ki-information.ki-filled:after {
  content: "\eb63";
  
  opacity: 0.1;
}
.ki-information.ki-filled:before {
  content: "\eb64";
  position: absolute;
  
}
.ki-instagram.ki-filled:after {
  content: "\eb65";
  
  opacity: 0.1;
}
.ki-instagram.ki-filled:before {
  content: "\eb66";
  position: absolute;
  
}
.ki-joystick.ki-filled:after {
  content: "\eb67";
  
  opacity: 0.1;
}
.ki-joystick.ki-filled:before {
  content: "\eb68";
  position: absolute;
  
}
.ki-js-2.ki-filled:after {
  content: "\eb69";
  
  opacity: 0.1;
}
.ki-js-2.ki-filled:before {
  content: "\eb6a";
  position: absolute;
  
}
.ki-js.ki-filled:after {
  content: "\eb6b";
  
  opacity: 0.1;
}
.ki-js.ki-filled:before {
  content: "\eb6c";
  position: absolute;
  
}
.ki-kanban.ki-filled:after {
  content: "\eb6d";
  
  opacity: 0.1;
}
.ki-kanban.ki-filled:before {
  content: "\eb6e";
  position: absolute;
  
}
.ki-key-square.ki-filled:after {
  content: "\eb6f";
  
  opacity: 0.1;
}
.ki-key-square.ki-filled:before {
  content: "\eb70";
  position: absolute;
  
}
.ki-key.ki-filled:after {
  content: "\eb71";
  
  opacity: 0.1;
}
.ki-key.ki-filled:before {
  content: "\eb72";
  position: absolute;
  
}
.ki-keyboard.ki-filled:after {
  content: "\eb73";
  
  opacity: 0.1;
}
.ki-keyboard.ki-filled:before {
  content: "\eb74";
  position: absolute;
  
}
.ki-laptop.ki-filled:after {
  content: "\eb75";
  
  opacity: 0.1;
}
.ki-laptop.ki-filled:before {
  content: "\eb76";
  position: absolute;
  
}
.ki-laravel.ki-filled:after {
  content: "\eb77";
  
  opacity: 0.1;
}
.ki-laravel.ki-filled:before {
  content: "\eb78";
  position: absolute;
  
}
.ki-left-square.ki-filled:after {
  content: "\eb79";
  
  opacity: 0.1;
}
.ki-left-square.ki-filled:before {
  content: "\eb7a";
  position: absolute;
  
}
.ki-left.ki-filled:before {
  content: "\eb7b";
}
.ki-like-2.ki-filled:after {
  content: "\eb7c";
  
  opacity: 0.1;
}
.ki-like-2.ki-filled:before {
  content: "\eb7d";
  position: absolute;
  
}
.ki-like-folder.ki-filled:after {
  content: "\eb7e";
  
  opacity: 0.1;
}
.ki-like-folder.ki-filled:before {
  content: "\eb7f";
  position: absolute;
  
}
.ki-like-shapes.ki-filled:after {
  content: "\eb80";
  
  opacity: 0.1;
}
.ki-like-shapes.ki-filled:before {
  content: "\eb81";
  position: absolute;
  
}
.ki-like-tag.ki-filled:after {
  content: "\eb82";
  
  opacity: 0.1;
}
.ki-like-tag.ki-filled:before {
  content: "\eb83";
  position: absolute;
  
}
.ki-like.ki-filled:after {
  content: "\eb84";
  
  opacity: 0.1;
}
.ki-like.ki-filled:before {
  content: "\eb85";
  position: absolute;
  
}
.ki-loading.ki-filled:before {
  content: "\eb86";
}
.ki-lock-2.ki-filled:after {
  content: "\eb87";
  
  opacity: 0.1;
}
.ki-lock-2.ki-filled:before {
  content: "\eb88";
  position: absolute;
  
}
.ki-lock-3.ki-filled:after {
  content: "\eb89";
  
  opacity: 0.1;
}
.ki-lock-3.ki-filled:before {
  content: "\eb8a";
  position: absolute;
  
}
.ki-lock.ki-filled:after {
  content: "\eb8b";
  
  opacity: 0.1;
}
.ki-lock.ki-filled:before {
  content: "\eb8c";
  position: absolute;
  
}
.ki-logistic.ki-filled:after {
  content: "\eb8d";
  
  opacity: 0.1;
}
.ki-logistic.ki-filled:before {
  content: "\eb8e";
  position: absolute;
  
}
.ki-lots-shopping.ki-filled:after {
  content: "\eb8f";
  
  opacity: 0.1;
}
.ki-lots-shopping.ki-filled:before {
  content: "\eb90";
  position: absolute;
  
}
.ki-lovely.ki-filled:after {
  content: "\eb91";
  
  opacity: 0.1;
}
.ki-lovely.ki-filled:before {
  content: "\eb92";
  position: absolute;
  
}
.ki-lts.ki-filled:after {
  content: "\eb93";
  
  opacity: 0.1;
}
.ki-lts.ki-filled:before {
  content: "\eb94";
  position: absolute;
  
}
.ki-magnifier.ki-filled:after {
  content: "\eb95";
  
  opacity: 0.1;
}
.ki-magnifier.ki-filled:before {
  content: "\eb96";
  position: absolute;
  
}
.ki-map.ki-filled:after {
  content: "\eb97";
  
  opacity: 0.1;
}
.ki-map.ki-filled:before {
  content: "\eb98";
  position: absolute;
  
}
.ki-mask.ki-filled:after {
  content: "\eb99";
  
  opacity: 0.1;
}
.ki-mask.ki-filled:before {
  content: "\eb9a";
  position: absolute;
  
}
.ki-maximize.ki-filled:after {
  content: "\eb9b";
  
  opacity: 0.1;
}
.ki-maximize.ki-filled:before {
  content: "\eb9c";
  position: absolute;
  
}
.ki-medal-star.ki-filled:after {
  content: "\eb9d";
  
  opacity: 0.1;
}
.ki-medal-star.ki-filled:before {
  content: "\eb9e";
  position: absolute;
  
}
.ki-menu.ki-filled:after {
  content: "\eb9f";
  
  opacity: 0.1;
}
.ki-menu.ki-filled:before {
  content: "\eba0";
  position: absolute;
  
}
.ki-message-add.ki-filled:after {
  content: "\eba1";
  
  opacity: 0.1;
}
.ki-message-add.ki-filled:before {
  content: "\eba2";
  position: absolute;
  
}
.ki-message-edit.ki-filled:after {
  content: "\eba3";
  
  opacity: 0.1;
}
.ki-message-edit.ki-filled:before {
  content: "\eba4";
  position: absolute;
  
}
.ki-message-minus.ki-filled:after {
  content: "\eba5";
  
  opacity: 0.1;
}
.ki-message-minus.ki-filled:before {
  content: "\eba6";
  position: absolute;
  
}
.ki-message-notify.ki-filled:after {
  content: "\eba7";
  
  opacity: 0.1;
}
.ki-message-notify.ki-filled:before {
  content: "\eba8";
  position: absolute;
  
}
.ki-message-programming.ki-filled:after {
  content: "\eba9";
  
  opacity: 0.1;
}
.ki-message-programming.ki-filled:before {
  content: "\ebaa";
  position: absolute;
  
}
.ki-message-question.ki-filled:after {
  content: "\ebab";
  
  opacity: 0.1;
}
.ki-message-question.ki-filled:before {
  content: "\ebac";
  position: absolute;
  
}
.ki-message-text-2.ki-filled:after {
  content: "\ebad";
  
  opacity: 0.1;
}
.ki-message-text-2.ki-filled:before {
  content: "\ebae";
  position: absolute;
  
}
.ki-message-text.ki-filled:after {
  content: "\ebaf";
  
  opacity: 0.1;
}
.ki-message-text.ki-filled:before {
  content: "\ebb0";
  position: absolute;
  
}
.ki-messages.ki-filled:after {
  content: "\ebb1";
  
  opacity: 0.1;
}
.ki-messages.ki-filled:before {
  content: "\ebb2";
  position: absolute;
  
}
.ki-microsoft.ki-filled:after {
  content: "\ebb3";
  
  opacity: 0.1;
}
.ki-microsoft.ki-filled:before {
  content: "\ebb4";
  position: absolute;
  
}
.ki-milk.ki-filled:after {
  content: "\ebb5";
  
  opacity: 0.1;
}
.ki-milk.ki-filled:before {
  content: "\ebb6";
  position: absolute;
  
}
.ki-minus-circle.ki-filled:after {
  content: "\ebb7";
  
  opacity: 0.1;
}
.ki-minus-circle.ki-filled:before {
  content: "\ebb8";
  position: absolute;
  
}
.ki-minus-folder.ki-filled:after {
  content: "\ebb9";
  
  opacity: 0.1;
}
.ki-minus-folder.ki-filled:before {
  content: "\ebba";
  position: absolute;
  
}
.ki-minus-squared.ki-filled:after {
  content: "\ebbb";
  
  opacity: 0.1;
}
.ki-minus-squared.ki-filled:before {
  content: "\ebbc";
  position: absolute;
  
}
.ki-minus.ki-filled:before {
  content: "\ebbd";
}
.ki-moon.ki-filled:after {
  content: "\ebbe";
  
  opacity: 0.1;
}
.ki-moon.ki-filled:before {
  content: "\ebbf";
  position: absolute;
  
}
.ki-more-2.ki-filled:after {
  content: "\ebc0";
  
  opacity: 0.1;
}
.ki-more-2.ki-filled:before {
  content: "\ebc1";
  position: absolute;
  
}
.ki-mouse-circle.ki-filled:after {
  content: "\ebc2";
  
  opacity: 0.1;
}
.ki-mouse-circle.ki-filled:before {
  content: "\ebc3";
  position: absolute;
  
}
.ki-mouse-square.ki-filled:after {
  content: "\ebc4";
  
  opacity: 0.1;
}
.ki-mouse-square.ki-filled:before {
  content: "\ebc5";
  position: absolute;
  
}
.ki-mouse.ki-filled:after {
  content: "\ebc6";
  
  opacity: 0.1;
}
.ki-mouse.ki-filled:before {
  content: "\ebc7";
  position: absolute;
  
}
.ki-nexo.ki-filled:after {
  content: "\ebc8";
  
  opacity: 0.1;
}
.ki-nexo.ki-filled:before {
  content: "\ebc9";
  position: absolute;
  
}
.ki-night-day.ki-filled:after {
  content: "\ebca";
  
  opacity: 0.1;
}
.ki-night-day.ki-filled:before {
  content: "\ebcb";
  position: absolute;
  
}
.ki-note-2.ki-filled:after {
  content: "\ebcc";
  
  opacity: 0.1;
}
.ki-note-2.ki-filled:before {
  content: "\ebcd";
  position: absolute;
  
}
.ki-note.ki-filled:after {
  content: "\ebce";
  
  opacity: 0.1;
}
.ki-note.ki-filled:before {
  content: "\ebcf";
  position: absolute;
  
}
.ki-notepad-bookmark.ki-filled:after {
  content: "\ebd0";
  
  opacity: 0.1;
}
.ki-notepad-bookmark.ki-filled:before {
  content: "\ebd1";
  position: absolute;
  
}
.ki-notepad-edit.ki-filled:after {
  content: "\ebd2";
  
  opacity: 0.1;
}
.ki-notepad-edit.ki-filled:before {
  content: "\ebd3";
  position: absolute;
  
}
.ki-notepad.ki-filled:after {
  content: "\ebd4";
  
  opacity: 0.1;
}
.ki-notepad.ki-filled:before {
  content: "\ebd5";
  position: absolute;
  
}
.ki-notification-1.ki-filled:after {
  content: "\ebd6";
  
  opacity: 0.1;
}
.ki-notification-1.ki-filled:before {
  content: "\ebd7";
  position: absolute;
  
}
.ki-notification-bing.ki-filled:after {
  content: "\ebd8";
  
  opacity: 0.1;
}
.ki-notification-bing.ki-filled:before {
  content: "\ebd9";
  position: absolute;
  
}
.ki-notification-circle.ki-filled:after {
  content: "\ebda";
  
  opacity: 0.1;
}
.ki-notification-circle.ki-filled:before {
  content: "\ebdb";
  position: absolute;
  
}
.ki-notification-favorite.ki-filled:after {
  content: "\ebdc";
  
  opacity: 0.1;
}
.ki-notification-favorite.ki-filled:before {
  content: "\ebdd";
  position: absolute;
  
}
.ki-notification-on.ki-filled:after {
  content: "\ebde";
  
  opacity: 0.1;
}
.ki-notification-on.ki-filled:before {
  content: "\ebdf";
  position: absolute;
  
}
.ki-notification-status.ki-filled:after {
  content: "\ebe0";
  
  opacity: 0.1;
}
.ki-notification-status.ki-filled:before {
  content: "\ebe1";
  position: absolute;
  
}
.ki-notification.ki-filled:after {
  content: "\ebe2";
  
  opacity: 0.1;
}
.ki-notification.ki-filled:before {
  content: "\ebe3";
  position: absolute;
  
}
.ki-ocean.ki-filled:before {
  content: "\ebe4";
}
.ki-office-bag.ki-filled:after {
  content: "\ebe5";
  
  opacity: 0.1;
}
.ki-office-bag.ki-filled:before {
  content: "\ebe6";
  position: absolute;
  
}
.ki-package.ki-filled:after {
  content: "\ebe7";
  
  opacity: 0.1;
}
.ki-package.ki-filled:before {
  content: "\ebe8";
  position: absolute;
  
}
.ki-pad.ki-filled:after {
  content: "\ebe9";
  
  opacity: 0.1;
}
.ki-pad.ki-filled:before {
  content: "\ebea";
  position: absolute;
  
}
.ki-pails.ki-filled:after {
  content: "\ebeb";
  
  opacity: 0.1;
}
.ki-pails.ki-filled:before {
  content: "\ebec";
  position: absolute;
  
}
.ki-paintbucket.ki-filled:after {
  content: "\ebed";
  
  opacity: 0.1;
}
.ki-paintbucket.ki-filled:before {
  content: "\ebee";
  position: absolute;
  
}
.ki-paper-clip.ki-filled:after {
  content: "\ebef";
  
  opacity: 0.1;
}
.ki-paper-clip.ki-filled:before {
  content: "\ebf0";
  position: absolute;
  
}
.ki-paper-plane.ki-filled:after {
  content: "\ebf1";
  
  opacity: 0.1;
}
.ki-paper-plane.ki-filled:before {
  content: "\ebf2";
  position: absolute;
  
}
.ki-parcel-tracking.ki-filled:after {
  content: "\ebf3";
  
  opacity: 0.1;
}
.ki-parcel-tracking.ki-filled:before {
  content: "\ebf4";
  position: absolute;
  
}
.ki-parcel.ki-filled:after {
  content: "\ebf5";
  
  opacity: 0.1;
}
.ki-parcel.ki-filled:before {
  content: "\ebf6";
  position: absolute;
  
}
.ki-password-check.ki-filled:after {
  content: "\ebf7";
  
  opacity: 0.1;
}
.ki-password-check.ki-filled:before {
  content: "\ebf8";
  position: absolute;
  
}
.ki-paypal.ki-filled:after {
  content: "\ebf9";
  
  opacity: 0.1;
}
.ki-paypal.ki-filled:before {
  content: "\ebfa";
  position: absolute;
  
}
.ki-pencil.ki-filled:after {
  content: "\ebfb";
  
  opacity: 0.1;
}
.ki-pencil.ki-filled:before {
  content: "\ebfc";
  position: absolute;
  
}
.ki-people.ki-filled:after {
  content: "\ebfd";
  
  opacity: 0.1;
}
.ki-people.ki-filled:before {
  content: "\ebfe";
  position: absolute;
  
}
.ki-percentage.ki-filled:after {
  content: "\ebff";
  
  opacity: 0.1;
}
.ki-percentage.ki-filled:before {
  content: "\ec00";
  position: absolute;
  
}
.ki-phone.ki-filled:after {
  content: "\ec01";
  
  opacity: 0.1;
}
.ki-phone.ki-filled:before {
  content: "\ec02";
  position: absolute;
  
}
.ki-photoshop.ki-filled:after {
  content: "\ec03";
  
  opacity: 0.1;
}
.ki-photoshop.ki-filled:before {
  content: "\ec04";
  position: absolute;
  
}
.ki-picture.ki-filled:after {
  content: "\ec05";
  
  opacity: 0.1;
}
.ki-picture.ki-filled:before {
  content: "\ec06";
  position: absolute;
  
}
.ki-pill.ki-filled:after {
  content: "\ec07";
  
  opacity: 0.1;
}
.ki-pill.ki-filled:before {
  content: "\ec08";
  position: absolute;
  
}
.ki-pin.ki-filled:after {
  content: "\ec09";
  
  opacity: 0.1;
}
.ki-pin.ki-filled:before {
  content: "\ec0a";
  position: absolute;
  
}
.ki-plus-circle.ki-filled:after {
  content: "\ec0b";
  
  opacity: 0.1;
}
.ki-plus-circle.ki-filled:before {
  content: "\ec0c";
  position: absolute;
  
}
.ki-plus-squared.ki-filled:after {
  content: "\ec0d";
  
  opacity: 0.1;
}
.ki-plus-squared.ki-filled:before {
  content: "\ec0e";
  position: absolute;
  
}
.ki-plus.ki-filled:before {
  content: "\ec0f";
}
.ki-pointers.ki-filled:after {
  content: "\ec10";
  
  opacity: 0.1;
}
.ki-pointers.ki-filled:before {
  content: "\ec11";
  position: absolute;
  
}
.ki-price-tag.ki-filled:after {
  content: "\ec12";
  
  opacity: 0.1;
}
.ki-price-tag.ki-filled:before {
  content: "\ec13";
  position: absolute;
  
}
.ki-printer.ki-filled:after {
  content: "\ec14";
  
  opacity: 0.1;
}
.ki-printer.ki-filled:before {
  content: "\ec15";
  position: absolute;
  
}
.ki-profile-circle.ki-filled:after {
  content: "\ec16";
  
  opacity: 0.1;
}
.ki-profile-circle.ki-filled:before {
  content: "\ec17";
  position: absolute;
  
}
.ki-pulse.ki-filled:after {
  content: "\ec18";
  
  opacity: 0.1;
}
.ki-pulse.ki-filled:before {
  content: "\ec19";
  position: absolute;
  
}
.ki-purchase.ki-filled:after {
  content: "\ec1a";
  
  opacity: 0.1;
}
.ki-purchase.ki-filled:before {
  content: "\ec1b";
  position: absolute;
  
}
.ki-python.ki-filled:after {
  content: "\ec1c";
  
  opacity: 0.1;
}
.ki-python.ki-filled:before {
  content: "\ec1d";
  position: absolute;
  
}
.ki-question-2.ki-filled:after {
  content: "\ec1e";
  
  opacity: 0.1;
}
.ki-question-2.ki-filled:before {
  content: "\ec1f";
  position: absolute;
  
}
.ki-question.ki-filled:after {
  content: "\ec20";
  
  opacity: 0.1;
}
.ki-question.ki-filled:before {
  content: "\ec21";
  position: absolute;
  
}
.ki-questionnaire-tablet.ki-filled:after {
  content: "\ec22";
  
  opacity: 0.1;
}
.ki-questionnaire-tablet.ki-filled:before {
  content: "\ec23";
  position: absolute;
  
}
.ki-ranking.ki-filled:after {
  content: "\ec24";
  
  opacity: 0.1;
}
.ki-ranking.ki-filled:before {
  content: "\ec25";
  position: absolute;
  
}
.ki-react.ki-filled:after {
  content: "\ec26";
  
  opacity: 0.1;
}
.ki-react.ki-filled:before {
  content: "\ec27";
  position: absolute;
  
}
.ki-receipt-square.ki-filled:after {
  content: "\ec28";
  
  opacity: 0.1;
}
.ki-receipt-square.ki-filled:before {
  content: "\ec29";
  position: absolute;
  
}
.ki-rescue.ki-filled:after {
  content: "\ec2a";
  
  opacity: 0.1;
}
.ki-rescue.ki-filled:before {
  content: "\ec2b";
  position: absolute;
  
}
.ki-right-left.ki-filled:before {
  content: "\ec2c";
}
.ki-right-square.ki-filled:after {
  content: "\ec2d";
  
  opacity: 0.1;
}
.ki-right-square.ki-filled:before {
  content: "\ec2e";
  position: absolute;
  
}
.ki-right.ki-filled:before {
  content: "\ec2f";
}
.ki-rocket.ki-filled:after {
  content: "\ec30";
  
  opacity: 0.1;
}
.ki-rocket.ki-filled:before {
  content: "\ec31";
  position: absolute;
  
}
.ki-route.ki-filled:after {
  content: "\ec32";
  
  opacity: 0.1;
}
.ki-route.ki-filled:before {
  content: "\ec33";
  position: absolute;
  
}
.ki-router.ki-filled:after {
  content: "\ec34";
  
  opacity: 0.1;
}
.ki-router.ki-filled:before {
  content: "\ec35";
  position: absolute;
  
}
.ki-row-horizontal.ki-filled:after {
  content: "\ec36";
  
  opacity: 0.1;
}
.ki-row-horizontal.ki-filled:before {
  content: "\ec37";
  position: absolute;
  
}
.ki-row-vertical.ki-filled:after {
  content: "\ec38";
  
  opacity: 0.1;
}
.ki-row-vertical.ki-filled:before {
  content: "\ec39";
  position: absolute;
  
}
.ki-safe-home.ki-filled:after {
  content: "\ec3a";
  
  opacity: 0.1;
}
.ki-safe-home.ki-filled:before {
  content: "\ec3b";
  position: absolute;
  
}
.ki-satellite.ki-filled:after {
  content: "\ec3c";
  
  opacity: 0.1;
}
.ki-satellite.ki-filled:before {
  content: "\ec3d";
  position: absolute;
  
}
.ki-save-2.ki-filled:after {
  content: "\ec3e";
  
  opacity: 0.1;
}
.ki-save-2.ki-filled:before {
  content: "\ec3f";
  position: absolute;
  
}
.ki-save-deposit.ki-filled:after {
  content: "\ec40";
  
  opacity: 0.1;
}
.ki-save-deposit.ki-filled:before {
  content: "\ec41";
  position: absolute;
  
}
.ki-scan-barcode.ki-filled:after {
  content: "\ec42";
  
  opacity: 0.1;
}
.ki-scan-barcode.ki-filled:before {
  content: "\ec43";
  position: absolute;
  
}
.ki-screen.ki-filled:after {
  content: "\ec44";
  
  opacity: 0.1;
}
.ki-screen.ki-filled:before {
  content: "\ec45";
  position: absolute;
  
}
.ki-scroll.ki-filled:after {
  content: "\ec46";
  
  opacity: 0.1;
}
.ki-scroll.ki-filled:before {
  content: "\ec47";
  position: absolute;
  
}
.ki-search-list.ki-filled:after {
  content: "\ec48";
  
  opacity: 0.1;
}
.ki-search-list.ki-filled:before {
  content: "\ec49";
  position: absolute;
  
}
.ki-security-user.ki-filled:after {
  content: "\ec4a";
  
  opacity: 0.1;
}
.ki-security-user.ki-filled:before {
  content: "\ec4b";
  position: absolute;
  
}
.ki-setting-2.ki-filled:after {
  content: "\ec4c";
  
  opacity: 0.1;
}
.ki-setting-2.ki-filled:before {
  content: "\ec4d";
  position: absolute;
  
}
.ki-setting-3.ki-filled:after {
  content: "\ec4e";
  
  opacity: 0.1;
}
.ki-setting-3.ki-filled:before {
  content: "\ec4f";
  position: absolute;
  
}
.ki-setting-4.ki-filled:after {
  content: "\ec50";
  
  opacity: 0.1;
}
.ki-setting-4.ki-filled:before {
  content: "\ec51";
  position: absolute;
  
}
.ki-setting.ki-filled:after {
  content: "\ec52";
  
  opacity: 0.1;
}
.ki-setting.ki-filled:before {
  content: "\ec53";
  position: absolute;
  
}
.ki-share.ki-filled:after {
  content: "\ec54";
  
  opacity: 0.1;
}
.ki-share.ki-filled:before {
  content: "\ec55";
  position: absolute;
  
}
.ki-shield-cross.ki-filled:after {
  content: "\ec56";
  
  opacity: 0.1;
}
.ki-shield-cross.ki-filled:before {
  content: "\ec57";
  position: absolute;
  
}
.ki-shield-search.ki-filled:after {
  content: "\ec58";
  
  opacity: 0.1;
}
.ki-shield-search.ki-filled:before {
  content: "\ec59";
  position: absolute;
  
}
.ki-shield-slash.ki-filled:after {
  content: "\ec5a";
  
  opacity: 0.1;
}
.ki-shield-slash.ki-filled:before {
  content: "\ec5b";
  position: absolute;
  
}
.ki-shield-tick.ki-filled:after {
  content: "\ec5c";
  
  opacity: 0.1;
}
.ki-shield-tick.ki-filled:before {
  content: "\ec5d";
  position: absolute;
  
}
.ki-shield.ki-filled:after {
  content: "\ec5e";
  
  opacity: 0.1;
}
.ki-shield.ki-filled:before {
  content: "\ec5f";
  position: absolute;
  
}
.ki-ship.ki-filled:after {
  content: "\ec60";
  
  opacity: 0.1;
}
.ki-ship.ki-filled:before {
  content: "\ec61";
  position: absolute;
  
}
.ki-shop.ki-filled:after {
  content: "\ec62";
  
  opacity: 0.1;
}
.ki-shop.ki-filled:before {
  content: "\ec63";
  position: absolute;
  
}
.ki-simcard-2.ki-filled:after {
  content: "\ec64";
  
  opacity: 0.1;
}
.ki-simcard-2.ki-filled:before {
  content: "\ec65";
  position: absolute;
  
}
.ki-simcard.ki-filled:after {
  content: "\ec66";
  
  opacity: 0.1;
}
.ki-simcard.ki-filled:before {
  content: "\ec67";
  position: absolute;
  
}
.ki-size.ki-filled:after {
  content: "\ec68";
  
  opacity: 0.1;
}
.ki-size.ki-filled:before {
  content: "\ec69";
  position: absolute;
  
}
.ki-slack.ki-filled:after {
  content: "\ec6a";
  
  opacity: 0.1;
}
.ki-slack.ki-filled:before {
  content: "\ec6b";
  position: absolute;
  
}
.ki-slider-horizontal-2.ki-filled:after {
  content: "\ec6c";
  
  opacity: 0.1;
}
.ki-slider-horizontal-2.ki-filled:before {
  content: "\ec6d";
  position: absolute;
  
}
.ki-slider-horizontal.ki-filled:after {
  content: "\ec6e";
  
  opacity: 0.1;
}
.ki-slider-horizontal.ki-filled:before {
  content: "\ec6f";
  position: absolute;
  
}
.ki-slider-vertica.ki-filled:after {
  content: "\ec70";
  
  opacity: 0.1;
}
.ki-slider-vertica.ki-filled:before {
  content: "\ec71";
  position: absolute;
  
}
.ki-slider-vertical.ki-filled:after {
  content: "\ec72";
  
  opacity: 0.1;
}
.ki-slider-vertical.ki-filled:before {
  content: "\ec73";
  position: absolute;
  
}
.ki-slider.ki-filled:after {
  content: "\ec74";
  
  opacity: 0.1;
}
.ki-slider.ki-filled:before {
  content: "\ec75";
  position: absolute;
  
}
.ki-sms.ki-filled:after {
  content: "\ec76";
  
  opacity: 0.1;
}
.ki-sms.ki-filled:before {
  content: "\ec77";
  position: absolute;
  
}
.ki-snapchat.ki-filled:after {
  content: "\ec78";
  
  opacity: 0.1;
}
.ki-snapchat.ki-filled:before {
  content: "\ec79";
  position: absolute;
  
}
.ki-social-media.ki-filled:after {
  content: "\ec7a";
  
  opacity: 0.1;
}
.ki-social-media.ki-filled:before {
  content: "\ec7b";
  position: absolute;
  
}
.ki-soft-2.ki-filled:after {
  content: "\ec7c";
  
  opacity: 0.1;
}
.ki-soft-2.ki-filled:before {
  content: "\ec7d";
  position: absolute;
  
}
.ki-soft-3.ki-filled:after {
  content: "\ec7e";
  
  opacity: 0.1;
}
.ki-soft-3.ki-filled:before {
  content: "\ec7f";
  position: absolute;
  
}
.ki-soft.ki-filled:after {
  content: "\ec80";
  
  opacity: 0.1;
}
.ki-soft.ki-filled:before {
  content: "\ec81";
  position: absolute;
  
}
.ki-some-files.ki-filled:after {
  content: "\ec82";
  
  opacity: 0.1;
}
.ki-some-files.ki-filled:before {
  content: "\ec83";
  position: absolute;
  
}
.ki-sort.ki-filled:after {
  content: "\ec84";
  
  opacity: 0.1;
}
.ki-sort.ki-filled:before {
  content: "\ec85";
  position: absolute;
  
}
.ki-speaker.ki-filled:after {
  content: "\ec86";
  
  opacity: 0.1;
}
.ki-speaker.ki-filled:before {
  content: "\ec87";
  position: absolute;
  
}
.ki-spotify.ki-filled:after {
  content: "\ec88";
  
  opacity: 0.1;
}
.ki-spotify.ki-filled:before {
  content: "\ec89";
  position: absolute;
  
}
.ki-spring-framework.ki-filled:after {
  content: "\ec8a";
  
  opacity: 0.1;
}
.ki-spring-framework.ki-filled:before {
  content: "\ec8b";
  position: absolute;
  
}
.ki-square-brackets.ki-filled:after {
  content: "\ec8c";
  
  opacity: 0.1;
}
.ki-square-brackets.ki-filled:before {
  content: "\ec8d";
  position: absolute;
  
}
.ki-star.ki-filled:after {
  content: "\ec8e";
  
  opacity: 0.1;
}
.ki-star.ki-filled:before {
  content: "\ec8f";
  position: absolute;
  
}
.ki-status.ki-filled:after {
  content: "\ec90";
  
  opacity: 0.1;
}
.ki-status.ki-filled:before {
  content: "\ec91";
  position: absolute;
  
}
.ki-subtitle.ki-filled:after {
  content: "\ec92";
  
  opacity: 0.1;
}
.ki-subtitle.ki-filled:before {
  content: "\ec93";
  position: absolute;
  
}
.ki-sun.ki-filled:after {
  content: "\ec94";
  
  opacity: 0.1;
}
.ki-sun.ki-filled:before {
  content: "\ec95";
  position: absolute;
  
}
.ki-support.ki-filled:after {
  content: "\ec96";
  
  opacity: 0.1;
}
.ki-support.ki-filled:before {
  content: "\ec97";
  position: absolute;
  
}
.ki-switch.ki-filled:after {
  content: "\ec98";
  
  opacity: 0.1;
}
.ki-switch.ki-filled:before {
  content: "\ec99";
  position: absolute;
  
}
.ki-syringe.ki-filled:after {
  content: "\ec9a";
  
  opacity: 0.1;
}
.ki-syringe.ki-filled:before {
  content: "\ec9b";
  position: absolute;
  
}
.ki-tab-tablet.ki-filled:after {
  content: "\ec9c";
  
  opacity: 0.1;
}
.ki-tab-tablet.ki-filled:before {
  content: "\ec9d";
  position: absolute;
  
}
.ki-tablet-delete.ki-filled:after {
  content: "\ec9e";
  
  opacity: 0.1;
}
.ki-tablet-delete.ki-filled:before {
  content: "\ec9f";
  position: absolute;
  
}
.ki-tablet-down.ki-filled:after {
  content: "\eca0";
  
  opacity: 0.1;
}
.ki-tablet-down.ki-filled:before {
  content: "\eca1";
  position: absolute;
  
}
.ki-tablet-ok.ki-filled:after {
  content: "\eca2";
  
}
.ki-tablet-ok.ki-filled:before {
  content: "\eca3";
  position: absolute;
  
  opacity: 0.1;
}
.ki-tablet-text-down.ki-filled:after {
  content: "\eca4";
  
  opacity: 0.1;
}
.ki-tablet-text-down.ki-filled:before {
  content: "\eca5";
  position: absolute;
  
}
.ki-tablet-text-up.ki-filled:after {
  content: "\eca6";
  
  opacity: 0.1;
}
.ki-tablet-text-up.ki-filled:before {
  content: "\eca7";
  position: absolute;
  
}
.ki-tablet-up.ki-filled:after {
  content: "\eca8";
  
  opacity: 0.1;
}
.ki-tablet-up.ki-filled:before {
  content: "\eca9";
  position: absolute;
  
}
.ki-tablet.ki-filled:after {
  content: "\ecaa";
  
  opacity: 0.1;
}
.ki-tablet.ki-filled:before {
  content: "\ecab";
  position: absolute;
  
}
.ki-tag-cross.ki-filled:after {
  content: "\ecac";
  
  opacity: 0.1;
}
.ki-tag-cross.ki-filled:before {
  content: "\ecad";
  position: absolute;
  
}
.ki-tag.ki-filled:after {
  content: "\ecae";
  
  opacity: 0.1;
}
.ki-tag.ki-filled:before {
  content: "\ecaf";
  position: absolute;
  
}
.ki-teacher.ki-filled:after {
  content: "\ecb0";
  
  opacity: 0.1;
}
.ki-teacher.ki-filled:before {
  content: "\ecb1";
  position: absolute;
  
}
.ki-technology-1.ki-filled:after {
  content: "\ecb2";
  
  opacity: 0.1;
}
.ki-technology-1.ki-filled:before {
  content: "\ecb3";
  position: absolute;
  
}
.ki-technology-2.ki-filled:after {
  content: "\ecb4";
  
  opacity: 0.1;
}
.ki-technology-2.ki-filled:before {
  content: "\ecb5";
  position: absolute;
  
}
.ki-technology-3.ki-filled:after {
  content: "\ecb6";
  
  opacity: 0.1;
}
.ki-technology-3.ki-filled:before {
  content: "\ecb7";
  position: absolute;
  
}
.ki-technology-4.ki-filled:after {
  content: "\ecb8";
  
  opacity: 0.1;
}
.ki-technology-4.ki-filled:before {
  content: "\ecb9";
  position: absolute;
  
}
.ki-telephone-geolocation.ki-filled:after {
  content: "\ecba";
  
  opacity: 0.1;
}
.ki-telephone-geolocation.ki-filled:before {
  content: "\ecbb";
  position: absolute;
  
}
.ki-test-tubes.ki-filled:after {
  content: "\ecbc";
  
  opacity: 0.1;
}
.ki-test-tubes.ki-filled:before {
  content: "\ecbd";
  position: absolute;
  
}
.ki-text-bold.ki-filled:after {
  content: "\ecbe";
  
  opacity: 0.1;
}
.ki-text-bold.ki-filled:before {
  content: "\ecbf";
  position: absolute;
  
}
.ki-text-circle.ki-filled:before {
  content: "\ecc0";
}
.ki-text-italic.ki-filled:after {
  content: "\ecc1";
  
  opacity: 0.1;
}
.ki-text-italic.ki-filled:before {
  content: "\ecc2";
  position: absolute;
  
}
.ki-text-number.ki-filled:before {
  content: "\ecc3";
}
.ki-text-strikethrough.ki-filled:after {
  content: "\ecc4";
  
  opacity: 0.1;
}
.ki-text-strikethrough.ki-filled:before {
  content: "\ecc5";
  position: absolute;
  
}
.ki-text-underline.ki-filled:after {
  content: "\ecc6";
  
}
.ki-text-underline.ki-filled:before {
  content: "\ecc7";
  position: absolute;
  
  opacity: 0.1;
}
.ki-text.ki-filled:after {
  content: "\ecc8";
  
  opacity: 0.1;
}
.ki-text.ki-filled:before {
  content: "\ecc9";
  position: absolute;
  
}
.ki-textalign-center.ki-filled:before {
  content: "\ecca";
}
.ki-textalign-justifycenter.ki-filled:before {
  content: "\eccb";
}
.ki-textalign-left.ki-filled:before {
  content: "\eccc";
}
.ki-textalign-right.ki-filled:before {
  content: "\eccd";
}
.ki-thermometer.ki-filled:after {
  content: "\ecce";
  
  opacity: 0.1;
}
.ki-thermometer.ki-filled:before {
  content: "\eccf";
  position: absolute;
  
}
.ki-theta-theta.ki-filled:after {
  content: "\ecd0";
  
  opacity: 0.1;
}
.ki-theta-theta.ki-filled:before {
  content: "\ecd1";
  position: absolute;
  
}
.ki-tiktok.ki-filled:after {
  content: "\ecd2";
  
  opacity: 0.1;
}
.ki-tiktok.ki-filled:before {
  content: "\ecd3";
  position: absolute;
  
}
.ki-time.ki-filled:after {
  content: "\ecd4";
  
  opacity: 0.1;
}
.ki-time.ki-filled:before {
  content: "\ecd5";
  position: absolute;
  
}
.ki-timer.ki-filled:after {
  content: "\ecd6";
  
  opacity: 0.1;
}
.ki-timer.ki-filled:before {
  content: "\ecd7";
  position: absolute;
  
}
.ki-to-left.ki-filled:after {
  content: "\ecd8";
  
  opacity: 0.1;
}
.ki-to-left.ki-filled:before {
  content: "\ecd9";
  position: absolute;
  
}
.ki-to-right.ki-filled:after {
  content: "\ecda";
  
  opacity: 0.1;
}
.ki-to-right.ki-filled:before {
  content: "\ecdb";
  position: absolute;
  
}
.ki-toggle-off-circle.ki-filled:after {
  content: "\ecdc";
  
  opacity: 0.1;
}
.ki-toggle-off-circle.ki-filled:before {
  content: "\ecdd";
  position: absolute;
  
}
.ki-toggle-off.ki-filled:after {
  content: "\ecde";
  
}
.ki-toggle-off.ki-filled:before {
  content: "\ecdf";
  position: absolute;
  
  opacity: 0.1;
}
.ki-toggle-on-circle.ki-filled:after {
  content: "\ece0";
  
  opacity: 0.1;
}
.ki-toggle-on-circle.ki-filled:before {
  content: "\ece1";
  position: absolute;
  
}
.ki-toggle-on.ki-filled:after {
  content: "\ece2";
  
  opacity: 0.1;
}
.ki-toggle-on.ki-filled:before {
  content: "\ece3";
  position: absolute;
  
}
.ki-trash-square.ki-filled:after {
  content: "\ece4";
  
  opacity: 0.1;
}
.ki-trash-square.ki-filled:before {
  content: "\ece5";
  position: absolute;
  
}
.ki-trash.ki-filled:after {
  content: "\ece6";
  
  opacity: 0.1;
}
.ki-trash.ki-filled:before {
  content: "\ece7";
  position: absolute;
  
}
.ki-tree.ki-filled:after {
  content: "\ece8";
  
  opacity: 0.1;
}
.ki-tree.ki-filled:before {
  content: "\ece9";
  position: absolute;
  
}
.ki-trello.ki-filled:after {
  content: "\ecea";
  
  opacity: 0.1;
}
.ki-trello.ki-filled:before {
  content: "\eceb";
  position: absolute;
  
}
.ki-ts.ki-filled:after {
  content: "\ecec";
  
  opacity: 0.1;
}
.ki-ts.ki-filled:before {
  content: "\eced";
  position: absolute;
  
}
.ki-twitch.ki-filled:after {
  content: "\ecee";
  
  opacity: 0.1;
}
.ki-twitch.ki-filled:before {
  content: "\ecef";
  position: absolute;
  
}
.ki-twitter.ki-filled:after {
  content: "\ecf0";
  
  opacity: 0.1;
}
.ki-twitter.ki-filled:before {
  content: "\ecf1";
  position: absolute;
  
}
.ki-two-credit-cart.ki-filled:after {
  content: "\ecf2";
  
  opacity: 0.1;
}
.ki-two-credit-cart.ki-filled:before {
  content: "\ecf3";
  position: absolute;
  
}
.ki-underlining.ki-filled:after {
  content: "\ecf4";
  
  opacity: 0.1;
}
.ki-underlining.ki-filled:before {
  content: "\ecf5";
  position: absolute;
  
}
.ki-up-diagonal.ki-filled:before {
  content: "\ecf6";
}
.ki-up-down.ki-filled:before {
  content: "\ecf7";
}
.ki-up-square.ki-filled:after {
  content: "\ecf8";
  
}
.ki-up-square.ki-filled:before {
  content: "\ecf9";
  position: absolute;
  
  opacity: 0.1;
}
.ki-up.ki-filled:before {
  content: "\ecfa";
}
.ki-update-file.ki-filled:after {
  content: "\ecfb";
  
  opacity: 0.1;
}
.ki-update-file.ki-filled:before {
  content: "\ecfc";
  position: absolute;
  
}
.ki-update-folder.ki-filled:after {
  content: "\ecfd";
  
  opacity: 0.1;
}
.ki-update-folder.ki-filled:before {
  content: "\ecfe";
  position: absolute;
  
}
.ki-user-edit.ki-filled:after {
  content: "\ecff";
  
  opacity: 0.1;
}
.ki-user-edit.ki-filled:before {
  content: "\ed00";
  position: absolute;
  
}
.ki-user-square.ki-filled:after {
  content: "\ed01";
  
  opacity: 0.1;
}
.ki-user-square.ki-filled:before {
  content: "\ed02";
  position: absolute;
  
}
.ki-user-tick.ki-filled:after {
  content: "\ed03";
  
  opacity: 0.1;
}
.ki-user-tick.ki-filled:before {
  content: "\ed04";
  position: absolute;
  
}
.ki-user.ki-filled:after {
  content: "\ed05";
  
  opacity: 0.1;
}
.ki-user.ki-filled:before {
  content: "\ed06";
  position: absolute;
  
}
.ki-users.ki-filled:after {
  content: "\ed07";
  
  opacity: 0.1;
}
.ki-users.ki-filled:before {
  content: "\ed08";
  position: absolute;
  
}
.ki-verify.ki-filled:after {
  content: "\ed09";
  
  opacity: 0.1;
}
.ki-verify.ki-filled:before {
  content: "\ed0a";
  position: absolute;
  
}
.ki-vibe-vibe.ki-filled:after {
  content: "\ed0b";
  
  opacity: 0.1;
}
.ki-vibe-vibe.ki-filled:before {
  content: "\ed0c";
  position: absolute;
  
}
.ki-virus.ki-filled:after {
  content: "\ed0d";
  
  opacity: 0.1;
}
.ki-virus.ki-filled:before {
  content: "\ed0e";
  position: absolute;
  
}
.ki-vue.ki-filled:after {
  content: "\ed0f";
  
  opacity: 0.1;
}
.ki-vue.ki-filled:before {
  content: "\ed10";
  position: absolute;
  
}
.ki-vuesax.ki-filled:after {
  content: "\ed11";
  
  opacity: 0.1;
}
.ki-vuesax.ki-filled:before {
  content: "\ed12";
  position: absolute;
  
}
.ki-wallet.ki-filled:after {
  content: "\ed13";
  
  opacity: 0.1;
}
.ki-wallet.ki-filled:before {
  content: "\ed14";
  position: absolute;
  
}
.ki-wanchain-wan.ki-filled:after {
  content: "\ed15";
  
  opacity: 0.1;
}
.ki-wanchain-wan.ki-filled:before {
  content: "\ed16";
  position: absolute;
  
}
.ki-watch.ki-filled:after {
  content: "\ed17";
  
  opacity: 0.1;
}
.ki-watch.ki-filled:before {
  content: "\ed18";
  position: absolute;
  
}
.ki-whatsapp.ki-filled:after {
  content: "\ed19";
  
  opacity: 0.1;
}
.ki-whatsapp.ki-filled:before {
  content: "\ed1a";
  position: absolute;
  
}
.ki-wifi-home.ki-filled:after {
  content: "\ed1b";
  
  opacity: 0.1;
}
.ki-wifi-home.ki-filled:before {
  content: "\ed1c";
  position: absolute;
  
}
.ki-wifi-square.ki-filled:after {
  content: "\ed1d";
  
  opacity: 0.1;
}
.ki-wifi-square.ki-filled:before {
  content: "\ed1e";
  position: absolute;
  
}
.ki-wifi.ki-filled:after {
  content: "\ed1f";
  
  opacity: 0.1;
}
.ki-wifi.ki-filled:before {
  content: "\ed20";
  position: absolute;
  
}
.ki-wireframe.ki-filled:after {
  content: "\ed21";
  
  opacity: 0.1;
}
.ki-wireframe.ki-filled:before {
  content: "\ed22";
  position: absolute;
  
}
.ki-wlan.ki-filled:before {
  content: "\ed23";
}
.ki-wrench.ki-filled:after {
  content: "\ed24";
  
  opacity: 0.1;
}
.ki-wrench.ki-filled:before {
  content: "\ed25";
  position: absolute;
  
}
.ki-xaomi.ki-filled:after {
  content: "\ed26";
  
  opacity: 0.1;
}
.ki-xaomi.ki-filled:before {
  content: "\ed27";
  position: absolute;
  
}
.ki-xd.ki-filled:after {
  content: "\ed28";
  
  opacity: 0.1;
}
.ki-xd.ki-filled:before {
  content: "\ed29";
  position: absolute;
  
}
.ki-xmr.ki-filled:after {
  content: "\ed2a";
  
  opacity: 0.1;
}
.ki-xmr.ki-filled:before {
  content: "\ed2b";
  position: absolute;
  
}
.ki-yii.ki-filled:after {
  content: "\ed2c";
  
  opacity: 0.1;
}
.ki-yii.ki-filled:before {
  content: "\ed2d";
  position: absolute;
  
}
.ki-youtube.ki-filled:after {
  content: "\ed2e";
  
  opacity: 0.1;
}
.ki-youtube.ki-filled:before {
  content: "\ed2f";
  position: absolute;
  
}

@font-face {
  font-family: 'keenicons-outline';
  src:
    url('fonts/keenicons-outline.ttf?3m7u3e') format('truetype'),
    url('fonts/keenicons-outline.woff?3m7u3e') format('woff'),
    url('fonts/keenicons-outline.svg?3m7u3e#keenicons-outline') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.ki-outline {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'keenicons-outline' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  display: inline-flex;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ki-abstract-1.ki-outline:before {
  content: "\e900";
}
.ki-abstract-2.ki-outline:before {
  content: "\e901";
}
.ki-abstract-3.ki-outline:before {
  content: "\e902";
}
.ki-abstract-4.ki-outline:before {
  content: "\e903";
}
.ki-abstract-5.ki-outline:before {
  content: "\e904";
}
.ki-abstract-6.ki-outline:before {
  content: "\e905";
}
.ki-abstract-7.ki-outline:before {
  content: "\e906";
}
.ki-abstract-8.ki-outline:before {
  content: "\e907";
}
.ki-abstract-9.ki-outline:before {
  content: "\e908";
}
.ki-abstract-10.ki-outline:before {
  content: "\e909";
}
.ki-abstract-11.ki-outline:before {
  content: "\e90a";
}
.ki-abstract-12.ki-outline:before {
  content: "\e90b";
}
.ki-abstract-13.ki-outline:before {
  content: "\e90c";
}
.ki-abstract-14.ki-outline:before {
  content: "\e90d";
}
.ki-abstract-15.ki-outline:before {
  content: "\e90e";
}
.ki-abstract-16.ki-outline:before {
  content: "\e90f";
}
.ki-abstract-17.ki-outline:before {
  content: "\e910";
}
.ki-abstract-18.ki-outline:before {
  content: "\e911";
}
.ki-abstract-19.ki-outline:before {
  content: "\e912";
}
.ki-abstract-20.ki-outline:before {
  content: "\e913";
}
.ki-abstract-21.ki-outline:before {
  content: "\e914";
}
.ki-abstract-22.ki-outline:before {
  content: "\e915";
}
.ki-abstract-23.ki-outline:before {
  content: "\e916";
}
.ki-abstract-24.ki-outline:before {
  content: "\e917";
}
.ki-abstract-25.ki-outline:before {
  content: "\e918";
}
.ki-abstract-26.ki-outline:before {
  content: "\e919";
}
.ki-abstract-27.ki-outline:before {
  content: "\e91a";
}
.ki-abstract-28.ki-outline:before {
  content: "\e91b";
}
.ki-abstract-29.ki-outline:before {
  content: "\e91c";
}
.ki-abstract-30.ki-outline:before {
  content: "\e91d";
}
.ki-abstract-31.ki-outline:before {
  content: "\e91e";
}
.ki-abstract-32.ki-outline:before {
  content: "\e91f";
}
.ki-abstract-33.ki-outline:before {
  content: "\e920";
}
.ki-abstract-34.ki-outline:before {
  content: "\e921";
}
.ki-abstract-35.ki-outline:before {
  content: "\e922";
}
.ki-abstract-36.ki-outline:before {
  content: "\e923";
}
.ki-abstract-37.ki-outline:before {
  content: "\e924";
}
.ki-abstract-38.ki-outline:before {
  content: "\e925";
}
.ki-abstract-39.ki-outline:before {
  content: "\e926";
}
.ki-abstract-40.ki-outline:before {
  content: "\e927";
}
.ki-abstract-41.ki-outline:before {
  content: "\e928";
}
.ki-abstract-42.ki-outline:before {
  content: "\e929";
}
.ki-abstract-43.ki-outline:before {
  content: "\e92a";
}
.ki-abstract-44.ki-outline:before {
  content: "\e92b";
}
.ki-abstract-45.ki-outline:before {
  content: "\e92c";
}
.ki-abstract-46.ki-outline:before {
  content: "\e92d";
}
.ki-abstract-47.ki-outline:before {
  content: "\e92e";
}
.ki-abstract-48.ki-outline:before {
  content: "\e92f";
}
.ki-abstract-49.ki-outline:before {
  content: "\e930";
}
.ki-abstract.ki-outline:before {
  content: "\e931";
}
.ki-add-files.ki-outline:before {
  content: "\e932";
}
.ki-add-folder.ki-outline:before {
  content: "\e933";
}
.ki-add-notepad.ki-outline:before {
  content: "\e934";
}
.ki-additem.ki-outline:before {
  content: "\e935";
}
.ki-address-book.ki-outline:before {
  content: "\e936";
}
.ki-airplane-square.ki-outline:before {
  content: "\e937";
}
.ki-airplane.ki-outline:before {
  content: "\e938";
}
.ki-airpod.ki-outline:before {
  content: "\e939";
}
.ki-android.ki-outline:before {
  content: "\e93a";
}
.ki-angular.ki-outline:before {
  content: "\e93b";
}
.ki-apple.ki-outline:before {
  content: "\e93c";
}
.ki-archive-tick.ki-outline:before {
  content: "\e93d";
}
.ki-archive.ki-outline:before {
  content: "\e93e";
}
.ki-arrow-circle-left.ki-outline:before {
  content: "\e93f";
}
.ki-arrow-circle-right.ki-outline:before {
  content: "\e940";
}
.ki-arrow-down-left.ki-outline:before {
  content: "\e941";
}
.ki-arrow-down-refraction.ki-outline:before {
  content: "\e942";
}
.ki-arrow-down-right.ki-outline:before {
  content: "\e943";
}
.ki-arrow-down.ki-outline:before {
  content: "\e944";
}
.ki-arrow-left.ki-outline:before {
  content: "\e945";
}
.ki-arrow-mix.ki-outline:before {
  content: "\e946";
}
.ki-arrow-right-left.ki-outline:before {
  content: "\e947";
}
.ki-arrow-right.ki-outline:before {
  content: "\e948";
}
.ki-arrow-two-diagonals.ki-outline:before {
  content: "\e949";
}
.ki-arrow-up-down.ki-outline:before {
  content: "\e94a";
}
.ki-arrow-up-left.ki-outline:before {
  content: "\e94b";
}
.ki-arrow-up-refraction.ki-outline:before {
  content: "\e94c";
}
.ki-arrow-up-right.ki-outline:before {
  content: "\e94d";
}
.ki-arrow-up.ki-outline:before {
  content: "\e94e";
}
.ki-arrow-zigzag.ki-outline:before {
  content: "\e94f";
}
.ki-arrows-circle.ki-outline:before {
  content: "\e950";
}
.ki-arrows-loop.ki-outline:before {
  content: "\e951";
}
.ki-artificial-intelligence.ki-outline:before {
  content: "\e952";
}
.ki-autobrightness.ki-outline:before {
  content: "\e953";
}
.ki-avalanche-avax.ki-outline:before {
  content: "\e954";
}
.ki-award.ki-outline:before {
  content: "\e955";
}
.ki-badge.ki-outline:before {
  content: "\e956";
}
.ki-bandage.ki-outline:before {
  content: "\e957";
}
.ki-bank.ki-outline:before {
  content: "\e958";
}
.ki-bar-chart.ki-outline:before {
  content: "\e959";
}
.ki-barcode.ki-outline:before {
  content: "\e95a";
}
.ki-basket-ok.ki-outline:before {
  content: "\e95b";
}
.ki-basket.ki-outline:before {
  content: "\e95c";
}
.ki-behance.ki-outline:before {
  content: "\e95d";
}
.ki-bill.ki-outline:before {
  content: "\e95e";
}
.ki-binance-usd-busd.ki-outline:before {
  content: "\e95f";
}
.ki-binance.ki-outline:before {
  content: "\e960";
}
.ki-bitcoin.ki-outline:before {
  content: "\e961";
}
.ki-black-down.ki-outline:before {
  content: "\e962";
}
.ki-black-left-line.ki-outline:before {
  content: "\e963";
}
.ki-black-left.ki-outline:before {
  content: "\e964";
}
.ki-black-right-line.ki-outline:before {
  content: "\e965";
}
.ki-black-right.ki-outline:before {
  content: "\e966";
}
.ki-black-up.ki-outline:before {
  content: "\e967";
}
.ki-bluetooth.ki-outline:before {
  content: "\e968";
}
.ki-book-open.ki-outline:before {
  content: "\e969";
}
.ki-book-square.ki-outline:before {
  content: "\e96a";
}
.ki-book.ki-outline:before {
  content: "\e96b";
}
.ki-bookmark-2.ki-outline:before {
  content: "\e96c";
}
.ki-bookmark.ki-outline:before {
  content: "\e96d";
}
.ki-bootstrap.ki-outline:before {
  content: "\e96e";
}
.ki-briefcase.ki-outline:before {
  content: "\e96f";
}
.ki-brifecase-cros.ki-outline:before {
  content: "\e970";
}
.ki-brifecase-tick.ki-outline:before {
  content: "\e971";
}
.ki-brifecase-timer.ki-outline:before {
  content: "\e972";
}
.ki-brush.ki-outline:before {
  content: "\e973";
}
.ki-bucket-square.ki-outline:before {
  content: "\e974";
}
.ki-bucket.ki-outline:before {
  content: "\e975";
}
.ki-burger-menu-1.ki-outline:before {
  content: "\e976";
}
.ki-burger-menu-2.ki-outline:before {
  content: "\e977";
}
.ki-burger-menu-3.ki-outline:before {
  content: "\e978";
}
.ki-burger-menu-4.ki-outline:before {
  content: "\e979";
}
.ki-burger-menu-5.ki-outline:before {
  content: "\e97a";
}
.ki-burger-menu-6.ki-outline:before {
  content: "\e97b";
}
.ki-burger-menu.ki-outline:before {
  content: "\e97c";
}
.ki-bus.ki-outline:before {
  content: "\e97d";
}
.ki-calculator.ki-outline:before {
  content: "\e97e";
}
.ki-calculatoror.ki-outline:before {
  content: "\e97f";
}
.ki-calendar-2.ki-outline:before {
  content: "\e980";
}
.ki-calendar-8.ki-outline:before {
  content: "\e981";
}
.ki-calendar-add.ki-outline:before {
  content: "\e982";
}
.ki-calendar-edit.ki-outline:before {
  content: "\e983";
}
.ki-calendar-remove.ki-outline:before {
  content: "\e984";
}
.ki-calendar-search.ki-outline:before {
  content: "\e985";
}
.ki-calendar-tick.ki-outline:before {
  content: "\e986";
}
.ki-calendar.ki-outline:before {
  content: "\e987";
}
.ki-call.ki-outline:before {
  content: "\e988";
}
.ki-capsule.ki-outline:before {
  content: "\e989";
}
.ki-car.ki-outline:before {
  content: "\e98a";
}
.ki-category.ki-outline:before {
  content: "\e98b";
}
.ki-cd.ki-outline:before {
  content: "\e98c";
}
.ki-celsius-cel.ki-outline:before {
  content: "\e98d";
}
.ki-chart-line-down-2.ki-outline:before {
  content: "\e98e";
}
.ki-chart-line-down.ki-outline:before {
  content: "\e98f";
}
.ki-chart-line-star.ki-outline:before {
  content: "\e990";
}
.ki-chart-line-up-2.ki-outline:before {
  content: "\e991";
}
.ki-chart-line-up.ki-outline:before {
  content: "\e992";
}
.ki-chart-line.ki-outline:before {
  content: "\e993";
}
.ki-chart-pie-3.ki-outline:before {
  content: "\e994";
}
.ki-chart-pie-4.ki-outline:before {
  content: "\e995";
}
.ki-chart-pie-simple.ki-outline:before {
  content: "\e996";
}
.ki-chart-pie-too.ki-outline:before {
  content: "\e997";
}
.ki-chart-simple-2.ki-outline:before {
  content: "\e998";
}
.ki-chart-simple-3.ki-outline:before {
  content: "\e999";
}
.ki-chart-simple.ki-outline:before {
  content: "\e99a";
}
.ki-chart.ki-outline:before {
  content: "\e99b";
}
.ki-check-circle.ki-outline:before {
  content: "\e99c";
}
.ki-check-squared.ki-outline:before {
  content: "\e99d";
}
.ki-check.ki-outline:before {
  content: "\e99e";
}
.ki-cheque.ki-outline:before {
  content: "\e99f";
}
.ki-chrome.ki-outline:before {
  content: "\e9a0";
}
.ki-classmates.ki-outline:before {
  content: "\e9a1";
}
.ki-click.ki-outline:before {
  content: "\e9a2";
}
.ki-clipboard.ki-outline:before {
  content: "\e9a3";
}
.ki-cloud-add.ki-outline:before {
  content: "\e9a4";
}
.ki-cloud-change.ki-outline:before {
  content: "\e9a5";
}
.ki-cloud-download.ki-outline:before {
  content: "\e9a6";
}
.ki-cloud.ki-outline:before {
  content: "\e9a7";
}
.ki-code.ki-outline:before {
  content: "\e9a8";
}
.ki-coffee.ki-outline:before {
  content: "\e9a9";
}
.ki-color-swatch.ki-outline:before {
  content: "\e9aa";
}
.ki-colors-square.ki-outline:before {
  content: "\e9ab";
}
.ki-compass.ki-outline:before {
  content: "\e9ac";
}
.ki-copy-success.ki-outline:before {
  content: "\e9ad";
}
.ki-copy.ki-outline:before {
  content: "\e9ae";
}
.ki-courier-express.ki-outline:before {
  content: "\e9af";
}
.ki-courier.ki-outline:before {
  content: "\e9b0";
}
.ki-credit-cart.ki-outline:before {
  content: "\e9b1";
}
.ki-cross-circle.ki-outline:before {
  content: "\e9b2";
}
.ki-cross-square.ki-outline:before {
  content: "\e9b3";
}
.ki-cross.ki-outline:before {
  content: "\e9b4";
}
.ki-crown-2.ki-outline:before {
  content: "\e9b5";
}
.ki-crown.ki-outline:before {
  content: "\e9b6";
}
.ki-css.ki-outline:before {
  content: "\e9b7";
}
.ki-cube-2.ki-outline:before {
  content: "\e9b8";
}
.ki-cube-3.ki-outline:before {
  content: "\e9b9";
}
.ki-cup.ki-outline:before {
  content: "\e9ba";
}
.ki-cursor.ki-outline:before {
  content: "\e9bb";
}
.ki-dash.ki-outline:before {
  content: "\e9bc";
}
.ki-data.ki-outline:before {
  content: "\e9bd";
}
.ki-delete-files.ki-outline:before {
  content: "\e9be";
}
.ki-delete-folder.ki-outline:before {
  content: "\e9bf";
}
.ki-delivery-2.ki-outline:before {
  content: "\e9c0";
}
.ki-delivery-3.ki-outline:before {
  content: "\e9c1";
}
.ki-delivery-24.ki-outline:before {
  content: "\e9c2";
}
.ki-delivery-door.ki-outline:before {
  content: "\e9c3";
}
.ki-delivery-geolocation.ki-outline:before {
  content: "\e9c4";
}
.ki-delivery-time.ki-outline:before {
  content: "\e9c5";
}
.ki-delivery.ki-outline:before {
  content: "\e9c6";
}
.ki-design-1.ki-outline:before {
  content: "\e9c7";
}
.ki-design-2.ki-outline:before {
  content: "\e9c8";
}
.ki-desktop-mobile.ki-outline:before {
  content: "\e9c9";
}
.ki-devices-2.ki-outline:before {
  content: "\e9ca";
}
.ki-devices.ki-outline:before {
  content: "\e9cb";
}
.ki-diamonds.ki-outline:before {
  content: "\e9cc";
}
.ki-directbox-default.ki-outline:before {
  content: "\e9cd";
}
.ki-disconnect.ki-outline:before {
  content: "\e9ce";
}
.ki-discount.ki-outline:before {
  content: "\e9cf";
}
.ki-disguise.ki-outline:before {
  content: "\e9d0";
}
.ki-disk.ki-outline:before {
  content: "\e9d1";
}
.ki-dislike.ki-outline:before {
  content: "\e9d2";
}
.ki-dj.ki-outline:before {
  content: "\e9d3";
}
.ki-document.ki-outline:before {
  content: "\e9d4";
}
.ki-double-check.ki-outline:before {
  content: "\e9d5";
}
.ki-dollar.ki-outline:before {
  content: "\e9d6";
}
.ki-dots-circle-vertical.ki-outline:before {
  content: "\e9d7";
}
.ki-dots-circle.ki-outline:before {
  content: "\e9d8";
}
.ki-dots-horizontal.ki-outline:before {
  content: "\e9d9";
}
.ki-dots-square-vertical.ki-outline:before {
  content: "\e9da";
}
.ki-dots-square.ki-outline:before {
  content: "\e9db";
}
.ki-dots-vertical.ki-outline:before {
  content: "\e9dc";
}
.ki-double-check-circle.ki-outline:before {
  content: "\e9dd";
}
.ki-double-down.ki-outline:before {
  content: "\e9de";
}
.ki-double-left-arrow.ki-outline:before {
  content: "\e9df";
}
.ki-double-left.ki-outline:before {
  content: "\e9e0";
}
.ki-double-right-arrow.ki-outline:before {
  content: "\e9e1";
}
.ki-double-right.ki-outline:before {
  content: "\e9e2";
}
.ki-double-up.ki-outline:before {
  content: "\e9e3";
}
.ki-down-square.ki-outline:before {
  content: "\e9e4";
}
.ki-down.ki-outline:before {
  content: "\e9e5";
}
.ki-dribbble.ki-outline:before {
  content: "\e9e6";
}
.ki-drop.ki-outline:before {
  content: "\e9e7";
}
.ki-dropbox.ki-outline:before {
  content: "\e9e8";
}
.ki-educare-ekt.ki-outline:before {
  content: "\e9e9";
}
.ki-electricity.ki-outline:before {
  content: "\e9ea";
}
.ki-electronic-clock.ki-outline:before {
  content: "\e9eb";
}
.ki-element-1.ki-outline:before {
  content: "\e9ec";
}
.ki-element-2.ki-outline:before {
  content: "\e9ed";
}
.ki-element-3.ki-outline:before {
  content: "\e9ee";
}
.ki-element-4.ki-outline:before {
  content: "\e9ef";
}
.ki-element-5.ki-outline:before {
  content: "\e9f0";
}
.ki-element-6.ki-outline:before {
  content: "\e9f1";
}
.ki-element-7.ki-outline:before {
  content: "\e9f2";
}
.ki-element-8.ki-outline:before {
  content: "\e9f3";
}
.ki-element-9.ki-outline:before {
  content: "\e9f4";
}
.ki-element-10.ki-outline:before {
  content: "\e9f5";
}
.ki-element-11.ki-outline:before {
  content: "\e9f6";
}
.ki-element-12.ki-outline:before {
  content: "\e9f7";
}
.ki-element-equal.ki-outline:before {
  content: "\e9f8";
}
.ki-element-plus.ki-outline:before {
  content: "\e9f9";
}
.ki-emoji-happy.ki-outline:before {
  content: "\e9fa";
}
.ki-enjin-coin-enj.ki-outline:before {
  content: "\e9fb";
}
.ki-ensure.ki-outline:before {
  content: "\e9fc";
}
.ki-entrance-left.ki-outline:before {
  content: "\e9fd";
}
.ki-entrance-right.ki-outline:before {
  content: "\e9fe";
}
.ki-eraser.ki-outline:before {
  content: "\e9ff";
}
.ki-euro.ki-outline:before {
  content: "\ea00";
}
.ki-exit-down.ki-outline:before {
  content: "\ea01";
}
.ki-exit-left.ki-outline:before {
  content: "\ea02";
}
.ki-exit-right-corner.ki-outline:before {
  content: "\ea03";
}
.ki-exit-right.ki-outline:before {
  content: "\ea04";
}
.ki-exit-up.ki-outline:before {
  content: "\ea05";
}
.ki-external-drive.ki-outline:before {
  content: "\ea06";
}
.ki-eye-slash.ki-outline:before {
  content: "\ea07";
}
.ki-eye.ki-outline:before {
  content: "\ea08";
}
.ki-face-id.ki-outline:before {
  content: "\ea09";
}
.ki-facebook.ki-outline:before {
  content: "\ea0a";
}
.ki-fasten.ki-outline:before {
  content: "\ea0b";
}
.ki-fatrows.ki-outline:before {
  content: "\ea0c";
}
.ki-feather.ki-outline:before {
  content: "\ea0d";
}
.ki-figma.ki-outline:before {
  content: "\ea0e";
}
.ki-file-added.ki-outline:before {
  content: "\ea0f";
}
.ki-file-deleted.ki-outline:before {
  content: "\ea10";
}
.ki-file-down.ki-outline:before {
  content: "\ea11";
}
.ki-file-left.ki-outline:before {
  content: "\ea12";
}
.ki-file-right.ki-outline:before {
  content: "\ea13";
}
.ki-file-sheet.ki-outline:before {
  content: "\ea14";
}
.ki-file-up.ki-outline:before {
  content: "\ea15";
}
.ki-files.ki-outline:before {
  content: "\ea16";
}
.ki-filter-edit.ki-outline:before {
  content: "\ea17";
}
.ki-filter-search.ki-outline:before {
  content: "\ea18";
}
.ki-filter-square.ki-outline:before {
  content: "\ea19";
}
.ki-filter-tablet.ki-outline:before {
  content: "\ea1a";
}
.ki-filter-tick.ki-outline:before {
  content: "\ea1b";
}
.ki-filter.ki-outline:before {
  content: "\ea1c";
}
.ki-financial-schedule.ki-outline:before {
  content: "\ea1d";
}
.ki-fingerprint-scanning.ki-outline:before {
  content: "\ea1e";
}
.ki-flag.ki-outline:before {
  content: "\ea1f";
}
.ki-flash-circle.ki-outline:before {
  content: "\ea20";
}
.ki-flask.ki-outline:before {
  content: "\ea21";
}
.ki-focus.ki-outline:before {
  content: "\ea22";
}
.ki-folder-added.ki-outline:before {
  content: "\ea23";
}
.ki-folder-down.ki-outline:before {
  content: "\ea24";
}
.ki-folder-up.ki-outline:before {
  content: "\ea25";
}
.ki-folder.ki-outline:before {
  content: "\ea26";
}
.ki-frame.ki-outline:before {
  content: "\ea27";
}
.ki-geolocation-home.ki-outline:before {
  content: "\ea28";
}
.ki-geolocation.ki-outline:before {
  content: "\ea29";
}
.ki-ghost.ki-outline:before {
  content: "\ea2a";
}
.ki-gift.ki-outline:before {
  content: "\ea2b";
}
.ki-github.ki-outline:before {
  content: "\ea2c";
}
.ki-glass.ki-outline:before {
  content: "\ea2d";
}
.ki-google-play.ki-outline:before {
  content: "\ea2e";
}
.ki-google.ki-outline:before {
  content: "\ea2f";
}
.ki-graph-2.ki-outline:before {
  content: "\ea30";
}
.ki-graph-3.ki-outline:before {
  content: "\ea31";
}
.ki-graph-4.ki-outline:before {
  content: "\ea32";
}
.ki-graph-up.ki-outline:before {
  content: "\ea33";
}
.ki-graph.ki-outline:before {
  content: "\ea34";
}
.ki-grid-2.ki-outline:before {
  content: "\ea35";
}
.ki-grid.ki-outline:before {
  content: "\ea36";
}
.ki-handcart.ki-outline:before {
  content: "\ea37";
}
.ki-happyemoji.ki-outline:before {
  content: "\ea38";
}
.ki-heart-circle.ki-outline:before {
  content: "\ea39";
}
.ki-heart.ki-outline:before {
  content: "\ea3a";
}
.ki-home-1.ki-outline:before {
  content: "\ea3b";
}
.ki-home-2.ki-outline:before {
  content: "\ea3c";
}
.ki-home-3.ki-outline:before {
  content: "\ea3d";
}
.ki-home.ki-outline:before {
  content: "\ea3e";
}
.ki-html.ki-outline:before {
  content: "\ea3f";
}
.ki-icon.ki-outline:before {
  content: "\ea40";
}
.ki-illustrator.ki-outline:before {
  content: "\ea41";
}
.ki-information-1.ki-outline:before {
  content: "\ea42";
}
.ki-information-2.ki-outline:before {
  content: "\ea43";
}
.ki-information-3.ki-outline:before {
  content: "\ea44";
}
.ki-information-4.ki-outline:before {
  content: "\ea45";
}
.ki-information.ki-outline:before {
  content: "\ea46";
}
.ki-instagram.ki-outline:before {
  content: "\ea47";
}
.ki-joystick.ki-outline:before {
  content: "\ea48";
}
.ki-js-2.ki-outline:before {
  content: "\ea49";
}
.ki-js.ki-outline:before {
  content: "\ea4a";
}
.ki-kanban.ki-outline:before {
  content: "\ea4b";
}
.ki-key-square.ki-outline:before {
  content: "\ea4c";
}
.ki-key.ki-outline:before {
  content: "\ea4d";
}
.ki-keyboard.ki-outline:before {
  content: "\ea4e";
}
.ki-laptop.ki-outline:before {
  content: "\ea4f";
}
.ki-laravel.ki-outline:before {
  content: "\ea50";
}
.ki-left-square.ki-outline:before {
  content: "\ea51";
}
.ki-left.ki-outline:before {
  content: "\ea52";
}
.ki-like-2.ki-outline:before {
  content: "\ea53";
}
.ki-like-folder.ki-outline:before {
  content: "\ea54";
}
.ki-like-shapes.ki-outline:before {
  content: "\ea55";
}
.ki-like-tag.ki-outline:before {
  content: "\ea56";
}
.ki-like.ki-outline:before {
  content: "\ea57";
}
.ki-loading.ki-outline:before {
  content: "\ea58";
}
.ki-lock-2.ki-outline:before {
  content: "\ea59";
}
.ki-lock-3.ki-outline:before {
  content: "\ea5a";
}
.ki-lock.ki-outline:before {
  content: "\ea5b";
}
.ki-logistic.ki-outline:before {
  content: "\ea5c";
}
.ki-lots-shopping.ki-outline:before {
  content: "\ea5d";
}
.ki-lovely.ki-outline:before {
  content: "\ea5e";
}
.ki-lts.ki-outline:before {
  content: "\ea5f";
}
.ki-magnifier.ki-outline:before {
  content: "\ea60";
}
.ki-map.ki-outline:before {
  content: "\ea61";
}
.ki-mask.ki-outline:before {
  content: "\ea62";
}
.ki-maximize.ki-outline:before {
  content: "\ea63";
}
.ki-medal-star.ki-outline:before {
  content: "\ea64";
}
.ki-menu.ki-outline:before {
  content: "\ea65";
}
.ki-message-add.ki-outline:before {
  content: "\ea66";
}
.ki-message-edit.ki-outline:before {
  content: "\ea67";
}
.ki-message-minus.ki-outline:before {
  content: "\ea68";
}
.ki-message-notify.ki-outline:before {
  content: "\ea69";
}
.ki-message-programming.ki-outline:before {
  content: "\ea6a";
}
.ki-message-question.ki-outline:before {
  content: "\ea6b";
}
.ki-message-text-2.ki-outline:before {
  content: "\ea6c";
}
.ki-message-text.ki-outline:before {
  content: "\ea6d";
}
.ki-messages.ki-outline:before {
  content: "\ea6e";
}
.ki-microsoft.ki-outline:before {
  content: "\ea6f";
}
.ki-milk.ki-outline:before {
  content: "\ea70";
}
.ki-minus-circle.ki-outline:before {
  content: "\ea71";
}
.ki-minus-folder.ki-outline:before {
  content: "\ea72";
}
.ki-minus-squared.ki-outline:before {
  content: "\ea73";
}
.ki-minus.ki-outline:before {
  content: "\ea74";
}
.ki-moon.ki-outline:before {
  content: "\ea75";
}
.ki-more-2.ki-outline:before {
  content: "\ea76";
}
.ki-mouse-circle.ki-outline:before {
  content: "\ea77";
}
.ki-mouse-square.ki-outline:before {
  content: "\ea78";
}
.ki-mouse.ki-outline:before {
  content: "\ea79";
}
.ki-nexo.ki-outline:before {
  content: "\ea7a";
}
.ki-night-day.ki-outline:before {
  content: "\ea7b";
}
.ki-note-2.ki-outline:before {
  content: "\ea7c";
}
.ki-note.ki-outline:before {
  content: "\ea7d";
}
.ki-notepad-bookmark.ki-outline:before {
  content: "\ea7e";
}
.ki-notepad-edit.ki-outline:before {
  content: "\ea7f";
}
.ki-notepad.ki-outline:before {
  content: "\ea80";
}
.ki-notification-1.ki-outline:before {
  content: "\ea81";
}
.ki-notification-bing.ki-outline:before {
  content: "\ea82";
}
.ki-notification-circle.ki-outline:before {
  content: "\ea83";
}
.ki-notification-favorite.ki-outline:before {
  content: "\ea84";
}
.ki-notification-on.ki-outline:before {
  content: "\ea85";
}
.ki-notification-status.ki-outline:before {
  content: "\ea86";
}
.ki-notification.ki-outline:before {
  content: "\ea87";
}
.ki-ocean.ki-outline:before {
  content: "\ea88";
}
.ki-office-bag.ki-outline:before {
  content: "\ea89";
}
.ki-package.ki-outline:before {
  content: "\ea8a";
}
.ki-pad.ki-outline:before {
  content: "\ea8b";
}
.ki-pails.ki-outline:before {
  content: "\ea8c";
}
.ki-paintbucket.ki-outline:before {
  content: "\ea8d";
}
.ki-paper-clip.ki-outline:before {
  content: "\ea8e";
}
.ki-paper-plane.ki-outline:before {
  content: "\ea8f";
}
.ki-parcel-tracking.ki-outline:before {
  content: "\ea90";
}
.ki-parcel.ki-outline:before {
  content: "\ea91";
}
.ki-password-check.ki-outline:before {
  content: "\ea92";
}
.ki-paypal.ki-outline:before {
  content: "\ea93";
}
.ki-pencil.ki-outline:before {
  content: "\ea94";
}
.ki-people.ki-outline:before {
  content: "\ea95";
}
.ki-percentage.ki-outline:before {
  content: "\ea96";
}
.ki-phone.ki-outline:before {
  content: "\ea97";
}
.ki-photoshop.ki-outline:before {
  content: "\ea98";
}
.ki-picture.ki-outline:before {
  content: "\ea99";
}
.ki-pill.ki-outline:before {
  content: "\ea9a";
}
.ki-pin.ki-outline:before {
  content: "\ea9b";
}
.ki-plus-circle.ki-outline:before {
  content: "\ea9c";
}
.ki-plus-squared.ki-outline:before {
  content: "\ea9d";
}
.ki-plus.ki-outline:before {
  content: "\ea9e";
}
.ki-pointers.ki-outline:before {
  content: "\ea9f";
}
.ki-price-tag.ki-outline:before {
  content: "\eaa0";
}
.ki-printer.ki-outline:before {
  content: "\eaa1";
}
.ki-profile-circle.ki-outline:before {
  content: "\eaa2";
}
.ki-pulse.ki-outline:before {
  content: "\eaa3";
}
.ki-purchase.ki-outline:before {
  content: "\eaa4";
}
.ki-python.ki-outline:before {
  content: "\eaa5";
}
.ki-question-2.ki-outline:before {
  content: "\eaa6";
}
.ki-question.ki-outline:before {
  content: "\eaa7";
}
.ki-questionnaire-tablet.ki-outline:before {
  content: "\eaa8";
}
.ki-ranking.ki-outline:before {
  content: "\eaa9";
}
.ki-react.ki-outline:before {
  content: "\eaaa";
}
.ki-receipt-square.ki-outline:before {
  content: "\eaab";
}
.ki-rescue.ki-outline:before {
  content: "\eaac";
}
.ki-right-left.ki-outline:before {
  content: "\eaad";
}
.ki-right-square.ki-outline:before {
  content: "\eaae";
}
.ki-right.ki-outline:before {
  content: "\eaaf";
}
.ki-rocket.ki-outline:before {
  content: "\eab0";
}
.ki-route.ki-outline:before {
  content: "\eab1";
}
.ki-router.ki-outline:before {
  content: "\eab2";
}
.ki-row-horizontal.ki-outline:before {
  content: "\eab3";
}
.ki-row-vertical.ki-outline:before {
  content: "\eab4";
}
.ki-safe-home.ki-outline:before {
  content: "\eab5";
}
.ki-satellite.ki-outline:before {
  content: "\eab6";
}
.ki-save-2.ki-outline:before {
  content: "\eab7";
}
.ki-save-deposit.ki-outline:before {
  content: "\eab8";
}
.ki-scan-barcode.ki-outline:before {
  content: "\eab9";
}
.ki-screen.ki-outline:before {
  content: "\eaba";
}
.ki-scroll.ki-outline:before {
  content: "\eabb";
}
.ki-search-list.ki-outline:before {
  content: "\eabc";
}
.ki-security-user.ki-outline:before {
  content: "\eabd";
}
.ki-setting-2.ki-outline:before {
  content: "\eabe";
}
.ki-setting-3.ki-outline:before {
  content: "\eabf";
}
.ki-setting-4.ki-outline:before {
  content: "\eac0";
}
.ki-setting.ki-outline:before {
  content: "\eac1";
}
.ki-share.ki-outline:before {
  content: "\eac2";
}
.ki-shield-cross.ki-outline:before {
  content: "\eac3";
}
.ki-shield-search.ki-outline:before {
  content: "\eac4";
}
.ki-shield-slash.ki-outline:before {
  content: "\eac5";
}
.ki-shield-tick.ki-outline:before {
  content: "\eac6";
}
.ki-shield.ki-outline:before {
  content: "\eac7";
}
.ki-ship.ki-outline:before {
  content: "\eac8";
}
.ki-shop.ki-outline:before {
  content: "\eac9";
}
.ki-simcard-2.ki-outline:before {
  content: "\eaca";
}
.ki-simcard.ki-outline:before {
  content: "\eacb";
}
.ki-size.ki-outline:before {
  content: "\eacc";
}
.ki-slack.ki-outline:before {
  content: "\eacd";
}
.ki-slider-horizontal-2.ki-outline:before {
  content: "\eace";
}
.ki-slider-horizontal.ki-outline:before {
  content: "\eacf";
}
.ki-slider-vertica.ki-outline:before {
  content: "\ead0";
}
.ki-slider-vertical.ki-outline:before {
  content: "\ead1";
}
.ki-slider.ki-outline:before {
  content: "\ead2";
}
.ki-sms.ki-outline:before {
  content: "\ead3";
}
.ki-snapchat.ki-outline:before {
  content: "\ead4";
}
.ki-social-media.ki-outline:before {
  content: "\ead5";
}
.ki-soft-2.ki-outline:before {
  content: "\ead6";
}
.ki-soft-3.ki-outline:before {
  content: "\ead7";
}
.ki-soft.ki-outline:before {
  content: "\ead8";
}
.ki-some-files.ki-outline:before {
  content: "\ead9";
}
.ki-sort.ki-outline:before {
  content: "\eada";
}
.ki-speaker.ki-outline:before {
  content: "\eadb";
}
.ki-spotify.ki-outline:before {
  content: "\eadc";
}
.ki-spring-framework.ki-outline:before {
  content: "\eadd";
}
.ki-square-brackets.ki-outline:before {
  content: "\eade";
}
.ki-star.ki-outline:before {
  content: "\eadf";
}
.ki-status.ki-outline:before {
  content: "\eae0";
}
.ki-subtitle.ki-outline:before {
  content: "\eae1";
}
.ki-sun.ki-outline:before {
  content: "\eae2";
}
.ki-support.ki-outline:before {
  content: "\eae3";
}
.ki-switch.ki-outline:before {
  content: "\eae4";
}
.ki-syringe.ki-outline:before {
  content: "\eae5";
}
.ki-tab-tablet.ki-outline:before {
  content: "\eae6";
}
.ki-tablet-delete.ki-outline:before {
  content: "\eae7";
}
.ki-tablet-down.ki-outline:before {
  content: "\eae8";
}
.ki-tablet-ok.ki-outline:before {
  content: "\eae9";
}
.ki-tablet-text-down.ki-outline:before {
  content: "\eaea";
}
.ki-tablet-text-up.ki-outline:before {
  content: "\eaeb";
}
.ki-tablet-up.ki-outline:before {
  content: "\eaec";
}
.ki-tablet.ki-outline:before {
  content: "\eaed";
}
.ki-tag-cross.ki-outline:before {
  content: "\eaee";
}
.ki-tag.ki-outline:before {
  content: "\eaef";
}
.ki-teacher.ki-outline:before {
  content: "\eaf0";
}
.ki-technology-1.ki-outline:before {
  content: "\eaf1";
}
.ki-technology-2.ki-outline:before {
  content: "\eaf2";
}
.ki-technology-3.ki-outline:before {
  content: "\eaf3";
}
.ki-technology-4.ki-outline:before {
  content: "\eaf4";
}
.ki-telephone-geolocation.ki-outline:before {
  content: "\eaf5";
}
.ki-test-tubes.ki-outline:before {
  content: "\eaf6";
}
.ki-text-bold.ki-outline:before {
  content: "\eaf7";
}
.ki-text-circle.ki-outline:before {
  content: "\eaf8";
}
.ki-text-italic.ki-outline:before {
  content: "\eaf9";
}
.ki-text-number.ki-outline:before {
  content: "\eafa";
}
.ki-text-strikethrough.ki-outline:before {
  content: "\eafb";
}
.ki-text-underline.ki-outline:before {
  content: "\eafc";
}
.ki-text.ki-outline:before {
  content: "\eafd";
}
.ki-textalign-center.ki-outline:before {
  content: "\eafe";
}
.ki-textalign-justifycenter.ki-outline:before {
  content: "\eaff";
}
.ki-textalign-left.ki-outline:before {
  content: "\eb00";
}
.ki-textalign-right.ki-outline:before {
  content: "\eb01";
}
.ki-thermometer.ki-outline:before {
  content: "\eb02";
}
.ki-theta-theta.ki-outline:before {
  content: "\eb03";
}
.ki-tiktok.ki-outline:before {
  content: "\eb04";
}
.ki-time.ki-outline:before {
  content: "\eb05";
}
.ki-timer.ki-outline:before {
  content: "\eb06";
}
.ki-to-left.ki-outline:before {
  content: "\eb07";
}
.ki-to-right.ki-outline:before {
  content: "\eb08";
}
.ki-toggle-off-circle.ki-outline:before {
  content: "\eb09";
}
.ki-toggle-off.ki-outline:before {
  content: "\eb0a";
}
.ki-toggle-on-circle.ki-outline:before {
  content: "\eb0b";
}
.ki-toggle-on.ki-outline:before {
  content: "\eb0c";
}
.ki-trash-square.ki-outline:before {
  content: "\eb0d";
}
.ki-trash.ki-outline:before {
  content: "\eb0e";
}
.ki-tree.ki-outline:before {
  content: "\eb0f";
}
.ki-trello.ki-outline:before {
  content: "\eb10";
}
.ki-ts.ki-outline:before {
  content: "\eb11";
}
.ki-twitch.ki-outline:before {
  content: "\eb12";
}
.ki-twitter.ki-outline:before {
  content: "\eb13";
}
.ki-two-credit-cart.ki-outline:before {
  content: "\eb14";
}
.ki-underlining.ki-outline:before {
  content: "\eb15";
}
.ki-up-diagonal.ki-outline:before {
  content: "\eb16";
}
.ki-up-down.ki-outline:before {
  content: "\eb17";
}
.ki-up-square.ki-outline:before {
  content: "\eb18";
}
.ki-up.ki-outline:before {
  content: "\eb19";
}
.ki-update-file.ki-outline:before {
  content: "\eb1a";
}
.ki-update-folder.ki-outline:before {
  content: "\eb1b";
}
.ki-user-edit.ki-outline:before {
  content: "\eb1c";
}
.ki-user-square.ki-outline:before {
  content: "\eb1d";
}
.ki-user-tick.ki-outline:before {
  content: "\eb1e";
}
.ki-user.ki-outline:before {
  content: "\eb1f";
}
.ki-users.ki-outline:before {
  content: "\eb20";
}
.ki-verify.ki-outline:before {
  content: "\eb21";
}
.ki-vibe-vibe.ki-outline:before {
  content: "\eb22";
}
.ki-virus.ki-outline:before {
  content: "\eb23";
}
.ki-vue.ki-outline:before {
  content: "\eb24";
}
.ki-vuesax.ki-outline:before {
  content: "\eb25";
}
.ki-wallet.ki-outline:before {
  content: "\eb26";
}
.ki-wanchain-wan.ki-outline:before {
  content: "\eb27";
}
.ki-watch.ki-outline:before {
  content: "\eb28";
}
.ki-whatsapp.ki-outline:before {
  content: "\eb29";
}
.ki-wifi-home.ki-outline:before {
  content: "\eb2a";
}
.ki-wifi-square.ki-outline:before {
  content: "\eb2b";
}
.ki-wifi.ki-outline:before {
  content: "\eb2c";
}
.ki-wireframe.ki-outline:before {
  content: "\eb2d";
}
.ki-wlan.ki-outline:before {
  content: "\eb2e";
}
.ki-wrench.ki-outline:before {
  content: "\eb2f";
}
.ki-xaomi.ki-outline:before {
  content: "\eb30";
}
.ki-xd.ki-outline:before {
  content: "\eb31";
}
.ki-xmr.ki-outline:before {
  content: "\eb32";
}
.ki-yii.ki-outline:before {
  content: "\eb33";
}
.ki-youtube.ki-outline:before {
  content: "\eb34";
}

@font-face {
  font-family: 'keenicons-solid';
  src:
    url('fonts/keenicons-solid.ttf?m54ea1') format('truetype'),
    url('fonts/keenicons-solid.woff?m54ea1') format('woff'),
    url('fonts/keenicons-solid.svg?m54ea1#keenicons-solid') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.ki-solid {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'keenicons-solid' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  display: inline-flex;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ki-abstract-1.ki-solid:before {
  content: "\e900";
}
.ki-abstract-2.ki-solid:before {
  content: "\e901";
}
.ki-abstract-3.ki-solid:before {
  content: "\e902";
}
.ki-abstract-4.ki-solid:before {
  content: "\e903";
}
.ki-abstract-5.ki-solid:before {
  content: "\e904";
}
.ki-abstract-6.ki-solid:before {
  content: "\e905";
}
.ki-abstract-7.ki-solid:before {
  content: "\e906";
}
.ki-abstract-8.ki-solid:before {
  content: "\e907";
}
.ki-abstract-9.ki-solid:before {
  content: "\e908";
}
.ki-abstract-10.ki-solid:before {
  content: "\e909";
}
.ki-abstract-11.ki-solid:before {
  content: "\e90a";
}
.ki-abstract-12.ki-solid:before {
  content: "\e90b";
}
.ki-abstract-13.ki-solid:before {
  content: "\e90c";
}
.ki-abstract-14.ki-solid:before {
  content: "\e90d";
}
.ki-abstract-15.ki-solid:before {
  content: "\e90e";
}
.ki-abstract-16.ki-solid:before {
  content: "\e90f";
}
.ki-abstract-17.ki-solid:before {
  content: "\e910";
}
.ki-abstract-18.ki-solid:before {
  content: "\e911";
}
.ki-abstract-19.ki-solid:before {
  content: "\e912";
}
.ki-abstract-20.ki-solid:before {
  content: "\e913";
}
.ki-abstract-21.ki-solid:before {
  content: "\e914";
}
.ki-abstract-22.ki-solid:before {
  content: "\e915";
}
.ki-abstract-23.ki-solid:before {
  content: "\e916";
}
.ki-abstract-24.ki-solid:before {
  content: "\e917";
}
.ki-abstract-25.ki-solid:before {
  content: "\e918";
}
.ki-abstract-26.ki-solid:before {
  content: "\e919";
}
.ki-abstract-27.ki-solid:before {
  content: "\e91a";
}
.ki-abstract-28.ki-solid:before {
  content: "\e91b";
}
.ki-abstract-29.ki-solid:before {
  content: "\e91c";
}
.ki-abstract-30.ki-solid:before {
  content: "\e91d";
}
.ki-abstract-31.ki-solid:before {
  content: "\e91e";
}
.ki-abstract-32.ki-solid:before {
  content: "\e91f";
}
.ki-abstract-33.ki-solid:before {
  content: "\e920";
}
.ki-abstract-34.ki-solid:before {
  content: "\e921";
}
.ki-abstract-35.ki-solid:before {
  content: "\e922";
}
.ki-abstract-36.ki-solid:before {
  content: "\e923";
}
.ki-abstract-37.ki-solid:before {
  content: "\e924";
}
.ki-abstract-38.ki-solid:before {
  content: "\e925";
}
.ki-abstract-39.ki-solid:before {
  content: "\e926";
}
.ki-abstract-40.ki-solid:before {
  content: "\e927";
}
.ki-abstract-41.ki-solid:before {
  content: "\e928";
}
.ki-abstract-42.ki-solid:before {
  content: "\e929";
}
.ki-abstract-43.ki-solid:before {
  content: "\e92a";
}
.ki-abstract-44.ki-solid:before {
  content: "\e92b";
}
.ki-abstract-45.ki-solid:before {
  content: "\e92c";
}
.ki-abstract-46.ki-solid:before {
  content: "\e92d";
}
.ki-abstract-47.ki-solid:before {
  content: "\e92e";
}
.ki-abstract-48.ki-solid:before {
  content: "\e92f";
}
.ki-abstract-49.ki-solid:before {
  content: "\e930";
}
.ki-abstract.ki-solid:before {
  content: "\e931";
}
.ki-add-files.ki-solid:before {
  content: "\e932";
}
.ki-add-folder.ki-solid:before {
  content: "\e933";
}
.ki-add-notepad.ki-solid:before {
  content: "\e934";
}
.ki-additem.ki-solid:before {
  content: "\e935";
}
.ki-address-book.ki-solid:before {
  content: "\e936";
}
.ki-airplane-square.ki-solid:before {
  content: "\e937";
}
.ki-airplane.ki-solid:before {
  content: "\e938";
}
.ki-airpod.ki-solid:before {
  content: "\e939";
}
.ki-android.ki-solid:before {
  content: "\e93a";
}
.ki-angular.ki-solid:before {
  content: "\e93b";
}
.ki-apple.ki-solid:before {
  content: "\e93c";
}
.ki-archive-tick.ki-solid:before {
  content: "\e93d";
}
.ki-archive.ki-solid:before {
  content: "\e93e";
}
.ki-arrow-circle-left.ki-solid:before {
  content: "\e93f";
}
.ki-arrow-circle-right.ki-solid:before {
  content: "\e940";
}
.ki-arrow-down-left.ki-solid:before {
  content: "\e941";
}
.ki-arrow-down-refraction.ki-solid:before {
  content: "\e942";
}
.ki-arrow-down-right.ki-solid:before {
  content: "\e943";
}
.ki-arrow-down.ki-solid:before {
  content: "\e944";
}
.ki-arrow-left.ki-solid:before {
  content: "\e945";
}
.ki-arrow-mix.ki-solid:before {
  content: "\e946";
}
.ki-arrow-right-left.ki-solid:before {
  content: "\e947";
}
.ki-arrow-right.ki-solid:before {
  content: "\e948";
}
.ki-arrow-two-diagonals.ki-solid:before {
  content: "\e949";
}
.ki-arrow-up-down.ki-solid:before {
  content: "\e94a";
}
.ki-arrow-up-left.ki-solid:before {
  content: "\e94b";
}
.ki-arrow-up-refraction.ki-solid:before {
  content: "\e94c";
}
.ki-arrow-up-right.ki-solid:before {
  content: "\e94d";
}
.ki-arrow-up.ki-solid:before {
  content: "\e94e";
}
.ki-arrow-zigzag.ki-solid:before {
  content: "\e94f";
}
.ki-arrows-circle.ki-solid:before {
  content: "\e950";
}
.ki-arrows-loop.ki-solid:before {
  content: "\e951";
}
.ki-artificial-intelligence.ki-solid:before {
  content: "\e952";
}
.ki-autobrightness.ki-solid:before {
  content: "\e953";
}
.ki-avalanche-avax.ki-solid:before {
  content: "\e954";
}
.ki-award.ki-solid:before {
  content: "\e955";
}
.ki-badge.ki-solid:before {
  content: "\e956";
}
.ki-bandage.ki-solid:before {
  content: "\e957";
}
.ki-bank.ki-solid:before {
  content: "\e958";
}
.ki-bar-chart.ki-solid:before {
  content: "\e959";
}
.ki-barcode.ki-solid:before {
  content: "\e95a";
}
.ki-basket-ok.ki-solid:before {
  content: "\e95b";
}
.ki-basket.ki-solid:before {
  content: "\e95c";
}
.ki-behance.ki-solid:before {
  content: "\e95d";
}
.ki-bill.ki-solid:before {
  content: "\e95e";
}
.ki-binance-usd-busd.ki-solid:before {
  content: "\e95f";
}
.ki-binance.ki-solid:before {
  content: "\e960";
}
.ki-bitcoin.ki-solid:before {
  content: "\e961";
}
.ki-black-down.ki-solid:before {
  content: "\e962";
}
.ki-black-left-line.ki-solid:before {
  content: "\e963";
}
.ki-black-left.ki-solid:before {
  content: "\e964";
}
.ki-black-right-line.ki-solid:before {
  content: "\e965";
}
.ki-black-right.ki-solid:before {
  content: "\e966";
}
.ki-black-up.ki-solid:before {
  content: "\e967";
}
.ki-bluetooth.ki-solid:before {
  content: "\e968";
}
.ki-book-open.ki-solid:before {
  content: "\e969";
}
.ki-book-square.ki-solid:before {
  content: "\e96a";
}
.ki-book.ki-solid:before {
  content: "\e96b";
}
.ki-bookmark-2.ki-solid:before {
  content: "\e96c";
}
.ki-bookmark.ki-solid:before {
  content: "\e96d";
}
.ki-bootstrap.ki-solid:before {
  content: "\e96e";
}
.ki-briefcase.ki-solid:before {
  content: "\e96f";
}
.ki-brifecase-cros.ki-solid:before {
  content: "\e970";
}
.ki-brifecase-tick.ki-solid:before {
  content: "\e971";
}
.ki-brifecase-timer.ki-solid:before {
  content: "\e972";
}
.ki-brush.ki-solid:before {
  content: "\e973";
}
.ki-bucket-square.ki-solid:before {
  content: "\e974";
}
.ki-bucket.ki-solid:before {
  content: "\e975";
}
.ki-burger-menu-1.ki-solid:before {
  content: "\e976";
}
.ki-burger-menu-2.ki-solid:before {
  content: "\e977";
}
.ki-burger-menu-3.ki-solid:before {
  content: "\e978";
}
.ki-burger-menu-4.ki-solid:before {
  content: "\e979";
}
.ki-burger-menu-5.ki-solid:before {
  content: "\e97a";
}
.ki-burger-menu-6.ki-solid:before {
  content: "\e97b";
}
.ki-burger-menu.ki-solid:before {
  content: "\e97c";
}
.ki-bus.ki-solid:before {
  content: "\e97d";
}
.ki-calculator.ki-solid:before {
  content: "\e97e";
}
.ki-calculatoror.ki-solid:before {
  content: "\e97f";
}
.ki-calendar-2.ki-solid:before {
  content: "\e980";
}
.ki-calendar-8.ki-solid:before {
  content: "\e981";
}
.ki-calendar-add.ki-solid:before {
  content: "\e982";
}
.ki-calendar-edit.ki-solid:before {
  content: "\e983";
}
.ki-calendar-remove.ki-solid:before {
  content: "\e984";
}
.ki-calendar-search.ki-solid:before {
  content: "\e985";
}
.ki-calendar-tick.ki-solid:before {
  content: "\e986";
}
.ki-calendar.ki-solid:before {
  content: "\e987";
}
.ki-call.ki-solid:before {
  content: "\e988";
}
.ki-capsule.ki-solid:before {
  content: "\e989";
}
.ki-car.ki-solid:before {
  content: "\e98a";
}
.ki-category.ki-solid:before {
  content: "\e98b";
}
.ki-cd.ki-solid:before {
  content: "\e98c";
}
.ki-celsius-cel.ki-solid:before {
  content: "\e98d";
}
.ki-chart-line-down-2.ki-solid:before {
  content: "\e98e";
}
.ki-chart-line-down.ki-solid:before {
  content: "\e98f";
}
.ki-chart-line-star.ki-solid:before {
  content: "\e990";
}
.ki-chart-line-up-2.ki-solid:before {
  content: "\e991";
}
.ki-chart-line-up.ki-solid:before {
  content: "\e992";
}
.ki-chart-line.ki-solid:before {
  content: "\e993";
}
.ki-chart-pie-3.ki-solid:before {
  content: "\e994";
}
.ki-chart-pie-4.ki-solid:before {
  content: "\e995";
}
.ki-chart-pie-simple.ki-solid:before {
  content: "\e996";
}
.ki-chart-pie-too.ki-solid:before {
  content: "\e997";
}
.ki-chart-simple-2.ki-solid:before {
  content: "\e998";
}
.ki-chart-simple-3.ki-solid:before {
  content: "\e999";
}
.ki-chart-simple.ki-solid:before {
  content: "\e99a";
}
.ki-chart.ki-solid:before {
  content: "\e99b";
}
.ki-check-circle.ki-solid:before {
  content: "\e99c";
}
.ki-check-squared.ki-solid:before {
  content: "\e99d";
}
.ki-check.ki-solid:before {
  content: "\e99e";
}
.ki-cheque.ki-solid:before {
  content: "\e99f";
}
.ki-chrome.ki-solid:before {
  content: "\e9a0";
}
.ki-classmates.ki-solid:before {
  content: "\e9a1";
}
.ki-click.ki-solid:before {
  content: "\e9a2";
}
.ki-clipboard.ki-solid:before {
  content: "\e9a3";
}
.ki-cloud-add.ki-solid:before {
  content: "\e9a4";
}
.ki-cloud-change.ki-solid:before {
  content: "\e9a5";
}
.ki-cloud-download.ki-solid:before {
  content: "\e9a6";
}
.ki-cloud.ki-solid:before {
  content: "\e9a7";
}
.ki-code.ki-solid:before {
  content: "\e9a8";
}
.ki-coffee.ki-solid:before {
  content: "\e9a9";
}
.ki-color-swatch.ki-solid:before {
  content: "\e9aa";
}
.ki-colors-square.ki-solid:before {
  content: "\e9ab";
}
.ki-compass.ki-solid:before {
  content: "\e9ac";
}
.ki-copy-success.ki-solid:before {
  content: "\e9ad";
}
.ki-copy.ki-solid:before {
  content: "\e9ae";
}
.ki-courier-express.ki-solid:before {
  content: "\e9af";
}
.ki-courier.ki-solid:before {
  content: "\e9b0";
}
.ki-credit-cart.ki-solid:before {
  content: "\e9b1";
}
.ki-cross-circle.ki-solid:before {
  content: "\e9b2";
}
.ki-cross-square.ki-solid:before {
  content: "\e9b3";
}
.ki-cross.ki-solid:before {
  content: "\e9b4";
}
.ki-crown-2.ki-solid:before {
  content: "\e9b5";
}
.ki-crown.ki-solid:before {
  content: "\e9b6";
}
.ki-css.ki-solid:before {
  content: "\e9b7";
}
.ki-cube-2.ki-solid:before {
  content: "\e9b8";
}
.ki-cube-3.ki-solid:before {
  content: "\e9b9";
}
.ki-cup.ki-solid:before {
  content: "\e9ba";
}
.ki-cursor.ki-solid:before {
  content: "\e9bb";
}
.ki-dash.ki-solid:before {
  content: "\e9bc";
}
.ki-data.ki-solid:before {
  content: "\e9bd";
}
.ki-delete-files.ki-solid:before {
  content: "\e9be";
}
.ki-delete-folder.ki-solid:before {
  content: "\e9bf";
}
.ki-delivery-2.ki-solid:before {
  content: "\e9c0";
}
.ki-delivery-3.ki-solid:before {
  content: "\e9c1";
}
.ki-delivery-24.ki-solid:before {
  content: "\e9c2";
}
.ki-delivery-door.ki-solid:before {
  content: "\e9c3";
}
.ki-delivery-geolocation.ki-solid:before {
  content: "\e9c4";
}
.ki-delivery-time.ki-solid:before {
  content: "\e9c5";
}
.ki-delivery.ki-solid:before {
  content: "\e9c6";
}
.ki-design-1.ki-solid:before {
  content: "\e9c7";
}
.ki-design-2.ki-solid:before {
  content: "\e9c8";
}
.ki-desktop-mobile.ki-solid:before {
  content: "\e9c9";
}
.ki-devices-2.ki-solid:before {
  content: "\e9ca";
}
.ki-devices.ki-solid:before {
  content: "\e9cb";
}
.ki-diamonds.ki-solid:before {
  content: "\e9cc";
}
.ki-directbox-default.ki-solid:before {
  content: "\e9cd";
}
.ki-disconnect.ki-solid:before {
  content: "\e9ce";
}
.ki-discount.ki-solid:before {
  content: "\e9cf";
}
.ki-disguise.ki-solid:before {
  content: "\e9d0";
}
.ki-disk.ki-solid:before {
  content: "\e9d1";
}
.ki-dislike.ki-solid:before {
  content: "\e9d2";
}
.ki-dj.ki-solid:before {
  content: "\e9d3";
}
.ki-document.ki-solid:before {
  content: "\e9d4";
}
.ki-double-check.ki-solid:before {
  content: "\e9d5";
}
.ki-dollar.ki-solid:before {
  content: "\e9d6";
}
.ki-dots-circle-vertical.ki-solid:before {
  content: "\e9d7";
}
.ki-dots-circle.ki-solid:before {
  content: "\e9d8";
}
.ki-dots-horizontal.ki-solid:before {
  content: "\e9d9";
}
.ki-dots-square-vertical.ki-solid:before {
  content: "\e9da";
}
.ki-dots-square.ki-solid:before {
  content: "\e9db";
}
.ki-dots-vertical.ki-solid:before {
  content: "\e9dc";
}
.ki-double-down.ki-solid:before {
  content: "\e9dd";
}
.ki-double-left-arrow.ki-solid:before {
  content: "\e9de";
}
.ki-double-left.ki-solid:before {
  content: "\e9df";
}
.ki-double-right-arrow.ki-solid:before {
  content: "\e9e0";
}
.ki-double-right.ki-solid:before {
  content: "\e9e1";
}
.ki-double-up.ki-solid:before {
  content: "\e9e2";
}
.ki-down-square.ki-solid:before {
  content: "\e9e3";
}
.ki-down.ki-solid:before {
  content: "\e9e4";
}
.ki-dribbble.ki-solid:before {
  content: "\e9e5";
}
.ki-drop.ki-solid:before {
  content: "\e9e6";
}
.ki-dropbox.ki-solid:before {
  content: "\e9e7";
}
.ki-duble-check-circle.ki-solid:before {
  content: "\e9e8";
}
.ki-educare-ekt.ki-solid:before {
  content: "\e9e9";
}
.ki-electricity.ki-solid:before {
  content: "\e9ea";
}
.ki-electronic-clock.ki-solid:before {
  content: "\e9eb";
}
.ki-element-1.ki-solid:before {
  content: "\e9ec";
}
.ki-element-2.ki-solid:before {
  content: "\e9ed";
}
.ki-element-3.ki-solid:before {
  content: "\e9ee";
}
.ki-element-4.ki-solid:before {
  content: "\e9ef";
}
.ki-element-5.ki-solid:before {
  content: "\e9f0";
}
.ki-element-6.ki-solid:before {
  content: "\e9f1";
}
.ki-element-7.ki-solid:before {
  content: "\e9f2";
}
.ki-element-8.ki-solid:before {
  content: "\e9f3";
}
.ki-element-9.ki-solid:before {
  content: "\e9f4";
}
.ki-element-10.ki-solid:before {
  content: "\e9f5";
}
.ki-element-11.ki-solid:before {
  content: "\e9f6";
}
.ki-element-12.ki-solid:before {
  content: "\e9f7";
}
.ki-element-equal.ki-solid:before {
  content: "\e9f8";
}
.ki-element-plus.ki-solid:before {
  content: "\e9f9";
}
.ki-emoji-happy.ki-solid:before {
  content: "\e9fa";
}
.ki-enjin-coin-enj.ki-solid:before {
  content: "\e9fb";
}
.ki-ensure.ki-solid:before {
  content: "\e9fc";
}
.ki-entrance-left.ki-solid:before {
  content: "\e9fd";
}
.ki-entrance-right.ki-solid:before {
  content: "\e9fe";
}
.ki-eraser.ki-solid:before {
  content: "\e9ff";
}
.ki-euro.ki-solid:before {
  content: "\ea00";
}
.ki-exit-down.ki-solid:before {
  content: "\ea01";
}
.ki-exit-left.ki-solid:before {
  content: "\ea02";
}
.ki-exit-right-corner.ki-solid:before {
  content: "\ea03";
}
.ki-exit-right.ki-solid:before {
  content: "\ea04";
}
.ki-exit-up.ki-solid:before {
  content: "\ea05";
}
.ki-external-drive.ki-solid:before {
  content: "\ea06";
}
.ki-eye-slash.ki-solid:before {
  content: "\ea07";
}
.ki-eye.ki-solid:before {
  content: "\ea08";
}
.ki-face-id.ki-solid:before {
  content: "\ea09";
}
.ki-facebook.ki-solid:before {
  content: "\ea0a";
}
.ki-fasten.ki-solid:before {
  content: "\ea0b";
}
.ki-fatrows.ki-solid:before {
  content: "\ea0c";
}
.ki-feather.ki-solid:before {
  content: "\ea0d";
}
.ki-figma.ki-solid:before {
  content: "\ea0e";
}
.ki-file-added.ki-solid:before {
  content: "\ea0f";
}
.ki-file-deleted.ki-solid:before {
  content: "\ea10";
}
.ki-file-down.ki-solid:before {
  content: "\ea11";
}
.ki-file-left.ki-solid:before {
  content: "\ea12";
}
.ki-file-right.ki-solid:before {
  content: "\ea13";
}
.ki-file-sheet.ki-solid:before {
  content: "\ea14";
}
.ki-file-up.ki-solid:before {
  content: "\ea15";
}
.ki-files.ki-solid:before {
  content: "\ea16";
}
.ki-filter-edit.ki-solid:before {
  content: "\ea17";
}
.ki-filter-search.ki-solid:before {
  content: "\ea18";
}
.ki-filter-square.ki-solid:before {
  content: "\ea19";
}
.ki-filter-tablet.ki-solid:before {
  content: "\ea1a";
}
.ki-filter-tick.ki-solid:before {
  content: "\ea1b";
}
.ki-filter.ki-solid:before {
  content: "\ea1c";
}
.ki-financial-schedule.ki-solid:before {
  content: "\ea1d";
}
.ki-fingerprint-scanning.ki-solid:before {
  content: "\ea1e";
}
.ki-flag.ki-solid:before {
  content: "\ea1f";
}
.ki-flash-circle.ki-solid:before {
  content: "\ea20";
}
.ki-flask.ki-solid:before {
  content: "\ea21";
}
.ki-focus.ki-solid:before {
  content: "\ea22";
}
.ki-folder-added.ki-solid:before {
  content: "\ea23";
}
.ki-folder-down.ki-solid:before {
  content: "\ea24";
}
.ki-folder-up.ki-solid:before {
  content: "\ea25";
}
.ki-folder.ki-solid:before {
  content: "\ea26";
}
.ki-frame.ki-solid:before {
  content: "\ea27";
}
.ki-geolocation-home.ki-solid:before {
  content: "\ea28";
}
.ki-geolocation.ki-solid:before {
  content: "\ea29";
}
.ki-ghost.ki-solid:before {
  content: "\ea2a";
}
.ki-gift.ki-solid:before {
  content: "\ea2b";
}
.ki-github.ki-solid:before {
  content: "\ea2c";
}
.ki-glass.ki-solid:before {
  content: "\ea2d";
}
.ki-google-play.ki-solid:before {
  content: "\ea2e";
}
.ki-google.ki-solid:before {
  content: "\ea2f";
}
.ki-graph-2.ki-solid:before {
  content: "\ea30";
}
.ki-graph-3.ki-solid:before {
  content: "\ea31";
}
.ki-graph-4.ki-solid:before {
  content: "\ea32";
}
.ki-graph-up.ki-solid:before {
  content: "\ea33";
}
.ki-graph.ki-solid:before {
  content: "\ea34";
}
.ki-grid-2.ki-solid:before {
  content: "\ea35";
}
.ki-grid.ki-solid:before {
  content: "\ea36";
}
.ki-handcart.ki-solid:before {
  content: "\ea37";
}
.ki-happyemoji.ki-solid:before {
  content: "\ea38";
}
.ki-heart-circle.ki-solid:before {
  content: "\ea39";
}
.ki-heart.ki-solid:before {
  content: "\ea3a";
}
.ki-home-1.ki-solid:before {
  content: "\ea3b";
}
.ki-home-2.ki-solid:before {
  content: "\ea3c";
}
.ki-home-3.ki-solid:before {
  content: "\ea3d";
}
.ki-home.ki-solid:before {
  content: "\ea3e";
}
.ki-html.ki-solid:before {
  content: "\ea3f";
}
.ki-icon.ki-solid:before {
  content: "\ea40";
}
.ki-illustrator.ki-solid:before {
  content: "\ea41";
}
.ki-information-1.ki-solid:before {
  content: "\ea42";
}
.ki-information-2.ki-solid:before {
  content: "\ea43";
}
.ki-information-3.ki-solid:before {
  content: "\ea44";
}
.ki-information-4.ki-solid:before {
  content: "\ea45";
}
.ki-information.ki-solid:before {
  content: "\ea46";
}
.ki-instagram.ki-solid:before {
  content: "\ea47";
}
.ki-joystick.ki-solid:before {
  content: "\ea48";
}
.ki-js-2.ki-solid:before {
  content: "\ea49";
}
.ki-js.ki-solid:before {
  content: "\ea4a";
}
.ki-kanban.ki-solid:before {
  content: "\ea4b";
}
.ki-key-square.ki-solid:before {
  content: "\ea4c";
}
.ki-key.ki-solid:before {
  content: "\ea4d";
}
.ki-keyboard.ki-solid:before {
  content: "\ea4e";
}
.ki-laptop.ki-solid:before {
  content: "\ea4f";
}
.ki-laravel.ki-solid:before {
  content: "\ea50";
}
.ki-left-square.ki-solid:before {
  content: "\ea51";
}
.ki-left.ki-solid:before {
  content: "\ea52";
}
.ki-like-2.ki-solid:before {
  content: "\ea53";
}
.ki-like-folder.ki-solid:before {
  content: "\ea54";
}
.ki-like-shapes.ki-solid:before {
  content: "\ea55";
}
.ki-like-tag.ki-solid:before {
  content: "\ea56";
}
.ki-like.ki-solid:before {
  content: "\ea57";
}
.ki-loading.ki-solid:before {
  content: "\ea58";
}
.ki-lock-2.ki-solid:before {
  content: "\ea59";
}
.ki-lock-3.ki-solid:before {
  content: "\ea5a";
}
.ki-lock.ki-solid:before {
  content: "\ea5b";
}
.ki-logistic.ki-solid:before {
  content: "\ea5c";
}
.ki-lots-shopping.ki-solid:before {
  content: "\ea5d";
}
.ki-lovely.ki-solid:before {
  content: "\ea5e";
}
.ki-lts.ki-solid:before {
  content: "\ea5f";
}
.ki-magnifier.ki-solid:before {
  content: "\ea60";
}
.ki-map.ki-solid:before {
  content: "\ea61";
}
.ki-mask.ki-solid:before {
  content: "\ea62";
}
.ki-maximize.ki-solid:before {
  content: "\ea63";
}
.ki-medal-star.ki-solid:before {
  content: "\ea64";
}
.ki-menu.ki-solid:before {
  content: "\ea65";
}
.ki-message-add.ki-solid:before {
  content: "\ea66";
}
.ki-message-edit.ki-solid:before {
  content: "\ea67";
}
.ki-message-minus.ki-solid:before {
  content: "\ea68";
}
.ki-message-notify.ki-solid:before {
  content: "\ea69";
}
.ki-message-programming.ki-solid:before {
  content: "\ea6a";
}
.ki-message-question.ki-solid:before {
  content: "\ea6b";
}
.ki-message-text-2.ki-solid:before {
  content: "\ea6c";
}
.ki-message-text.ki-solid:before {
  content: "\ea6d";
}
.ki-messages.ki-solid:before {
  content: "\ea6e";
}
.ki-microsoft.ki-solid:before {
  content: "\ea6f";
}
.ki-milk.ki-solid:before {
  content: "\ea70";
}
.ki-minus-circle.ki-solid:before {
  content: "\ea71";
}
.ki-minus-folder.ki-solid:before {
  content: "\ea72";
}
.ki-minus-squared.ki-solid:before {
  content: "\ea73";
}
.ki-minus.ki-solid:before {
  content: "\ea74";
}
.ki-moon.ki-solid:before {
  content: "\ea75";
}
.ki-more-2.ki-solid:before {
  content: "\ea76";
}
.ki-mouse-circle.ki-solid:before {
  content: "\ea77";
}
.ki-mouse-square.ki-solid:before {
  content: "\ea78";
}
.ki-mouse.ki-solid:before {
  content: "\ea79";
}
.ki-nexo.ki-solid:before {
  content: "\ea7a";
}
.ki-night-day.ki-solid:before {
  content: "\ea7b";
}
.ki-note-2.ki-solid:before {
  content: "\ea7c";
}
.ki-note.ki-solid:before {
  content: "\ea7d";
}
.ki-notepad-bookmark.ki-solid:before {
  content: "\ea7e";
}
.ki-notepad-edit.ki-solid:before {
  content: "\ea7f";
}
.ki-notepad.ki-solid:before {
  content: "\ea80";
}
.ki-notification-1.ki-solid:before {
  content: "\ea81";
}
.ki-notification-bing.ki-solid:before {
  content: "\ea82";
}
.ki-notification-circle.ki-solid:before {
  content: "\ea83";
}
.ki-notification-favorite.ki-solid:before {
  content: "\ea84";
}
.ki-notification-on.ki-solid:before {
  content: "\ea85";
}
.ki-notification-status.ki-solid:before {
  content: "\ea86";
}
.ki-notification.ki-solid:before {
  content: "\ea87";
}
.ki-ocean.ki-solid:before {
  content: "\ea88";
}
.ki-office-bag.ki-solid:before {
  content: "\ea89";
}
.ki-package.ki-solid:before {
  content: "\ea8a";
}
.ki-pad.ki-solid:before {
  content: "\ea8b";
}
.ki-pails.ki-solid:before {
  content: "\ea8c";
}
.ki-paintbucket.ki-solid:before {
  content: "\ea8d";
}
.ki-paper-clip.ki-solid:before {
  content: "\ea8e";
}
.ki-paper-plane.ki-solid:before {
  content: "\ea8f";
}
.ki-parcel-tracking.ki-solid:before {
  content: "\ea90";
}
.ki-parcel.ki-solid:before {
  content: "\ea91";
}
.ki-password-check.ki-solid:before {
  content: "\ea92";
}
.ki-paypal.ki-solid:before {
  content: "\ea93";
}
.ki-pencil.ki-solid:before {
  content: "\ea94";
}
.ki-people.ki-solid:before {
  content: "\ea95";
}
.ki-percentage.ki-solid:before {
  content: "\ea96";
}
.ki-phone.ki-solid:before {
  content: "\ea97";
}
.ki-photoshop.ki-solid:before {
  content: "\ea98";
}
.ki-picture.ki-solid:before {
  content: "\ea99";
}
.ki-pill.ki-solid:before {
  content: "\ea9a";
}
.ki-pin.ki-solid:before {
  content: "\ea9b";
}
.ki-plus-circle.ki-solid:before {
  content: "\ea9c";
}
.ki-plus-squared.ki-solid:before {
  content: "\ea9d";
}
.ki-plus.ki-solid:before {
  content: "\ea9e";
}
.ki-pointers.ki-solid:before {
  content: "\ea9f";
}
.ki-price-tag.ki-solid:before {
  content: "\eaa0";
}
.ki-printer.ki-solid:before {
  content: "\eaa1";
}
.ki-profile-circle.ki-solid:before {
  content: "\eaa2";
}
.ki-pulse.ki-solid:before {
  content: "\eaa3";
}
.ki-purchase.ki-solid:before {
  content: "\eaa4";
}
.ki-python.ki-solid:before {
  content: "\eaa5";
}
.ki-question-2.ki-solid:before {
  content: "\eaa6";
}
.ki-question.ki-solid:before {
  content: "\eaa7";
}
.ki-questionnaire-tablet.ki-solid:before {
  content: "\eaa8";
}
.ki-ranking.ki-solid:before {
  content: "\eaa9";
}
.ki-react.ki-solid:before {
  content: "\eaaa";
}
.ki-receipt-square.ki-solid:before {
  content: "\eaab";
}
.ki-rescue.ki-solid:before {
  content: "\eaac";
}
.ki-right-left.ki-solid:before {
  content: "\eaad";
}
.ki-right-square.ki-solid:before {
  content: "\eaae";
}
.ki-right.ki-solid:before {
  content: "\eaaf";
}
.ki-rocket.ki-solid:before {
  content: "\eab0";
}
.ki-route.ki-solid:before {
  content: "\eab1";
}
.ki-router.ki-solid:before {
  content: "\eab2";
}
.ki-row-horizontal.ki-solid:before {
  content: "\eab3";
}
.ki-row-vertical.ki-solid:before {
  content: "\eab4";
}
.ki-safe-home.ki-solid:before {
  content: "\eab5";
}
.ki-satellite.ki-solid:before {
  content: "\eab6";
}
.ki-save-2.ki-solid:before {
  content: "\eab7";
}
.ki-save-deposit.ki-solid:before {
  content: "\eab8";
}
.ki-scan-barcode.ki-solid:before {
  content: "\eab9";
}
.ki-screen.ki-solid:before {
  content: "\eaba";
}
.ki-scroll.ki-solid:before {
  content: "\eabb";
}
.ki-search-list.ki-solid:before {
  content: "\eabc";
}
.ki-security-user.ki-solid:before {
  content: "\eabd";
}
.ki-setting-2.ki-solid:before {
  content: "\eabe";
}
.ki-setting-3.ki-solid:before {
  content: "\eabf";
}
.ki-setting-4.ki-solid:before {
  content: "\eac0";
}
.ki-setting.ki-solid:before {
  content: "\eac1";
}
.ki-share.ki-solid:before {
  content: "\eac2";
}
.ki-shield-cross.ki-solid:before {
  content: "\eac3";
}
.ki-shield-search.ki-solid:before {
  content: "\eac4";
}
.ki-shield-slash.ki-solid:before {
  content: "\eac5";
}
.ki-shield-tick.ki-solid:before {
  content: "\eac6";
}
.ki-shield.ki-solid:before {
  content: "\eac7";
}
.ki-ship.ki-solid:before {
  content: "\eac8";
}
.ki-shop.ki-solid:before {
  content: "\eac9";
}
.ki-simcard-2.ki-solid:before {
  content: "\eaca";
}
.ki-simcard.ki-solid:before {
  content: "\eacb";
}
.ki-size.ki-solid:before {
  content: "\eacc";
}
.ki-slack.ki-solid:before {
  content: "\eacd";
}
.ki-slider-horizontal-2.ki-solid:before {
  content: "\eace";
}
.ki-slider-horizontal.ki-solid:before {
  content: "\eacf";
}
.ki-slider-vertica.ki-solid:before {
  content: "\ead0";
}
.ki-slider-vertical.ki-solid:before {
  content: "\ead1";
}
.ki-slider.ki-solid:before {
  content: "\ead2";
}
.ki-sms.ki-solid:before {
  content: "\ead3";
}
.ki-snapchat.ki-solid:before {
  content: "\ead4";
}
.ki-social-media.ki-solid:before {
  content: "\ead5";
}
.ki-soft-2.ki-solid:before {
  content: "\ead6";
}
.ki-soft-3.ki-solid:before {
  content: "\ead7";
}
.ki-soft.ki-solid:before {
  content: "\ead8";
}
.ki-some-files.ki-solid:before {
  content: "\ead9";
}
.ki-sort.ki-solid:before {
  content: "\eada";
}
.ki-speaker.ki-solid:before {
  content: "\eadb";
}
.ki-spotify.ki-solid:before {
  content: "\eadc";
}
.ki-spring-framework.ki-solid:before {
  content: "\eadd";
}
.ki-square-brackets.ki-solid:before {
  content: "\eade";
}
.ki-star.ki-solid:before {
  content: "\eadf";
}
.ki-status.ki-solid:before {
  content: "\eae0";
}
.ki-subtitle.ki-solid:before {
  content: "\eae1";
}
.ki-sun.ki-solid:before {
  content: "\eae2";
}
.ki-support.ki-solid:before {
  content: "\eae3";
}
.ki-switch.ki-solid:before {
  content: "\eae4";
}
.ki-syringe.ki-solid:before {
  content: "\eae5";
}
.ki-tab-tablet.ki-solid:before {
  content: "\eae6";
}
.ki-tablet-delete.ki-solid:before {
  content: "\eae7";
}
.ki-tablet-down.ki-solid:before {
  content: "\eae8";
}
.ki-tablet-ok.ki-solid:before {
  content: "\eae9";
}
.ki-tablet-text-down.ki-solid:before {
  content: "\eaea";
}
.ki-tablet-text-up.ki-solid:before {
  content: "\eaeb";
}
.ki-tablet-up.ki-solid:before {
  content: "\eaec";
}
.ki-tablet.ki-solid:before {
  content: "\eaed";
}
.ki-tag-cross.ki-solid:before {
  content: "\eaee";
}
.ki-tag.ki-solid:before {
  content: "\eaef";
}
.ki-teacher.ki-solid:before {
  content: "\eaf0";
}
.ki-technology-1.ki-solid:before {
  content: "\eaf1";
}
.ki-technology-2.ki-solid:before {
  content: "\eaf2";
}
.ki-technology-3.ki-solid:before {
  content: "\eaf3";
}
.ki-technology-4.ki-solid:before {
  content: "\eaf4";
}
.ki-telephone-geolocation.ki-solid:before {
  content: "\eaf5";
}
.ki-test-tubes.ki-solid:before {
  content: "\eaf6";
}
.ki-text-bold.ki-solid:before {
  content: "\eaf7";
}
.ki-text-circle.ki-solid:before {
  content: "\eaf8";
}
.ki-text-italic.ki-solid:before {
  content: "\eaf9";
}
.ki-text-number.ki-solid:before {
  content: "\eafa";
}
.ki-text-strikethrough.ki-solid:before {
  content: "\eafb";
}
.ki-text-underline.ki-solid:before {
  content: "\eafc";
}
.ki-text.ki-solid:before {
  content: "\eafd";
}
.ki-textalign-center.ki-solid:before {
  content: "\eafe";
}
.ki-textalign-justifycenter.ki-solid:before {
  content: "\eaff";
}
.ki-textalign-left.ki-solid:before {
  content: "\eb00";
}
.ki-textalign-right.ki-solid:before {
  content: "\eb01";
}
.ki-thermometer.ki-solid:before {
  content: "\eb02";
}
.ki-theta-theta.ki-solid:before {
  content: "\eb03";
}
.ki-tiktok.ki-solid:before {
  content: "\eb04";
}
.ki-time.ki-solid:before {
  content: "\eb05";
}
.ki-timer.ki-solid:before {
  content: "\eb06";
}
.ki-to-left.ki-solid:before {
  content: "\eb07";
}
.ki-to-right.ki-solid:before {
  content: "\eb08";
}
.ki-toggle-off-circle.ki-solid:before {
  content: "\eb09";
}
.ki-toggle-off.ki-solid:before {
  content: "\eb0a";
}
.ki-toggle-on-circle.ki-solid:before {
  content: "\eb0b";
}
.ki-toggle-on.ki-solid:before {
  content: "\eb0c";
}
.ki-trash-square.ki-solid:before {
  content: "\eb0d";
}
.ki-trash.ki-solid:before {
  content: "\eb0e";
}
.ki-tree.ki-solid:before {
  content: "\eb0f";
}
.ki-trello.ki-solid:before {
  content: "\eb10";
}
.ki-ts.ki-solid:before {
  content: "\eb11";
}
.ki-twitch.ki-solid:before {
  content: "\eb12";
}
.ki-twitter.ki-solid:before {
  content: "\eb13";
}
.ki-two-credit-cart.ki-solid:before {
  content: "\eb14";
}
.ki-underlining.ki-solid:before {
  content: "\eb15";
}
.ki-up-diagonal.ki-solid:before {
  content: "\eb16";
}
.ki-up-down.ki-solid:before {
  content: "\eb17";
}
.ki-up-square.ki-solid:before {
  content: "\eb18";
}
.ki-up.ki-solid:before {
  content: "\eb19";
}
.ki-update-file.ki-solid:before {
  content: "\eb1a";
}
.ki-update-folder.ki-solid:before {
  content: "\eb1b";
}
.ki-user-edit.ki-solid:before {
  content: "\eb1c";
}
.ki-user-square.ki-solid:before {
  content: "\eb1d";
}
.ki-user-tick.ki-solid:before {
  content: "\eb1e";
}
.ki-user.ki-solid:before {
  content: "\eb1f";
}
.ki-users.ki-solid:before {
  content: "\eb20";
}
.ki-verify.ki-solid:before {
  content: "\eb21";
}
.ki-vibe-vibe.ki-solid:before {
  content: "\eb22";
}
.ki-virus.ki-solid:before {
  content: "\eb23";
}
.ki-vue.ki-solid:before {
  content: "\eb24";
}
.ki-vuesax.ki-solid:before {
  content: "\eb25";
}
.ki-wallet.ki-solid:before {
  content: "\eb26";
}
.ki-wanchain-wan.ki-solid:before {
  content: "\eb27";
}
.ki-watch.ki-solid:before {
  content: "\eb28";
}
.ki-whatsapp.ki-solid:before {
  content: "\eb29";
}
.ki-wifi-home.ki-solid:before {
  content: "\eb2a";
}
.ki-wifi-square.ki-solid:before {
  content: "\eb2b";
}
.ki-wifi.ki-solid:before {
  content: "\eb2c";
}
.ki-wireframe.ki-solid:before {
  content: "\eb2d";
}
.ki-wlan.ki-solid:before {
  content: "\eb2e";
}
.ki-wrench.ki-solid:before {
  content: "\eb2f";
}
.ki-xaomi.ki-solid:before {
  content: "\eb30";
}
.ki-xd.ki-solid:before {
  content: "\eb31";
}
.ki-xmr.ki-solid:before {
  content: "\eb32";
}
.ki-yii.ki-solid:before {
  content: "\eb33";
}
.ki-youtube.ki-solid:before {
  content: "\eb34";
}
