/**
 * Admin Panel Toastr Configuration and Helper Functions
 *
 * This file configures Toastr.js for the Laravel admin panel with:
 * - RTL support for Arabic language
 * - Consistent positioning and timing
 * - Helper functions for different message types
 * - Integration with Laravel session flash data
 */

// Initialize Toastr when j<PERSON>uery and Toastr are available
function initToastr() {
    // Check if jQuery and Toastr are available
    if (typeof $ === 'undefined' || typeof toastr === 'undefined') {
        // If not available, retry after a short delay
        setTimeout(initToastr, 100);
        return;
    }

    // Global Toastr configuration
    toastr.options = {
        "closeButton": false,
        "debug": false,
        "newestOnTop": true,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "preventDuplicates": true,
        "onclick": null,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut",
        "tapToDismiss": false,
        "escapeHtml": false
    };

    // RTL support - adjust position for Arabic
    if (document.documentElement.dir === 'rtl' || document.body.classList.contains('rtl')) {
        toastr.options.positionClass = "toast-top-left";
    }

    /**
     * Admin Toastr Helper Object
     * Provides consistent methods for displaying notifications
     */
    window.AdminToastr = {

        /**
         * Display success notification
         * @param {string} message - The message to display
         * @param {string} title - Optional title (defaults to translated 'Success')
         */
        success: function(message, title = null) {
            if (typeof toastr !== 'undefined') {
                const defaultTitle = window.translations?.success || 'Success';
                toastr.success(message, title || defaultTitle);
            }
        },

        /**
         * Display error notification
         * @param {string} message - The message to display
         * @param {string} title - Optional title (defaults to translated 'Error')
         */
        error: function(message, title = null) {
            if (typeof toastr !== 'undefined') {
                const defaultTitle = window.translations?.error || 'Error';
                toastr.error(message, title || defaultTitle);
            }
        },

        /**
         * Display warning notification
         * @param {string} message - The message to display
         * @param {string} title - Optional title (defaults to translated 'Warning')
         */
        warning: function(message, title = null) {
            if (typeof toastr !== 'undefined') {
                const defaultTitle = window.translations?.warning || 'Warning';
                toastr.warning(message, title || defaultTitle);
            }
        },

        /**
         * Display info notification
         * @param {string} message - The message to display
         * @param {string} title - Optional title (defaults to translated 'Information')
         */
        info: function(message, title = null) {
            if (typeof toastr !== 'undefined') {
                const defaultTitle = window.translations?.info || 'Information';
                toastr.info(message, title || defaultTitle);
            }
        },

        /**
         * Display validation errors
         * @param {array|object} errors - Array of error messages or errors object
         * @param {string} title - Optional title (defaults to translated 'Validation Errors')
         */
        validationErrors: function(errors, title = null) {
            if (typeof toastr !== 'undefined') {
                const defaultTitle = window.translations?.validation_errors || 'Validation Errors';

                let errorMessage = '';
                if (Array.isArray(errors)) {
                    errorMessage = errors.join('<br>');
                } else if (typeof errors === 'object') {
                    const errorArray = [];
                    for (const field in errors) {
                        if (errors.hasOwnProperty(field)) {
                            if (Array.isArray(errors[field])) {
                                errorArray.push(...errors[field]);
                            } else {
                                errorArray.push(errors[field]);
                            }
                        }
                    }
                    errorMessage = errorArray.join('<br>');
                } else {
                    errorMessage = errors.toString();
                }

                toastr.error("Please correct the errors in the form below.", title || defaultTitle);
            }
        },

        /**
         * Clear all toastr notifications
         */
        clear: function() {
            if (typeof toastr !== 'undefined') {
                toastr.clear();
            }
        },

        /**
         * Remove specific toastr notification
         * @param {object} toast - The toast object to remove
         */
        remove: function(toast) {
            if (typeof toastr !== 'undefined') {
                toastr.remove(toast);
            }
        }
    };

    /**
     * Initialize Laravel Session Flash Messages with Toastr
     * This function should be called after the DOM is loaded
     */
    window.initLaravelFlashMessages = function() {
        // Check if Laravel flash data exists
        if (typeof window.laravelFlashData !== 'undefined') {
            const flashData = window.laravelFlashData;

            // Display success messages
            if (flashData.success) {
                AdminToastr.success(flashData.success);
            }

            // Display error messages
            if (flashData.error) {
                AdminToastr.error(flashData.error);
            }

            // Display warning messages
            if (flashData.warning) {
                AdminToastr.warning(flashData.warning);
            }

            // Display info messages
            if (flashData.info) {
                AdminToastr.info(flashData.info);
            }

            // Display validation errors
            if (flashData.errors && Object.keys(flashData.errors).length > 0) {
                AdminToastr.validationErrors(flashData.errors);
            }
        }
    };

    // Auto-initialize Laravel flash messages when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Small delay to ensure all scripts are loaded
        setTimeout(function() {
            window.initLaravelFlashMessages();
        }, 100);
    });
}

// Start initialization when script loads
initToastr();