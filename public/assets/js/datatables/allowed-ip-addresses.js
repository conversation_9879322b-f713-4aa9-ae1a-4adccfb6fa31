/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function() {
return /******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/@popperjs/core/lib/createPopper.js":
/*!*********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/createPopper.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPopper: function() { return /* binding */ createPopper; },\n/* harmony export */   detectOverflow: function() { return /* reexport safe */ _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]; },\n/* harmony export */   popperGenerator: function() { return /* binding */ popperGenerator; }\n/* harmony export */ });\n/* harmony import */ var _dom_utils_getCompositeRect_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dom-utils/getCompositeRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js\");\n/* harmony import */ var _dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./dom-utils/getLayoutRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js\");\n/* harmony import */ var _dom_utils_listScrollParents_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dom-utils/listScrollParents.js */ \"./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js\");\n/* harmony import */ var _dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./dom-utils/getOffsetParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _utils_orderModifiers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/orderModifiers.js */ \"./node_modules/@popperjs/core/lib/utils/orderModifiers.js\");\n/* harmony import */ var _utils_debounce_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/debounce.js */ \"./node_modules/@popperjs/core/lib/utils/debounce.js\");\n/* harmony import */ var _utils_mergeByName_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/mergeByName.js */ \"./node_modules/@popperjs/core/lib/utils/mergeByName.js\");\n/* harmony import */ var _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/detectOverflow.js */ \"./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dom-utils/instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\n\n\n\n\n\n\n\n\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nfunction popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: (0,_dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isElement)(reference) ? (0,_dom_utils_listScrollParents_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(reference) : reference.contextElement ? (0,_dom_utils_listScrollParents_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(reference.contextElement) : [],\n          popper: (0,_dom_utils_listScrollParents_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = (0,_utils_orderModifiers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_utils_mergeByName_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: (0,_dom_utils_getCompositeRect_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(reference, (0,_dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(popper), state.options.strategy === 'fixed'),\n          popper: (0,_dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: (0,_utils_debounce_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nvar createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/createPopper.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/contains.js":
/*!***************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/contains.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ contains; }\n/* harmony export */ });\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\nfunction contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isShadowRoot)(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/contains.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getBoundingClientRect; }\n/* harmony export */ });\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/math.js */ \"./node_modules/@popperjs/core/lib/utils/math.js\");\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _isLayoutViewport_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./isLayoutViewport.js */ \"./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js\");\n\n\n\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n    scaleX = element.offsetWidth > 0 ? (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_1__.round)(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_1__.round)(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !(0,_isLayoutViewport_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getClippingRect; }\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _getViewportRect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getViewportRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js\");\n/* harmony import */ var _getDocumentRect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getDocumentRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js\");\n/* harmony import */ var _listScrollParents_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./listScrollParents.js */ \"./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js\");\n/* harmony import */ var _getOffsetParent_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./getOffsetParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getDocumentElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _getComputedStyle_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./getComputedStyle.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getBoundingClientRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n/* harmony import */ var _getParentNode_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./getParentNode.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./contains.js */ \"./node_modules/@popperjs/core/lib/dom-utils/contains.js\");\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./getNodeName.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _utils_rectToClientRect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/rectToClientRect.js */ \"./node_modules/@popperjs/core/lib/utils/rectToClientRect.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../utils/math.js */ \"./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === _enums_js__WEBPACK_IMPORTED_MODULE_1__.viewport ? (0,_utils_rectToClientRect_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_getViewportRect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element, strategy)) : (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isElement)(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : (0,_utils_rectToClientRect_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_getDocumentRect_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = (0,_listScrollParents_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_getParentNode_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf((0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(element).position) >= 0;\n  var clipperElement = canEscapeClipping && (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(element) ? (0,_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(element) : element;\n\n  if (!(0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isElement)(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isElement)(clippingParent) && (0,_contains_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(clippingParent, clipperElement) && (0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nfunction getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_13__.max)(rect.top, accRect.top);\n    accRect.right = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_13__.min)(rect.right, accRect.right);\n    accRect.bottom = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_13__.min)(rect.bottom, accRect.bottom);\n    accRect.left = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_13__.max)(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getCompositeRect; }\n/* harmony export */ });\n/* harmony import */ var _getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getBoundingClientRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n/* harmony import */ var _getNodeScroll_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getNodeScroll.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js\");\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getNodeName.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./getWindowScrollBarX.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getDocumentElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _isScrollParent_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./isScrollParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/math.js */ \"./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n\n\n\n\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_0__.round)(rect.width) / element.offsetWidth || 1;\n  var scaleY = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_0__.round)(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nfunction getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent);\n  var offsetParentIsScaled = (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = (0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(offsetParent);\n  var rect = (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if ((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    (0,_isScrollParent_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(documentElement)) {\n      scroll = (0,_getNodeScroll_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(offsetParent);\n    }\n\n    if ((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent)) {\n      offsets = (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = (0,_getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getComputedStyle; }\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n\nfunction getComputedStyle(element) {\n  return (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element).getComputedStyle(element);\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getDocumentElement; }\n/* harmony export */ });\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\nfunction getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return (((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getDocumentRect; }\n/* harmony export */ });\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getDocumentElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _getComputedStyle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getComputedStyle.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n/* harmony import */ var _getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getWindowScrollBarX.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js\");\n/* harmony import */ var _getWindowScroll_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getWindowScroll.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/math.js */ \"./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nfunction getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = (0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element);\n  var winScroll = (0,_getWindowScroll_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_2__.max)(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_2__.max)(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + (0,_getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element);\n  var y = -winScroll.scrollTop;\n\n  if ((0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(body || html).direction === 'rtl') {\n    x += (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_2__.max)(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getHTMLElementScroll; }\n/* harmony export */ });\nfunction getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getLayoutRect; }\n/* harmony export */ });\n/* harmony import */ var _getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getBoundingClientRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nfunction getLayoutRect(element) {\n  var clientRect = (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js":
/*!******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getNodeName; }\n/* harmony export */ });\nfunction getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getNodeScroll; }\n/* harmony export */ });\n/* harmony import */ var _getWindowScroll_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getWindowScroll.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js\");\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _getHTMLElementScroll_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getHTMLElementScroll.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js\");\n\n\n\n\nfunction getNodeScroll(node) {\n  if (node === (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node) || !(0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(node)) {\n    return (0,_getWindowScroll_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node);\n  } else {\n    return (0,_getHTMLElementScroll_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(node);\n  }\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getOffsetParent; }\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getNodeName.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getComputedStyle.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _isTableElement_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./isTableElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/isTableElement.js\");\n/* harmony import */ var _getParentNode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getParentNode.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\");\n/* harmony import */ var _utils_userAgent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/userAgent.js */ \"./node_modules/@popperjs/core/lib/utils/userAgent.js\");\n\n\n\n\n\n\n\n\nfunction getTrueOffsetParent(element) {\n  if (!(0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || // https://github.com/popperjs/popper-core/issues/837\n  (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test((0,_utils_userAgent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])());\n  var isIE = /Trident/i.test((0,_utils_userAgent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])());\n\n  if (isIE && (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = (0,_getParentNode_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element);\n\n  if ((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isShadowRoot)(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while ((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(currentNode) && ['html', 'body'].indexOf((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(currentNode)) < 0) {\n    var css = (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nfunction getOffsetParent(element) {\n  var window = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && (0,_isTableElement_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(offsetParent) && (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && ((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(offsetParent) === 'html' || (0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(offsetParent) === 'body' && (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getParentNode; }\n/* harmony export */ });\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getNodeName.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getDocumentElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\n\n\nfunction getParentNode(element) {\n  if ((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isShadowRoot)(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    (0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(element) // fallback\n\n  );\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getScrollParent; }\n/* harmony export */ });\n/* harmony import */ var _getParentNode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getParentNode.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\");\n/* harmony import */ var _isScrollParent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isScrollParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js\");\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getNodeName.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\n\n\n\nfunction getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if ((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(node) && (0,_isScrollParent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node)) {\n    return node;\n  }\n\n  return getScrollParent((0,_getParentNode_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(node));\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getViewportRect; }\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDocumentElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getWindowScrollBarX.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js\");\n/* harmony import */ var _isLayoutViewport_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isLayoutViewport.js */ \"./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js\");\n\n\n\n\nfunction getViewportRect(element, strategy) {\n  var win = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element);\n  var html = (0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = (0,_isLayoutViewport_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + (0,_getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element),\n    y: y\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getWindow.js":
/*!****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getWindow.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getWindow; }\n/* harmony export */ });\nfunction getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getWindowScroll; }\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n\nfunction getWindowScroll(node) {\n  var win = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getWindowScrollBarX; }\n/* harmony export */ });\n/* harmony import */ var _getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getBoundingClientRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDocumentElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _getWindowScroll_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getWindowScroll.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js\");\n\n\n\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element)).left + (0,_getWindowScroll_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(element).scrollLeft;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElement: function() { return /* binding */ isElement; },\n/* harmony export */   isHTMLElement: function() { return /* binding */ isHTMLElement; },\n/* harmony export */   isShadowRoot: function() { return /* binding */ isShadowRoot; }\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n\n\nfunction isElement(node) {\n  var OwnElement = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ isLayoutViewport; }\n/* harmony export */ });\n/* harmony import */ var _utils_userAgent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/userAgent.js */ \"./node_modules/@popperjs/core/lib/utils/userAgent.js\");\n\nfunction isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test((0,_utils_userAgent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])());\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ isScrollParent; }\n/* harmony export */ });\n/* harmony import */ var _getComputedStyle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getComputedStyle.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n\nfunction isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/isTableElement.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/isTableElement.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ isTableElement; }\n/* harmony export */ });\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getNodeName.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element)) >= 0;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/isTableElement.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js":
/*!************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ listScrollParents; }\n/* harmony export */ });\n/* harmony import */ var _getScrollParent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getScrollParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js\");\n/* harmony import */ var _getParentNode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getParentNode.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\");\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _isScrollParent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isScrollParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js\");\n\n\n\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nfunction listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = (0,_getScrollParent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], (0,_isScrollParent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents((0,_getParentNode_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(target)));\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/enums.js":
/*!**************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/enums.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   afterMain: function() { return /* binding */ afterMain; },\n/* harmony export */   afterRead: function() { return /* binding */ afterRead; },\n/* harmony export */   afterWrite: function() { return /* binding */ afterWrite; },\n/* harmony export */   auto: function() { return /* binding */ auto; },\n/* harmony export */   basePlacements: function() { return /* binding */ basePlacements; },\n/* harmony export */   beforeMain: function() { return /* binding */ beforeMain; },\n/* harmony export */   beforeRead: function() { return /* binding */ beforeRead; },\n/* harmony export */   beforeWrite: function() { return /* binding */ beforeWrite; },\n/* harmony export */   bottom: function() { return /* binding */ bottom; },\n/* harmony export */   clippingParents: function() { return /* binding */ clippingParents; },\n/* harmony export */   end: function() { return /* binding */ end; },\n/* harmony export */   left: function() { return /* binding */ left; },\n/* harmony export */   main: function() { return /* binding */ main; },\n/* harmony export */   modifierPhases: function() { return /* binding */ modifierPhases; },\n/* harmony export */   placements: function() { return /* binding */ placements; },\n/* harmony export */   popper: function() { return /* binding */ popper; },\n/* harmony export */   read: function() { return /* binding */ read; },\n/* harmony export */   reference: function() { return /* binding */ reference; },\n/* harmony export */   right: function() { return /* binding */ right; },\n/* harmony export */   start: function() { return /* binding */ start; },\n/* harmony export */   top: function() { return /* binding */ top; },\n/* harmony export */   variationPlacements: function() { return /* binding */ variationPlacements; },\n/* harmony export */   viewport: function() { return /* binding */ viewport; },\n/* harmony export */   write: function() { return /* binding */ write; }\n/* harmony export */ });\nvar top = 'top';\nvar bottom = 'bottom';\nvar right = 'right';\nvar left = 'left';\nvar auto = 'auto';\nvar basePlacements = [top, bottom, right, left];\nvar start = 'start';\nvar end = 'end';\nvar clippingParents = 'clippingParents';\nvar viewport = 'viewport';\nvar popper = 'popper';\nvar reference = 'reference';\nvar variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nvar placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nvar beforeRead = 'beforeRead';\nvar read = 'read';\nvar afterRead = 'afterRead'; // pure-logic modifiers\n\nvar beforeMain = 'beforeMain';\nvar main = 'main';\nvar afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nvar beforeWrite = 'beforeWrite';\nvar write = 'write';\nvar afterWrite = 'afterWrite';\nvar modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/enums.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   afterMain: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.afterMain; },\n/* harmony export */   afterRead: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.afterRead; },\n/* harmony export */   afterWrite: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.afterWrite; },\n/* harmony export */   applyStyles: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.applyStyles; },\n/* harmony export */   arrow: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.arrow; },\n/* harmony export */   auto: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.auto; },\n/* harmony export */   basePlacements: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.basePlacements; },\n/* harmony export */   beforeMain: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.beforeMain; },\n/* harmony export */   beforeRead: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.beforeRead; },\n/* harmony export */   beforeWrite: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.beforeWrite; },\n/* harmony export */   bottom: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.bottom; },\n/* harmony export */   clippingParents: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.clippingParents; },\n/* harmony export */   computeStyles: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.computeStyles; },\n/* harmony export */   createPopper: function() { return /* reexport safe */ _popper_js__WEBPACK_IMPORTED_MODULE_4__.createPopper; },\n/* harmony export */   createPopperBase: function() { return /* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_2__.createPopper; },\n/* harmony export */   createPopperLite: function() { return /* reexport safe */ _popper_lite_js__WEBPACK_IMPORTED_MODULE_5__.createPopper; },\n/* harmony export */   detectOverflow: function() { return /* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   end: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.end; },\n/* harmony export */   eventListeners: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.eventListeners; },\n/* harmony export */   flip: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.flip; },\n/* harmony export */   hide: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.hide; },\n/* harmony export */   left: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.left; },\n/* harmony export */   main: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.main; },\n/* harmony export */   modifierPhases: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.modifierPhases; },\n/* harmony export */   offset: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.offset; },\n/* harmony export */   placements: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.placements; },\n/* harmony export */   popper: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper; },\n/* harmony export */   popperGenerator: function() { return /* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_2__.popperGenerator; },\n/* harmony export */   popperOffsets: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.popperOffsets; },\n/* harmony export */   preventOverflow: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__.preventOverflow; },\n/* harmony export */   read: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.read; },\n/* harmony export */   reference: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.reference; },\n/* harmony export */   right: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.right; },\n/* harmony export */   start: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.start; },\n/* harmony export */   top: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.top; },\n/* harmony export */   variationPlacements: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.variationPlacements; },\n/* harmony export */   viewport: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.viewport; },\n/* harmony export */   write: function() { return /* reexport safe */ _enums_js__WEBPACK_IMPORTED_MODULE_0__.write; }\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _modifiers_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modifiers/index.js */ \"./node_modules/@popperjs/core/lib/modifiers/index.js\");\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createPopper.js */ \"./node_modules/@popperjs/core/lib/createPopper.js\");\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createPopper.js */ \"./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _popper_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./popper.js */ \"./node_modules/@popperjs/core/lib/popper.js\");\n/* harmony import */ var _popper_lite_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./popper-lite.js */ \"./node_modules/@popperjs/core/lib/popper-lite.js\");\n\n // eslint-disable-next-line import/no-unused-modules\n\n // eslint-disable-next-line import/no-unused-modules\n\n // eslint-disable-next-line import/no-unused-modules\n\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/index.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/applyStyles.js":
/*!******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/applyStyles.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _dom_utils_getNodeName_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../dom-utils/getNodeName.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom-utils/instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\n // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!(0,_dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || !(0,_dom_utils_getNodeName_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!(0,_dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || !(0,_dom_utils_getNodeName_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/applyStyles.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/arrow.js":
/*!************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/arrow.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dom-utils/getLayoutRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js\");\n/* harmony import */ var _dom_utils_contains_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../dom-utils/contains.js */ \"./node_modules/@popperjs/core/lib/dom-utils/contains.js\");\n/* harmony import */ var _dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../dom-utils/getOffsetParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _utils_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/getMainAxisFromPlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js\");\n/* harmony import */ var _utils_within_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/within.js */ \"./node_modules/@popperjs/core/lib/utils/within.js\");\n/* harmony import */ var _utils_mergePaddingObject_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/mergePaddingObject.js */ \"./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js\");\n/* harmony import */ var _utils_expandToHashMap_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/expandToHashMap.js */ \"./node_modules/@popperjs/core/lib/utils/expandToHashMap.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n\n\n\n\n\n\n\n\n // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return (0,_utils_mergePaddingObject_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(typeof padding !== 'number' ? padding : (0,_utils_expandToHashMap_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(padding, _enums_js__WEBPACK_IMPORTED_MODULE_2__.basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(state.placement);\n  var axis = (0,_utils_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(basePlacement);\n  var isVertical = [_enums_js__WEBPACK_IMPORTED_MODULE_2__.left, _enums_js__WEBPACK_IMPORTED_MODULE_2__.right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = (0,_dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(arrowElement);\n  var minProp = axis === 'y' ? _enums_js__WEBPACK_IMPORTED_MODULE_2__.top : _enums_js__WEBPACK_IMPORTED_MODULE_2__.left;\n  var maxProp = axis === 'y' ? _enums_js__WEBPACK_IMPORTED_MODULE_2__.bottom : _enums_js__WEBPACK_IMPORTED_MODULE_2__.right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = (0,_dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_7__.within)(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!(0,_dom_utils_contains_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/arrow.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/computeStyles.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/computeStyles.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mapToStyles: function() { return /* binding */ mapToStyles; }\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dom-utils/getOffsetParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dom-utils/getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _dom_utils_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dom-utils/getDocumentElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _dom_utils_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dom-utils/getComputedStyle.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _utils_getVariation_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/getVariation.js */ \"./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/math.js */ \"./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n\n\n\n // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_0__.round)(x * dpr) / dpr || 0,\n    y: (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_0__.round)(y * dpr) / dpr || 0\n  };\n}\n\nfunction mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = _enums_js__WEBPACK_IMPORTED_MODULE_1__.left;\n  var sideY = _enums_js__WEBPACK_IMPORTED_MODULE_1__.top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = (0,_dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === (0,_dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(popper)) {\n      offsetParent = (0,_dom_utils_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(popper);\n\n      if ((0,_dom_utils_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.top || (placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.left || placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.right) && variation === _enums_js__WEBPACK_IMPORTED_MODULE_1__.end) {\n      sideY = _enums_js__WEBPACK_IMPORTED_MODULE_1__.bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.left || (placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.top || placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.bottom) && variation === _enums_js__WEBPACK_IMPORTED_MODULE_1__.end) {\n      sideX = _enums_js__WEBPACK_IMPORTED_MODULE_1__.right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, (0,_dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(state.placement),\n    variation: (0,_utils_getVariation_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/computeStyles.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/eventListeners.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/eventListeners.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom-utils/getWindow.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = (0,_dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/eventListeners.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/flip.js":
/*!***********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/flip.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getOppositePlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getOppositePlacement.js\");\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _utils_getOppositeVariationPlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/getOppositeVariationPlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js\");\n/* harmony import */ var _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/detectOverflow.js */ \"./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _utils_computeAutoPlacement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/computeAutoPlacement.js */ \"./node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _utils_getVariation_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/getVariation.js */ \"./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n\n\n\n\n\n\n // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if ((0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement) === _enums_js__WEBPACK_IMPORTED_MODULE_1__.auto) {\n    return [];\n  }\n\n  var oppositePlacement = (0,_utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(placement);\n  return [(0,_utils_getOppositeVariationPlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(placement), oppositePlacement, (0,_utils_getOppositeVariationPlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [(0,_utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat((0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement) === _enums_js__WEBPACK_IMPORTED_MODULE_1__.auto ? (0,_utils_computeAutoPlacement_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement);\n\n    var isStartVariation = (0,_utils_getVariation_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(placement) === _enums_js__WEBPACK_IMPORTED_MODULE_1__.start;\n    var isVertical = [_enums_js__WEBPACK_IMPORTED_MODULE_1__.top, _enums_js__WEBPACK_IMPORTED_MODULE_1__.bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = (0,_utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? _enums_js__WEBPACK_IMPORTED_MODULE_1__.right : _enums_js__WEBPACK_IMPORTED_MODULE_1__.left : isStartVariation ? _enums_js__WEBPACK_IMPORTED_MODULE_1__.bottom : _enums_js__WEBPACK_IMPORTED_MODULE_1__.top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = (0,_utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(mainVariationSide);\n    }\n\n    var altVariationSide = (0,_utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/flip.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/hide.js":
/*!***********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/hide.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/detectOverflow.js */ \"./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n\n\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [_enums_js__WEBPACK_IMPORTED_MODULE_0__.top, _enums_js__WEBPACK_IMPORTED_MODULE_0__.right, _enums_js__WEBPACK_IMPORTED_MODULE_0__.bottom, _enums_js__WEBPACK_IMPORTED_MODULE_0__.left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = (0,_utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = (0,_utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/hide.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyStyles: function() { return /* reexport safe */ _applyStyles_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   arrow: function() { return /* reexport safe */ _arrow_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   computeStyles: function() { return /* reexport safe */ _computeStyles_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   eventListeners: function() { return /* reexport safe */ _eventListeners_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   flip: function() { return /* reexport safe */ _flip_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   hide: function() { return /* reexport safe */ _hide_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; },\n/* harmony export */   offset: function() { return /* reexport safe */ _offset_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]; },\n/* harmony export */   popperOffsets: function() { return /* reexport safe */ _popperOffsets_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]; },\n/* harmony export */   preventOverflow: function() { return /* reexport safe */ _preventOverflow_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _applyStyles_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./applyStyles.js */ \"./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n/* harmony import */ var _arrow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./arrow.js */ \"./node_modules/@popperjs/core/lib/modifiers/arrow.js\");\n/* harmony import */ var _computeStyles_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./computeStyles.js */ \"./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\");\n/* harmony import */ var _eventListeners_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./eventListeners.js */ \"./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\");\n/* harmony import */ var _flip_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./flip.js */ \"./node_modules/@popperjs/core/lib/modifiers/flip.js\");\n/* harmony import */ var _hide_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hide.js */ \"./node_modules/@popperjs/core/lib/modifiers/hide.js\");\n/* harmony import */ var _offset_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./offset.js */ \"./node_modules/@popperjs/core/lib/modifiers/offset.js\");\n/* harmony import */ var _popperOffsets_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./popperOffsets.js */ \"./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\");\n/* harmony import */ var _preventOverflow_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./preventOverflow.js */ \"./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/index.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/offset.js":
/*!*************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/offset.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   distanceAndSkiddingToXY: function() { return /* binding */ distanceAndSkiddingToXY; }\n/* harmony export */ });\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n\n // eslint-disable-next-line import/no-unused-modules\n\nfunction distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement);\n  var invertDistance = [_enums_js__WEBPACK_IMPORTED_MODULE_1__.left, _enums_js__WEBPACK_IMPORTED_MODULE_1__.top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [_enums_js__WEBPACK_IMPORTED_MODULE_1__.left, _enums_js__WEBPACK_IMPORTED_MODULE_1__.right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = _enums_js__WEBPACK_IMPORTED_MODULE_1__.placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/offset.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _utils_computeOffsets_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/computeOffsets.js */ \"./node_modules/@popperjs/core/lib/utils/computeOffsets.js\");\n\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = (0,_utils_computeOffsets_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _utils_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/getMainAxisFromPlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js\");\n/* harmony import */ var _utils_getAltAxis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/getAltAxis.js */ \"./node_modules/@popperjs/core/lib/utils/getAltAxis.js\");\n/* harmony import */ var _utils_within_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/within.js */ \"./node_modules/@popperjs/core/lib/utils/within.js\");\n/* harmony import */ var _dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../dom-utils/getLayoutRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js\");\n/* harmony import */ var _dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../dom-utils/getOffsetParent.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/detectOverflow.js */ \"./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _utils_getVariation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getVariation.js */ \"./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n/* harmony import */ var _utils_getFreshSideObject_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/getFreshSideObject.js */ \"./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../utils/math.js */ \"./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = (0,_utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(state.placement);\n  var variation = (0,_utils_getVariation_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = (0,_utils_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(basePlacement);\n  var altAxis = (0,_utils_getAltAxis_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? _enums_js__WEBPACK_IMPORTED_MODULE_5__.top : _enums_js__WEBPACK_IMPORTED_MODULE_5__.left;\n    var altSide = mainAxis === 'y' ? _enums_js__WEBPACK_IMPORTED_MODULE_5__.bottom : _enums_js__WEBPACK_IMPORTED_MODULE_5__.right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === _enums_js__WEBPACK_IMPORTED_MODULE_5__.start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === _enums_js__WEBPACK_IMPORTED_MODULE_5__.start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? (0,_dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : (0,_utils_getFreshSideObject_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_8__.within)(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && (0,_dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_8__.within)(tether ? (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_10__.min)(min, tetherMin) : min, offset, tether ? (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_10__.max)(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? _enums_js__WEBPACK_IMPORTED_MODULE_5__.top : _enums_js__WEBPACK_IMPORTED_MODULE_5__.left;\n\n    var _altSide = mainAxis === 'x' ? _enums_js__WEBPACK_IMPORTED_MODULE_5__.bottom : _enums_js__WEBPACK_IMPORTED_MODULE_5__.right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [_enums_js__WEBPACK_IMPORTED_MODULE_5__.top, _enums_js__WEBPACK_IMPORTED_MODULE_5__.left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_8__.withinMaxClamp)(_tetherMin, _offset, _tetherMax) : (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_8__.within)(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n});\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/popper-lite.js":
/*!********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/popper-lite.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPopper: function() { return /* binding */ createPopper; },\n/* harmony export */   defaultModifiers: function() { return /* binding */ defaultModifiers; },\n/* harmony export */   detectOverflow: function() { return /* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; },\n/* harmony export */   popperGenerator: function() { return /* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_4__.popperGenerator; }\n/* harmony export */ });\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./createPopper.js */ \"./node_modules/@popperjs/core/lib/createPopper.js\");\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./createPopper.js */ \"./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _modifiers_eventListeners_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./modifiers/eventListeners.js */ \"./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\");\n/* harmony import */ var _modifiers_popperOffsets_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modifiers/popperOffsets.js */ \"./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\");\n/* harmony import */ var _modifiers_computeStyles_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modifiers/computeStyles.js */ \"./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\");\n/* harmony import */ var _modifiers_applyStyles_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./modifiers/applyStyles.js */ \"./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n\n\n\n\n\nvar defaultModifiers = [_modifiers_eventListeners_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], _modifiers_popperOffsets_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], _modifiers_computeStyles_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"], _modifiers_applyStyles_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]];\nvar createPopper = /*#__PURE__*/(0,_createPopper_js__WEBPACK_IMPORTED_MODULE_4__.popperGenerator)({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/popper-lite.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/popper.js":
/*!***************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/popper.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyStyles: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.applyStyles; },\n/* harmony export */   arrow: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.arrow; },\n/* harmony export */   computeStyles: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.computeStyles; },\n/* harmony export */   createPopper: function() { return /* binding */ createPopper; },\n/* harmony export */   createPopperLite: function() { return /* reexport safe */ _popper_lite_js__WEBPACK_IMPORTED_MODULE_11__.createPopper; },\n/* harmony export */   defaultModifiers: function() { return /* binding */ defaultModifiers; },\n/* harmony export */   detectOverflow: function() { return /* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]; },\n/* harmony export */   eventListeners: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.eventListeners; },\n/* harmony export */   flip: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.flip; },\n/* harmony export */   hide: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.hide; },\n/* harmony export */   offset: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.offset; },\n/* harmony export */   popperGenerator: function() { return /* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_9__.popperGenerator; },\n/* harmony export */   popperOffsets: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.popperOffsets; },\n/* harmony export */   preventOverflow: function() { return /* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.preventOverflow; }\n/* harmony export */ });\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./createPopper.js */ \"./node_modules/@popperjs/core/lib/createPopper.js\");\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./createPopper.js */ \"./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _modifiers_eventListeners_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./modifiers/eventListeners.js */ \"./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\");\n/* harmony import */ var _modifiers_popperOffsets_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modifiers/popperOffsets.js */ \"./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\");\n/* harmony import */ var _modifiers_computeStyles_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modifiers/computeStyles.js */ \"./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\");\n/* harmony import */ var _modifiers_applyStyles_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./modifiers/applyStyles.js */ \"./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n/* harmony import */ var _modifiers_offset_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./modifiers/offset.js */ \"./node_modules/@popperjs/core/lib/modifiers/offset.js\");\n/* harmony import */ var _modifiers_flip_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./modifiers/flip.js */ \"./node_modules/@popperjs/core/lib/modifiers/flip.js\");\n/* harmony import */ var _modifiers_preventOverflow_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./modifiers/preventOverflow.js */ \"./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js\");\n/* harmony import */ var _modifiers_arrow_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./modifiers/arrow.js */ \"./node_modules/@popperjs/core/lib/modifiers/arrow.js\");\n/* harmony import */ var _modifiers_hide_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./modifiers/hide.js */ \"./node_modules/@popperjs/core/lib/modifiers/hide.js\");\n/* harmony import */ var _popper_lite_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./popper-lite.js */ \"./node_modules/@popperjs/core/lib/popper-lite.js\");\n/* harmony import */ var _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./modifiers/index.js */ \"./node_modules/@popperjs/core/lib/modifiers/index.js\");\n\n\n\n\n\n\n\n\n\n\nvar defaultModifiers = [_modifiers_eventListeners_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], _modifiers_popperOffsets_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], _modifiers_computeStyles_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"], _modifiers_applyStyles_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], _modifiers_offset_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], _modifiers_flip_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], _modifiers_preventOverflow_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"], _modifiers_arrow_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], _modifiers_hide_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]];\nvar createPopper = /*#__PURE__*/(0,_createPopper_js__WEBPACK_IMPORTED_MODULE_9__.popperGenerator)({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\n // eslint-disable-next-line import/no-unused-modules\n\n // eslint-disable-next-line import/no-unused-modules\n\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/popper.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ computeAutoPlacement; }\n/* harmony export */ });\n/* harmony import */ var _getVariation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getVariation.js */ \"./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _detectOverflow_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./detectOverflow.js */ \"./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _getBasePlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getBasePlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n\n\n\n\nfunction computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.placements : _options$allowedAutoP;\n  var variation = (0,_getVariation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(placement);\n  var placements = variation ? flipVariations ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.variationPlacements : _enums_js__WEBPACK_IMPORTED_MODULE_0__.variationPlacements.filter(function (placement) {\n    return (0,_getVariation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(placement) === variation;\n  }) : _enums_js__WEBPACK_IMPORTED_MODULE_0__.basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = (0,_detectOverflow_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[(0,_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/computeOffsets.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/computeOffsets.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ computeOffsets; }\n/* harmony export */ });\n/* harmony import */ var _getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getBasePlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _getVariation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getVariation.js */ \"./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n/* harmony import */ var _getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getMainAxisFromPlacement.js */ \"./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n\n\n\n\nfunction computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? (0,_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement) : null;\n  var variation = placement ? (0,_getVariation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case _enums_js__WEBPACK_IMPORTED_MODULE_2__.top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case _enums_js__WEBPACK_IMPORTED_MODULE_2__.bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case _enums_js__WEBPACK_IMPORTED_MODULE_2__.right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case _enums_js__WEBPACK_IMPORTED_MODULE_2__.left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? (0,_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case _enums_js__WEBPACK_IMPORTED_MODULE_2__.start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case _enums_js__WEBPACK_IMPORTED_MODULE_2__.end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/computeOffsets.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/debounce.js":
/*!***********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/debounce.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ debounce; }\n/* harmony export */ });\nfunction debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/debounce.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/detectOverflow.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/detectOverflow.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ detectOverflow; }\n/* harmony export */ });\n/* harmony import */ var _dom_utils_getClippingRect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dom-utils/getClippingRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js\");\n/* harmony import */ var _dom_utils_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dom-utils/getDocumentElement.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _dom_utils_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../dom-utils/getBoundingClientRect.js */ \"./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n/* harmony import */ var _computeOffsets_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./computeOffsets.js */ \"./node_modules/@popperjs/core/lib/utils/computeOffsets.js\");\n/* harmony import */ var _rectToClientRect_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./rectToClientRect.js */ \"./node_modules/@popperjs/core/lib/utils/rectToClientRect.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dom-utils/instanceOf.js */ \"./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _mergePaddingObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mergePaddingObject.js */ \"./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js\");\n/* harmony import */ var _expandToHashMap_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./expandToHashMap.js */ \"./node_modules/@popperjs/core/lib/utils/expandToHashMap.js\");\n\n\n\n\n\n\n\n\n // eslint-disable-next-line import/no-unused-modules\n\nfunction detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = (0,_mergePaddingObject_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(typeof padding !== 'number' ? padding : (0,_expandToHashMap_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(padding, _enums_js__WEBPACK_IMPORTED_MODULE_0__.basePlacements));\n  var altContext = elementContext === _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.reference : _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = (0,_dom_utils_getClippingRect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,_dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isElement)(element) ? element : element.contextElement || (0,_dom_utils_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = (0,_dom_utils_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(state.elements.reference);\n  var popperOffsets = (0,_computeOffsets_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = (0,_rectToClientRect_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [_enums_js__WEBPACK_IMPORTED_MODULE_0__.right, _enums_js__WEBPACK_IMPORTED_MODULE_0__.bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [_enums_js__WEBPACK_IMPORTED_MODULE_0__.top, _enums_js__WEBPACK_IMPORTED_MODULE_0__.bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/detectOverflow.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/expandToHashMap.js":
/*!******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/expandToHashMap.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ expandToHashMap; }\n/* harmony export */ });\nfunction expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/expandToHashMap.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/getAltAxis.js":
/*!*************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getAltAxis.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getAltAxis; }\n/* harmony export */ });\nfunction getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/getAltAxis.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/getBasePlacement.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getBasePlacement.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getBasePlacement; }\n/* harmony export */ });\n\nfunction getBasePlacement(placement) {\n  return placement.split('-')[0];\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getFreshSideObject; }\n/* harmony export */ });\nfunction getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getMainAxisFromPlacement; }\n/* harmony export */ });\nfunction getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/getOppositePlacement.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getOppositePlacement.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getOppositePlacement; }\n/* harmony export */ });\nvar hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/getOppositePlacement.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getOppositeVariationPlacement; }\n/* harmony export */ });\nvar hash = {\n  start: 'end',\n  end: 'start'\n};\nfunction getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/getVariation.js":
/*!***************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getVariation.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getVariation; }\n/* harmony export */ });\nfunction getVariation(placement) {\n  return placement.split('-')[1];\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/getVariation.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/math.js":
/*!*******************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/math.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   max: function() { return /* binding */ max; },\n/* harmony export */   min: function() { return /* binding */ min; },\n/* harmony export */   round: function() { return /* binding */ round; }\n/* harmony export */ });\nvar max = Math.max;\nvar min = Math.min;\nvar round = Math.round;\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/math.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/mergeByName.js":
/*!**************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/mergeByName.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ mergeByName; }\n/* harmony export */ });\nfunction mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/mergeByName.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ mergePaddingObject; }\n/* harmony export */ });\n/* harmony import */ var _getFreshSideObject_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getFreshSideObject.js */ \"./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js\");\n\nfunction mergePaddingObject(paddingObject) {\n  return Object.assign({}, (0,_getFreshSideObject_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), paddingObject);\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/orderModifiers.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/orderModifiers.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ orderModifiers; }\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums.js */ \"./node_modules/@popperjs/core/lib/enums.js\");\n // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nfunction orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return _enums_js__WEBPACK_IMPORTED_MODULE_0__.modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/orderModifiers.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/rectToClientRect.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/rectToClientRect.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ rectToClientRect; }\n/* harmony export */ });\nfunction rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/rectToClientRect.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/userAgent.js":
/*!************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/userAgent.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getUAString; }\n/* harmony export */ });\nfunction getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/userAgent.js?");

/***/ }),

/***/ "./node_modules/@popperjs/core/lib/utils/within.js":
/*!*********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/within.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   within: function() { return /* binding */ within; },\n/* harmony export */   withinMaxClamp: function() { return /* binding */ withinMaxClamp; }\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"./node_modules/@popperjs/core/lib/utils/math.js\");\n\nfunction within(min, value, max) {\n  return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.max)(min, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.min)(value, max));\n}\nfunction withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}\n\n//# sourceURL=webpack://metronic-tailwind-html/./node_modules/@popperjs/core/lib/utils/within.js?");

/***/ }),

/***/ "./src/app/datatables/allowed-ip-addresses.ts":
/*!****************************************************!*\
  !*** ./src/app/datatables/allowed-ip-addresses.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar datatable_1 = __webpack_require__(/*! ../../core/components/datatable */ \"./src/core/components/datatable/index.ts\");\nvar apiUrl = /^(localhost|127.0.0.1)$/.test(window.location.hostname)\n    ? 'http://127.0.0.1:8001/metronic-tailwind-html/demo1/account/security/allowed-ip-addresses/_data.html'\n    : 'https://keenthemes.com/metronic/tailwind/demo1/account/security/allowed-ip-addresses/_data.html';\nvar element = document.querySelector('#ip_addresses_table');\n// Example usage of the plugin with server-side API, pagination, and sorting\nvar dataTableOptions = {\n    // Initialization options for the DataTable\n    apiEndpoint: apiUrl,\n    // mapResponse: (responseData): KTDatatableResponseDataInterface => {\n    //     // Custom transformation logic for custom API\n    //     return {\n    //         data: responseData.data, // Assuming the response has an 'items' array\n    //         totalCount: responseData.totalCount // Assuming the response has a 'total' field\n    //     };\n    // },\n    // mapRequest: (query: URLSearchParams): URLSearchParams => {\n    //     // Custom query mapping logic\n    //     query.set('test', 'test');\n    //     return query;\n    // },\n    // pageSizes: [5, 10, 20, 30, 50],\n    pageSize: 10,\n    // stateSave: false,\n    stateNamespace: 'allowed-ip-addresses',\n    columns: {\n        select: {\n            render: function (item, data, context) {\n                var checkbox = document.createElement('input');\n                checkbox.className = 'checkbox checkbox-sm';\n                checkbox.type = 'checkbox';\n                checkbox.value = data.id.toString();\n                checkbox.setAttribute('data-datatable-row-check', 'true');\n                return checkbox.outerHTML.trim();\n            },\n        },\n        status: {\n            title: 'Status',\n            render: function (item) {\n                return \"<span class=\\\"badge badge-dot size-2 \".concat(item, \"\\\"></span>\");\n            },\n            createdCell: function (cell) {\n                cell.classList.add('text-center');\n            },\n        },\n        ipAddress: {\n            title: 'IP Address',\n        },\n        lastSession: {\n            title: 'Last Session',\n        },\n        label: {\n            title: 'Label',\n        },\n        method: {\n            title: 'Method',\n        },\n        edit: {\n            render: function (item, data) {\n                var button = document.createElement('button');\n                button.className = 'btn btn-sm btn-icon btn-clear btn-light';\n                button.innerHTML = '<i class=\"ki-outline ki-notepad-edit\"></i>';\n                return button.outerHTML.trim();\n            },\n            createdCell: function (cell, cellData, rowData) {\n                cell.querySelector('.btn').addEventListener('click', function () {\n                    alert(\"Clicked on edit button for row \".concat(rowData.label));\n                });\n            },\n        },\n        delete: {\n            render: function (item, data) {\n                var button = document.createElement('button');\n                button.className = 'btn btn-sm btn-icon btn-clear btn-light';\n                button.innerHTML = '<i class=\"ki-outline ki-trash\"></i>';\n                return button.outerHTML.trim();\n            },\n            createdCell: function (cell, cellData, rowData) {\n                cell.querySelector('.btn').addEventListener('click', function () {\n                    alert(\"Clicked on delete button for row \".concat(rowData.label));\n                });\n            },\n        },\n    },\n};\nvar dataTable = new datatable_1.KTDataTable(element, dataTableOptions);\ndocument.querySelector('#select_ip_btn').addEventListener('click', function () {\n    var selected = dataTable.getChecked();\n    if (selected.length) {\n        alert(\"Selected rows: \".concat(selected));\n    }\n    else {\n        alert('No rows selected');\n    }\n});\n// Cache elements with the data-datatable-search attribute\nvar searchElements = document.querySelectorAll('[data-datatable-search]');\nsearchElements.forEach(function (element) {\n    // Get the ID of the datatable to be searched\n    var tableId = element.getAttribute('data-datatable-search');\n    // Find the corresponding datatable element\n    var datatable = document.querySelector(tableId);\n    if (datatable) {\n        // Retrieve the datatable instance once\n        var dataTableInstance_1 = datatable.instance;\n        // Add the event listener for the keyup event\n        element.addEventListener('keyup', function () {\n            dataTableInstance_1.search(element.value);\n        });\n    }\n});\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/app/datatables/allowed-ip-addresses.ts?");

/***/ }),

/***/ "./src/core/components/accordion/accordion.ts":
/*!****************************************************!*\
  !*** ./src/core/components/accordion/accordion.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTAccordion = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTAccordion = /** @class */ (function (_super) {\n    __extends(KTAccordion, _super);\n    function KTAccordion(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'accordion';\n        _this._defaultConfig = {\n            hiddenClass: 'hidden',\n            activeClass: 'active',\n            expandAll: false\n        };\n        _this._config = _this._defaultConfig;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._handlers();\n        return _this;\n    }\n    KTAccordion.prototype._handlers = function () {\n        var _this = this;\n        event_handler_1.default.on(this._element, '[data-accordion-toggle]', 'click', function (event, target) {\n            event.preventDefault();\n            var accordionElement = target.closest('[data-accordion-item]');\n            if (accordionElement)\n                _this._toggle(accordionElement);\n        });\n    };\n    KTAccordion.prototype._toggle = function (accordionElement) {\n        var payload = { cancel: false };\n        this._fireEvent('toggle', payload);\n        this._dispatchEvent('toggle', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (accordionElement.classList.contains('active')) {\n            this._hide(accordionElement);\n        }\n        else {\n            this._show(accordionElement);\n        }\n    };\n    KTAccordion.prototype._show = function (accordionElement) {\n        var _this = this;\n        if (accordionElement.hasAttribute('animating') || accordionElement.classList.contains(this._getOption('activeClass')))\n            return;\n        var toggleElement = dom_1.default.child(accordionElement, '[data-accordion-toggle]');\n        if (!toggleElement)\n            return;\n        var contentElement = dom_1.default.getElement(toggleElement.getAttribute('data-accordion-toggle'));\n        if (!contentElement)\n            return;\n        var payload = { cancel: false };\n        this._fireEvent('show', payload);\n        this._dispatchEvent('show', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (this._getOption('expandAll') === false) {\n            this._hideSiblings(accordionElement);\n        }\n        accordionElement.setAttribute('aria-expanded', 'true');\n        accordionElement.classList.add(this._getOption('activeClass'));\n        accordionElement.setAttribute('animating', 'true');\n        contentElement.classList.remove(this._getOption('hiddenClass'));\n        contentElement.style.height = \"0px\";\n        dom_1.default.reflow(contentElement);\n        contentElement.style.height = \"\".concat(contentElement.scrollHeight, \"px\");\n        dom_1.default.transitionEnd(contentElement, function () {\n            accordionElement.removeAttribute('animating');\n            contentElement.style.height = '';\n            _this._fireEvent('shown');\n            _this._dispatchEvent('shown');\n        });\n    };\n    KTAccordion.prototype._hide = function (accordionElement) {\n        var _this = this;\n        if (accordionElement.hasAttribute('animating') || !accordionElement.classList.contains(this._getOption('activeClass')))\n            return;\n        var toggleElement = dom_1.default.child(accordionElement, '[data-accordion-toggle]');\n        if (!toggleElement)\n            return;\n        var contentElement = dom_1.default.getElement(toggleElement.getAttribute('data-accordion-toggle'));\n        if (!contentElement)\n            return;\n        var payload = { cancel: false };\n        this._fireEvent('hide', payload);\n        this._dispatchEvent('hide', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        accordionElement.setAttribute('aria-expanded', 'false');\n        contentElement.style.height = \"\".concat(contentElement.scrollHeight, \"px\");\n        dom_1.default.reflow(contentElement);\n        contentElement.style.height = '0px';\n        accordionElement.setAttribute('animating', 'true');\n        dom_1.default.transitionEnd(contentElement, function () {\n            accordionElement.removeAttribute('animating');\n            accordionElement.classList.remove(_this._getOption('activeClass'));\n            contentElement.classList.add(_this._getOption('hiddenClass'));\n            _this._fireEvent('hidden');\n            _this._dispatchEvent('hidden');\n        });\n    };\n    KTAccordion.prototype._hideSiblings = function (accordionElement) {\n        var _this = this;\n        var siblings = dom_1.default.siblings(accordionElement);\n        siblings === null || siblings === void 0 ? void 0 : siblings.forEach(function (sibling) {\n            _this._hide(sibling);\n        });\n    };\n    KTAccordion.prototype.show = function (accordionElement) {\n        this._show(accordionElement);\n    };\n    KTAccordion.prototype.hide = function (accordionElement) {\n        this._hide(accordionElement);\n    };\n    KTAccordion.prototype.toggle = function (accordionElement) {\n        this._toggle(accordionElement);\n    };\n    KTAccordion.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'accordion')) {\n            return data_1.default.get(element, 'accordion');\n        }\n        if (element.getAttribute('data-accordion') === \"true\") {\n            return new KTAccordion(element);\n        }\n        return null;\n    };\n    KTAccordion.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTAccordion(element, config);\n    };\n    KTAccordion.createInstances = function () {\n        var elements = document.querySelectorAll('[data-accordion=\"true\"]');\n        elements.forEach(function (element) {\n            new KTAccordion(element);\n        });\n    };\n    KTAccordion.init = function () {\n        KTAccordion.createInstances();\n    };\n    return KTAccordion;\n}(component_1.default));\nexports.KTAccordion = KTAccordion;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/accordion/accordion.ts?");

/***/ }),

/***/ "./src/core/components/accordion/index.ts":
/*!************************************************!*\
  !*** ./src/core/components/accordion/index.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTAccordion = void 0;\nvar accordion_1 = __webpack_require__(/*! ./accordion */ \"./src/core/components/accordion/accordion.ts\");\nObject.defineProperty(exports, \"KTAccordion\", ({ enumerable: true, get: function () { return accordion_1.KTAccordion; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/accordion/index.ts?");

/***/ }),

/***/ "./src/core/components/collapse/collapse.ts":
/*!**************************************************!*\
  !*** ./src/core/components/collapse/collapse.ts ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTCollapse = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTCollapse = /** @class */ (function (_super) {\n    __extends(KTCollapse, _super);\n    function KTCollapse(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'collapse';\n        _this._defaultConfig = {\n            hiddenClass: 'hidden',\n            activeClass: 'active',\n            target: ''\n        };\n        _this._config = _this._defaultConfig;\n        _this._isAnimating = false;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._targetElement = _this._getTargetElement();\n        if (!_this._targetElement) {\n            return _this;\n        }\n        _this._handlers();\n        return _this;\n    }\n    KTCollapse.prototype._getTargetElement = function () {\n        return (dom_1.default.getElement(this._element.getAttribute('data-collapse')) ||\n            dom_1.default.getElement(this._getOption('target')));\n    };\n    KTCollapse.prototype._isOpen = function () {\n        return this._targetElement.classList.contains(this._getOption('activeClass'));\n    };\n    KTCollapse.prototype._handlers = function () {\n        var _this = this;\n        this._element.addEventListener('click', function (event) {\n            event.preventDefault();\n            _this._toggle();\n        });\n    };\n    KTCollapse.prototype._expand = function () {\n        var _this = this;\n        if (this._isAnimating || this._isOpen()) {\n            return;\n        }\n        var payload = { cancel: false };\n        this._fireEvent('expand', payload);\n        this._dispatchEvent('expand', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (this._element) {\n            this._element.setAttribute('aria-expanded', 'true');\n            this._element.classList.add(this._getOption('activeClass'));\n        }\n        this._targetElement.classList.remove(this._getOption('hiddenClass'));\n        this._targetElement.classList.add(this._getOption('activeClass'));\n        this._targetElement.style.height = '0px';\n        this._targetElement.style.overflow = 'hidden';\n        dom_1.default.reflow(this._targetElement);\n        this._targetElement.style.height = \"\".concat(this._targetElement.scrollHeight, \"px\");\n        this._isAnimating = true;\n        dom_1.default.transitionEnd(this._targetElement, function () {\n            _this._isAnimating = false;\n            _this._targetElement.style.height = '';\n            _this._targetElement.style.overflow = '';\n            _this._fireEvent('expanded');\n            _this._dispatchEvent('expanded');\n        });\n    };\n    KTCollapse.prototype._collapse = function () {\n        var _this = this;\n        if (this._isAnimating || !this._isOpen()) {\n            return;\n        }\n        var payload = { cancel: false };\n        this._fireEvent('collapse', payload);\n        this._dispatchEvent('collapse', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (!this._element)\n            return;\n        this._element.setAttribute('aria-expanded', 'false');\n        this._element.classList.remove(this._getOption('activeClass'));\n        this._targetElement.classList.remove(this._getOption('activeClass'));\n        this._targetElement.style.height = \"\".concat(this._targetElement.scrollHeight, \"px\");\n        dom_1.default.reflow(this._targetElement);\n        this._targetElement.style.height = \"0px\";\n        this._targetElement.style.overflow = 'hidden';\n        this._isAnimating = true;\n        dom_1.default.transitionEnd(this._targetElement, function () {\n            _this._isAnimating = false;\n            _this._targetElement.classList.add(_this._getOption('hiddenClass'));\n            _this._targetElement.style.overflow = '';\n            _this._fireEvent('collapsed');\n            _this._dispatchEvent('collapsed');\n        });\n    };\n    KTCollapse.prototype._toggle = function () {\n        var payload = { cancel: false };\n        this._fireEvent('toggle', payload);\n        this._dispatchEvent('toggle', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (this._isOpen()) {\n            this._collapse();\n        }\n        else {\n            this._expand();\n        }\n    };\n    KTCollapse.prototype.expand = function () {\n        return this._expand();\n    };\n    KTCollapse.prototype.collapse = function () {\n        return this._collapse();\n    };\n    KTCollapse.prototype.isOpen = function () {\n        return this._isOpen();\n    };\n    KTCollapse.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'collapse')) {\n            return data_1.default.get(element, 'collapse');\n        }\n        if (element.getAttribute('data-collapse') !== \"false\") {\n            return new KTCollapse(element);\n        }\n        return null;\n    };\n    KTCollapse.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTCollapse(element, config);\n    };\n    KTCollapse.createInstances = function () {\n        var elements = document.querySelectorAll('[data-collapse]:not([data-collapse=\"false\"])');\n        elements.forEach(function (element) {\n            new KTCollapse(element);\n        });\n    };\n    KTCollapse.init = function () {\n        KTCollapse.createInstances();\n    };\n    return KTCollapse;\n}(component_1.default));\nexports.KTCollapse = KTCollapse;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/collapse/collapse.ts?");

/***/ }),

/***/ "./src/core/components/collapse/index.ts":
/*!***********************************************!*\
  !*** ./src/core/components/collapse/index.ts ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTCollapse = void 0;\nvar collapse_1 = __webpack_require__(/*! ./collapse */ \"./src/core/components/collapse/collapse.ts\");\nObject.defineProperty(exports, \"KTCollapse\", ({ enumerable: true, get: function () { return collapse_1.KTCollapse; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/collapse/index.ts?");

/***/ }),

/***/ "./src/core/components/component.ts":
/*!******************************************!*\
  !*** ./src/core/components/component.ts ***!
  \******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable guard-for-in */\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar data_1 = __webpack_require__(/*! ../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar utils_1 = __webpack_require__(/*! ../helpers/utils */ \"./src/core/helpers/utils.ts\");\nvar config_1 = __webpack_require__(/*! ./config */ \"./src/core/components/config.ts\");\nvar KTComponent = /** @class */ (function () {\n    function KTComponent() {\n        this._uid = null;\n        this._element = null;\n    }\n    KTComponent.prototype._init = function (element) {\n        element = dom_1.default.getElement(element);\n        if (!element) {\n            return;\n        }\n        this._element = element;\n        this._events = new Map();\n        this._uid = utils_1.default.geUID(this._name);\n        data_1.default.set(this._element, this._name, this);\n    };\n    KTComponent.prototype._fireEvent = function (eventType, payload) {\n        var _a;\n        if (payload === void 0) { payload = null; }\n        (_a = this._events.get(eventType)) === null || _a === void 0 ? void 0 : _a.forEach(function (callable) {\n            callable(payload);\n        });\n    };\n    KTComponent.prototype._dispatchEvent = function (eventType, payload) {\n        if (payload === void 0) { payload = null; }\n        var event = new CustomEvent(eventType, {\n            detail: { payload: payload },\n            bubbles: true,\n            cancelable: true,\n            composed: false,\n        });\n        if (!this._element)\n            return;\n        this._element.dispatchEvent(event);\n    };\n    KTComponent.prototype._getOption = function (name) {\n        var value = this._config[name];\n        if (value && (typeof value) === 'string') {\n            return this._getResponsiveOption(value);\n        }\n        else {\n            return value;\n        }\n    };\n    KTComponent.prototype._getResponsiveOption = function (value) {\n        var result = null;\n        var width = dom_1.default.getViewPort().width;\n        var parts = String(value).split('|');\n        if (parts.length > 1) {\n            for (var i = parts.length - 1; i < parts.length; i--) {\n                var part = parts[i];\n                if (part.includes(':')) {\n                    var _a = part.split(':'), breakpointKey = _a[0], breakpointValue = _a[1];\n                    var breakpoint = utils_1.default.getBreakpoint(breakpointKey);\n                    if (breakpoint <= width) {\n                        result = breakpointValue;\n                        break;\n                    }\n                }\n                else {\n                    result = part;\n                    break;\n                }\n            }\n        }\n        else {\n            result = value;\n        }\n        result = utils_1.default.parseDataAttribute(result);\n        return result;\n    };\n    KTComponent.prototype._getGlobalConfig = function () {\n        if (window.KTGlobalComponentsConfig && window.KTGlobalComponentsConfig[this._name]) {\n            return window.KTGlobalComponentsConfig[this._name];\n        }\n        else if (config_1.default && config_1.default[this._name]) {\n            return config_1.default[this._name];\n        }\n        else {\n            return {};\n        }\n    };\n    KTComponent.prototype._buildConfig = function (config) {\n        if (config === void 0) { config = {}; }\n        if (!this._element)\n            return;\n        this._config = __assign(__assign(__assign(__assign({}, this._defaultConfig), this._getGlobalConfig()), dom_1.default.getDataAttributes(this._element, this._name)), config);\n    };\n    KTComponent.prototype.dispose = function () {\n        if (!this._element)\n            return;\n        data_1.default.remove(this._element, this._name);\n    };\n    KTComponent.prototype.on = function (eventType, callback) {\n        var eventId = utils_1.default.geUID();\n        if (!this._events.get(eventType)) {\n            this._events.set(eventType, new Map());\n        }\n        this._events.get(eventType).set(eventId, callback);\n        return eventId;\n    };\n    KTComponent.prototype.off = function (eventType, eventId) {\n        var _a;\n        (_a = this._events.get(eventType)) === null || _a === void 0 ? void 0 : _a.delete(eventId);\n    };\n    KTComponent.prototype.getOption = function (name) {\n        return this._getOption(name);\n    };\n    KTComponent.prototype.getElement = function () {\n        if (!this._element)\n            return null;\n        return this._element;\n    };\n    return KTComponent;\n}());\nexports[\"default\"] = KTComponent;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/component.ts?");

/***/ }),

/***/ "./src/core/components/config.ts":
/*!***************************************!*\
  !*** ./src/core/components/config.ts ***!
  \***************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/* eslint-disable max-len */\nvar KTGlobalComponentsConfig = {\n    modal: {\n        backdropClass: 'transition-all duration-300',\n    },\n    drawer: {\n        backdropClass: 'transition-all duration-300',\n        hiddenClass: 'hidden'\n    },\n    collapse: {\n        hiddenClass: 'hidden',\n    },\n    dismiss: {\n        hiddenClass: 'hidden',\n    },\n    tabs: {\n        hiddenClass: 'hidden',\n    },\n    accordion: {\n        hiddenClass: 'hidden',\n    }\n};\nexports[\"default\"] = KTGlobalComponentsConfig;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/config.ts?");

/***/ }),

/***/ "./src/core/components/constants.ts":
/*!******************************************!*\
  !*** ./src/core/components/constants.ts ***!
  \******************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KT_ACCESSIBILITY_KEYS = void 0;\nexports.KT_ACCESSIBILITY_KEYS = [\n    'ArrowUp',\n    'ArrowLeft',\n    'ArrowDown',\n    'ArrowRight',\n    'Home',\n    'End',\n    'Escape',\n    'Enter',\n    'Tab',\n];\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/constants.ts?");

/***/ }),

/***/ "./src/core/components/datatable/datatable.ts":
/*!****************************************************!*\
  !*** ./src/core/components/datatable/datatable.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDataTable = void 0;\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar utils_1 = __webpack_require__(/*! ../../helpers/utils */ \"./src/core/helpers/utils.ts\");\nvar index_1 = __webpack_require__(/*! ../../index */ \"./src/core/index.ts\");\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\n/**\n * Custom DataTable plugin class with server-side API, pagination, and sorting\n * @classdesc A custom KTComponent class that integrates server-side API, pagination, and sorting functionality into a table.\n * It supports fetching data from a server-side API, pagination, and sorting of the fetched data.\n * @class\n * @extends {KTComponent}\n * @param {HTMLElement} element The table element\n * @param {KTDataTableConfigInterface} [config] Additional configuration options\n */\nvar KTDataTable = /** @class */ (function (_super) {\n    __extends(KTDataTable, _super);\n    function KTDataTable(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'datatable';\n        _this._checkboxListener = function (event) {\n            _this._checkboxToggle(event); // Toggle row checkbox state\n        };\n        // private _searchListener: (value: string) => void;\n        _this._data = [];\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._defaultConfig = _this._initDefaultConfig(config);\n        _this._init(element);\n        _this._buildConfig();\n        // Store the instance directly on the element\n        element.instance = _this;\n        _this._initElements();\n        if (_this._config.stateSave === false) {\n            _this._deleteState();\n        }\n        if (_this._config.stateSave) {\n            _this._loadState();\n        }\n        _this._initTableHeader();\n        _this._updateData();\n        _this._fireEvent('init');\n        _this._dispatchEvent('init');\n        return _this;\n    }\n    /**\n     * Initialize default configuration for the datatable\n     * @param config User-provided configuration options\n     * @returns Default configuration merged with user-provided options\n     */\n    KTDataTable.prototype._initDefaultConfig = function (config) {\n        return __assign({ \n            /**\n             * HTTP method for server-side API call\n             */\n            requestMethod: 'GET', \n            /**\n             * Custom HTTP headers for the API request\n             */\n            requestHeaders: {\n                'Content-Type': 'application/x-www-form-urlencoded',\n            }, \n            /**\n             * Pagination info template\n             */\n            info: '{start}-{end} of {total}', \n            /**\n             * Info text when there is no data\n             */\n            infoEmpty: 'No records found', \n            /**\n             * Available page sizes\n             */\n            pageSizes: [5, 10, 20, 30, 50], \n            /**\n             * Default page size\n             */\n            pageSize: 10, \n            /**\n             * Enable or disable pagination more button\n             */\n            pageMore: true, \n            /**\n             * Maximum number of pages before enabling pagination more button\n             */\n            pageMoreLimit: 3, \n            /**\n             * Pagination button templates\n             */\n            pagination: {\n                number: {\n                    /**\n                     * CSS classes to be added to the pagination button\n                     */\n                    class: 'btn',\n                    /**\n                     * Text to be displayed in the pagination button\n                     */\n                    text: '{page}',\n                },\n                previous: {\n                    /**\n                     * CSS classes to be added to the previous pagination button\n                     */\n                    class: 'btn',\n                    /**\n                     * Text to be displayed in the previous pagination button\n                     */\n                    text: '<i class=\"ki-outline ki-black-left rtl:transform rtl:rotate-180\"></i>',\n                },\n                next: {\n                    /**\n                     * CSS classes to be added to the next pagination button\n                     */\n                    class: 'btn',\n                    /**\n                     * Text to be displayed in the next pagination button\n                     */\n                    text: '<i class=\"ki-outline ki-black-right rtl:transform rtl:rotate-180\"></i>',\n                },\n                more: {\n                    /**\n                     * CSS classes to be added to the pagination more button\n                     */\n                    class: 'btn',\n                    /**\n                     * Text to be displayed in the pagination more button\n                     */\n                    text: '...',\n                }\n            }, \n            /**\n             * Sorting options\n             */\n            sort: {\n                /**\n                 * CSS classes to be added to the sortable headers\n                 */\n                classes: {\n                    base: 'sort',\n                    asc: 'asc',\n                    desc: 'desc',\n                },\n                /**\n                 * Local sorting callback function\n                 * Sorts the data array based on the sort field and order\n                 * @param data Data array to be sorted\n                 * @param sortField Property name of the data object to be sorted by\n                 * @param sortOrder Sorting order (ascending or descending)\n                 * @returns Sorted data array\n                 */\n                callback: function (data, sortField, sortOrder) {\n                    /**\n                     * Compares two values by converting them to strings and removing any HTML tags or white spaces\n                     * @param a First value to be compared\n                     * @param b Second value to be compared\n                     * @returns 1 if a > b, -1 if a < b, 0 if a === b\n                     */\n                    var compareValues = function (a, b) {\n                        var aText = String(a).replace(/<[^>]*>|&nbsp;/g, '');\n                        var bText = String(b).replace(/<[^>]*>|&nbsp;/g, '');\n                        return aText > bText ? (sortOrder === 'asc' ? 1 : -1) : (aText < bText ? (sortOrder === 'asc' ? -1 : 1) : 0);\n                    };\n                    return data.sort(function (a, b) {\n                        var aValue = a[sortField];\n                        var bValue = b[sortField];\n                        return compareValues(aValue, bValue);\n                    });\n                },\n            }, search: {\n                /**\n                 * Delay in milliseconds before the search function is applied to the data array\n                 * @default 500\n                 */\n                delay: 500, // ms\n                /**\n                 * Local search callback function\n                 * Filters the data array based on the search string\n                 * @param data Data array to be filtered\n                 * @param search Search string used to filter the data array\n                 * @returns Filtered data array\n                 */\n                callback: function (data, search) {\n                    if (!data || !search) {\n                        return [];\n                    }\n                    return data.filter(function (item) {\n                        if (!item) {\n                            return false;\n                        }\n                        return Object.values(item).some(function (value) {\n                            if (typeof value !== 'string' && typeof value !== 'number' && typeof value !== 'boolean') {\n                                return false;\n                            }\n                            var valueText = String(value).replace(/<[^>]*>|&nbsp;/g, '').toLowerCase();\n                            return valueText.includes(search.toLowerCase());\n                        });\n                    });\n                }\n            }, \n            /**\n             * Loading spinner options\n             */\n            loading: {\n                /**\n                 * Template to be displayed during data fetching process\n                 */\n                template: \"\\n\\t\\t\\t\\t\\t<div class=\\\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2\\\">\\n\\t\\t\\t\\t\\t\\t<div class=\\\"flex items-center gap-2 px-4 py-2 font-medium leading-none text-2sm border border-gray-200 shadow-default rounded-md text-gray-500 bg-light\\\">\\n\\t\\t\\t\\t\\t\\t\\t<svg class=\\\"animate-spin -ml-1 h-5 w-5 text-gray-600\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" fill=\\\"none\\\" viewBox=\\\"0 0 24 24\\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t<circle class=\\\"opacity-25\\\" cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"3\\\"></circle>\\n\\t\\t\\t\\t\\t\\t\\t\\t<path class=\\\"opacity-75\\\" fill=\\\"currentColor\\\" d=\\\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\\\"></path>\\n\\t\\t\\t\\t\\t\\t\\t</svg>\\n\\t\\t\\t\\t\\t\\t\\t{content}\\n\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\",\n                /**\n                 * Loading text to be displayed in the template\n                 */\n                content: 'Loading...'\n            }, \n            /**\n             * Selectors of the elements to be targeted\n             */\n            attributes: {\n                /**\n                 * Data table element\n                 */\n                table: 'table[data-datatable-table=\"true\"]',\n                /**\n                 * Pagination info element\n                 */\n                info: '[data-datatable-info=\"true\"]',\n                /**\n                 * Page size dropdown element\n                 */\n                size: '[data-datatable-size=\"true\"]',\n                /**\n                 * Pagination element\n                 */\n                pagination: '[data-datatable-pagination=\"true\"]',\n                /**\n                 * Spinner element\n                 */\n                spinner: '[data-datatable-spinner=\"true\"]',\n                /**\n                 * Checkbox element\n                 */\n                check: '[data-datatable-check=\"true\"]',\n                checkbox: '[data-datatable-row-check=\"true\"]'\n            }, \n            /**\n             * Enable or disable state saving\n             */\n            stateSave: true, checkbox: {\n                checkedClass: 'checked'\n            }, \n            /**\n             * Private properties\n             */\n            _state: {} }, config);\n    };\n    /**\n     * Initialize table, tbody, thead, info, size, and pagination elements\n     * @returns {void}\n     */\n    KTDataTable.prototype._initElements = function () {\n        /**\n         * Data table element\n         */\n        this._tableElement = this._element.querySelector(this._config.attributes.table);\n        /**\n         * Table body element\n         */\n        this._tbodyElement = this._tableElement.tBodies[0] || this._tableElement.createTBody();\n        /**\n         * Table head element\n         */\n        this._theadElement = this._tableElement.tHead;\n        /**\n         * Pagination info element\n         */\n        this._infoElement = this._element.querySelector(this._config.attributes.info);\n        /**\n         * Page size dropdown element\n         */\n        this._sizeElement = this._element.querySelector(this._config.attributes.size);\n        /**\n         * Pagination element\n         */\n        this._paginationElement = this._element.querySelector(this._config.attributes.pagination);\n    };\n    /**\n     * Fetch data from the server or from the DOM if `apiEndpoint` is not defined.\n     * @returns {Promise<void>} Promise which is resolved after data has been fetched and checkbox plugin initialized.\n     */\n    KTDataTable.prototype._updateData = function () {\n        return __awaiter(this, void 0, Promise, function () {\n            return __generator(this, function (_a) {\n                this._showSpinner(); // Show spinner before fetching data\n                // Fetch data from the DOM and initialize the checkbox plugin\n                return [2 /*return*/, (typeof this._config.apiEndpoint === 'undefined')\n                        ? this._fetchDataFromLocal().then(this._finalize.bind(this))\n                        : this._fetchDataFromServer().then(this._finalize.bind(this))];\n            });\n        });\n    };\n    /**\n     * Finalize data table after data has been fetched\n     * @returns {void}\n     */\n    KTDataTable.prototype._finalize = function () {\n        this._element.classList.add('datatable-initialized');\n        var headerCheckElement = this._element.querySelector(this._config.attributes.check);\n        if (headerCheckElement) {\n            this._initChecbox(headerCheckElement);\n        }\n        this._attachSearchEvent();\n        if (typeof index_1.default !== \"undefined\") {\n            index_1.default.init();\n        }\n        /**\n         * Hide spinner\n         */\n        this._hideSpinner();\n    };\n    /**\n     * Attach search event to the search input element\n     * @returns {void}\n     */\n    KTDataTable.prototype._attachSearchEvent = function () {\n        var _this = this;\n        var tableId = this._tableId();\n        var searchElement = document.querySelector(\"[data-datatable-search=\\\"#\".concat(tableId, \"\\\"]\"));\n        if (!searchElement) {\n            return;\n        }\n        // Get search state\n        var search = this.getState().search;\n        // Set search value\n        if (search !== undefined) {\n            searchElement.value = typeof search === 'string' ? search : String(search);\n        }\n        if (searchElement) {\n            // Check if a debounced search function already exists\n            if (searchElement._debouncedSearch) {\n                // Remove the existing debounced event listener\n                searchElement.removeEventListener('keyup', searchElement._debouncedSearch);\n            }\n            // Create a new debounced search function\n            var debouncedSearch = this._debounce(function () {\n                _this.search(searchElement.value);\n            }, this._config.search.delay);\n            // Store the new debounced function as a property of the element\n            searchElement._debouncedSearch = debouncedSearch;\n            // Add the new debounced event listener\n            searchElement.addEventListener('keyup', debouncedSearch);\n        }\n    };\n    /**\n     * Initialize the checkbox plugin\n     * @param {HTMLInputElement} headerCheckElement - The header checkbox element\n     * @returns {void}\n     */\n    KTDataTable.prototype._initChecbox = function (headerCheckElement) {\n        this._headerCheckElement = headerCheckElement;\n        this._headerChecked = headerCheckElement.checked;\n        this._targetElements = this._element.querySelectorAll(this._config.attributes.checkbox);\n        this._checkboxHandler();\n    };\n    /**\n     * Fetch data from the DOM\n     * Fetch data from the table element and save it to the `originalData` state property.\n     * This method is used when the data is not fetched from the server via an API endpoint.\n     */\n    KTDataTable.prototype._fetchDataFromLocal = function () {\n        return __awaiter(this, void 0, Promise, function () {\n            var _a, sortField, sortOrder, page, pageSize, search, originalData, _b, originalData_1, originalDataAttributes, _temp, startIndex, endIndex;\n            var _c;\n            return __generator(this, function (_d) {\n                switch (_d.label) {\n                    case 0:\n                        _a = this.getState(), sortField = _a.sortField, sortOrder = _a.sortOrder, page = _a.page, pageSize = _a.pageSize, search = _a.search;\n                        originalData = this.getState().originalData;\n                        // If the table element or the original data is not defined, bail\n                        if (!this._tableElement || originalData === undefined || this._tableConfigInvalidate() || this._localTableHeaderInvalidate() || this._localTableContentInvalidate()) {\n                            this._deleteState();\n                            _b = this._localExtractTableContent(), originalData_1 = _b.originalData, originalDataAttributes = _b.originalDataAttributes;\n                            this._config._state.originalData = originalData_1;\n                            this._config._state.originalDataAttributes = originalDataAttributes;\n                        }\n                        // Update the original data variable\n                        originalData = this.getState().originalData;\n                        _temp = this._data = __spreadArray([], originalData, true);\n                        if (search) {\n                            _temp = this._data = this._config.search.callback.call(this, this._data, search);\n                        }\n                        // If sorting is defined, sort the data\n                        if (sortField !== undefined && sortOrder !== undefined && sortOrder !== '') {\n                            if (typeof this._config.sort.callback === 'function') {\n                                this._data = this._config.sort.callback.call(this, this._data, sortField, sortOrder);\n                            }\n                        }\n                        // If there is data, slice it to the current page size\n                        if (((_c = this._data) === null || _c === void 0 ? void 0 : _c.length) > 0) {\n                            startIndex = (page - 1) * pageSize;\n                            endIndex = startIndex + pageSize;\n                            this._data = this._data.slice(startIndex, endIndex);\n                        }\n                        // Determine number of total rows\n                        this._config._state.totalItems = _temp.length;\n                        // Draw the data\n                        return [4 /*yield*/, this._draw()];\n                    case 1:\n                        // Draw the data\n                        _d.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * Checks if the table content has been invalidated by comparing the current checksum of the table body\n     * with the stored checksum in the state. If the checksums are different, the state is updated with the\n     * new checksum and `true` is returned. Otherwise, `false` is returned.\n     *\n     * @returns {boolean} `true` if the table content has been invalidated, `false` otherwise.\n     */\n    KTDataTable.prototype._localTableContentInvalidate = function () {\n        var checksum = utils_1.default.checksum(JSON.stringify(this._tbodyElement.innerHTML));\n        if (this.getState()._contentChecksum !== checksum) {\n            this._config._state._contentChecksum = checksum;\n            return true;\n        }\n        return false;\n    };\n    KTDataTable.prototype._tableConfigInvalidate = function () {\n        // Remove _data and _state from config\n        var _a = this._config, _data = _a._data, _state = _a._state, restConfig = __rest(_a, [\"_data\", \"_state\"]);\n        var checksum = utils_1.default.checksum(JSON.stringify(restConfig));\n        if (_state._configChecksum !== checksum) {\n            this._config._state._configChecksum = checksum;\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Extracts the table content and returns it as an object containing an array of original data and an array of original data attributes.\n     *\n     * @returns {{originalData: T[], originalDataAttributes: KTDataTableAttributeInterface[]}} - An object containing an array of original data and an array of original data attributes.\n     */\n    KTDataTable.prototype._localExtractTableContent = function () {\n        var originalData = [];\n        var originalDataAttributes = [];\n        var rows = this._tbodyElement.querySelectorAll('tr');\n        rows.forEach(function (row) {\n            var dataRow = {};\n            var dataRowAttribute = {};\n            // Loop through each cell in the row\n            row.querySelectorAll('td').forEach(function (td, index) {\n                var _a;\n                var attributes = {};\n                // Copy all attributes to the cell data\n                Array.from(td.attributes).forEach(function (attr) {\n                    attributes[attr.name] = attr.value;\n                });\n                // Set the data for the current row and cell\n                dataRow[index] = (_a = td.innerHTML) === null || _a === void 0 ? void 0 : _a.trim();\n                dataRowAttribute[index] = attributes;\n            });\n            // If the row has any data, add it to the original data array\n            if (Object.keys(dataRow).length > 0) {\n                originalData.push(dataRow);\n                originalDataAttributes.push(dataRowAttribute);\n            }\n        });\n        return { originalData: originalData, originalDataAttributes: originalDataAttributes };\n    };\n    /**\n     * Check if the table header is invalidated\n     * @returns {boolean} - Returns true if the table header is invalidated, false otherwise\n     */\n    KTDataTable.prototype._localTableHeaderInvalidate = function () {\n        var _a;\n        var originalData = this.getState().originalData;\n        var currentTableHeaders = ((_a = this._getTableHeaders()) === null || _a === void 0 ? void 0 : _a.length) || 0;\n        var totalColumns = originalData.length ? Object.keys(originalData[0]).length : 0;\n        return currentTableHeaders !== totalColumns;\n    };\n    /**\n     * Fetch data from the server\n     */\n    KTDataTable.prototype._fetchDataFromServer = function () {\n        return __awaiter(this, void 0, Promise, function () {\n            var queryParams, response, responseData, error_1;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this._fireEvent('fetch');\n                        this._dispatchEvent('fetch');\n                        queryParams = this._getQueryParamsForFetchRequest();\n                        return [4 /*yield*/, this._performFetchRequest(queryParams)];\n                    case 1:\n                        response = _a.sent();\n                        responseData = null;\n                        _a.label = 2;\n                    case 2:\n                        _a.trys.push([2, 4, , 5]);\n                        return [4 /*yield*/, response.json()];\n                    case 3:\n                        responseData = _a.sent();\n                        return [3 /*break*/, 5];\n                    case 4:\n                        error_1 = _a.sent();\n                        this._noticeOnTable('Error parsing API response as JSON: ' + String(error_1));\n                        return [2 /*return*/];\n                    case 5:\n                        this._fireEvent('fetched', { response: responseData });\n                        this._dispatchEvent('fetched', { response: responseData });\n                        // Use the mapResponse function to transform the data if provided\n                        if (typeof this._config.mapResponse === 'function') {\n                            responseData = this._config.mapResponse.call(this, responseData);\n                        }\n                        this._data = responseData.data;\n                        this._config._state.totalItems = responseData.totalCount;\n                        return [4 /*yield*/, this._draw()];\n                    case 6:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * Get the query params for a fetch request\n     * @returns The query params for the fetch request\n     */\n    KTDataTable.prototype._getQueryParamsForFetchRequest = function () {\n        // Get the current state of the datatable\n        var _a = this.getState(), page = _a.page, pageSize = _a.pageSize, sortField = _a.sortField, sortOrder = _a.sortOrder, filters = _a.filters, search = _a.search;\n        // Create a new URLSearchParams object to store the query params\n        var queryParams = new URLSearchParams();\n        // Add the current page number and page size to the query params\n        queryParams.set('page', String(page));\n        queryParams.set('size', String(pageSize));\n        // If there is a sort order and field set, add them to the query params\n        if (sortOrder !== undefined) {\n            queryParams.set('sortOrder', String(sortOrder));\n        }\n        if (sortField !== undefined) {\n            queryParams.set('sortField', String(sortField));\n        }\n        // If there are any filters set, add them to the query params\n        if (Array.isArray(filters) && filters.length) {\n            queryParams.set('filters', JSON.stringify(filters.map(function (filter) { return ({\n                // Map the filter object to a simpler object with just the necessary properties\n                column: filter.column,\n                type: filter.type,\n                value: filter.value,\n            }); })));\n        }\n        if (search) {\n            queryParams.set('search', typeof search === 'object' ? JSON.stringify(search) : search);\n        }\n        // If a mapRequest function is provided, call it with the query params object\n        if (typeof this._config.mapRequest === 'function') {\n            queryParams = this._config.mapRequest.call(this, queryParams);\n        }\n        // Return the query params object\n        return queryParams;\n    };\n    KTDataTable.prototype._performFetchRequest = function (queryParams) {\n        return __awaiter(this, void 0, Promise, function () {\n            var requestMethod, requestBody, apiEndpointWithQueryParams;\n            var _this = this;\n            return __generator(this, function (_a) {\n                requestMethod = this._config.requestMethod;\n                requestBody = undefined;\n                // If the request method is POST, send the query params as the request body\n                if (requestMethod === 'POST') {\n                    requestBody = queryParams;\n                }\n                else if (requestMethod === 'GET') {\n                    apiEndpointWithQueryParams = new URL(this._config.apiEndpoint);\n                    apiEndpointWithQueryParams.search = queryParams.toString();\n                    this._config.apiEndpoint = apiEndpointWithQueryParams.toString();\n                }\n                return [2 /*return*/, fetch(this._config.apiEndpoint, {\n                        method: requestMethod,\n                        body: requestBody,\n                        headers: this._config.requestHeaders\n                    }).then(function (response) {\n                        if (!response.ok) {\n                            if (response.status === 404 || response.status === 500) {\n                                // Trigger an error event\n                                _this._fireEvent('error', { error: new Error('API endpoint not found') });\n                                _this._dispatchEvent('error', { error: new Error('API endpoint not found') });\n                                _this._noticeOnTable('Error performing fetch request: API endpoint not found');\n                                throw new Error('API endpoint not found');\n                            }\n                            // If the response status is not 200, trigger an error event\n                            _this._fireEvent('error', { error: new Error('Error performing fetch request: ' + response.statusText) });\n                            _this._dispatchEvent('error', { error: new Error('Error performing fetch request: ' + response.statusText) });\n                            _this._noticeOnTable('Error performing fetch request: ' + response.statusText);\n                            _this._hideSpinner();\n                            throw new Error('Error performing fetch request: ' + response.statusText);\n                        }\n                        return response;\n                    }).catch(function (error) {\n                        // Trigger an error event\n                        _this._fireEvent('error', { error: error });\n                        _this._dispatchEvent('error', { error: error });\n                        _this._noticeOnTable('Error performing fetch request: ' + String(error));\n                        _this._hideSpinner();\n                        throw error;\n                    })];\n            });\n        });\n    };\n    /**\n     * Update the table and pagination controls with new data\n     * @returns {Promise<void>} A promise that resolves when the table and pagination controls are updated\n     */\n    KTDataTable.prototype._draw = function () {\n        return __awaiter(this, void 0, Promise, function () {\n            return __generator(this, function (_a) {\n                this._config._state.totalPages = Math.ceil(this.getState().totalItems / this.getState().pageSize) || 0;\n                this._fireEvent('draw');\n                this._dispatchEvent('draw');\n                this._dispose();\n                // Update the table and pagination controls\n                if (this._theadElement && this._tbodyElement) {\n                    this._updateTable();\n                }\n                if (this._infoElement && this._paginationElement) {\n                    this._updatePagination();\n                }\n                this._fireEvent('drew');\n                this._dispatchEvent('drew');\n                this._hideSpinner(); // Hide spinner after data is fetched\n                if (this._config.stateSave) {\n                    this._saveState();\n                }\n                return [2 /*return*/];\n            });\n        });\n    };\n    /**\n     * Update the HTML table with new data\n     * @returns {HTMLTableSectionElement} The new table body element\n     */\n    KTDataTable.prototype._updateTable = function () {\n        // Clear the existing table contents using a more efficient method\n        while (this._tableElement.tBodies.length) {\n            this._tableElement.removeChild(this._tableElement.tBodies[0]);\n        }\n        // Create the table body with the new data\n        var tbodyElement = this._tableElement.createTBody();\n        this._updateTableContent(tbodyElement);\n        return tbodyElement;\n    };\n    /**\n     * Initialize the table header\n     * Add sort event listener to all sortable columns\n     */\n    KTDataTable.prototype._initTableHeader = function () {\n        var _this = this;\n        if (!this._theadElement) {\n            return;\n        }\n        // Set the initial sort icon\n        this._setSortIcon(this.getState().sortField, this.getState().sortOrder);\n        // Get all the table headers\n        var headers = this._getTableHeaders();\n        // Loop through each table header\n        headers.forEach(function (header) {\n            // If the sort class is not found, it's not a sortable column\n            if (!header.querySelector(\".\".concat(_this._config.sort.classes.base))) {\n                return;\n            }\n            var sortAttribute = header.getAttribute('data-datatable-column-sort') || header.getAttribute('data-datatable-column');\n            var sortField = sortAttribute ? sortAttribute : header.cellIndex;\n            // Add click event listener to the header\n            header.addEventListener('click', function () {\n                var sortOrder = _this._toggleSortOrder(sortField);\n                _this._setSortIcon(sortField, sortOrder);\n                _this._sort(sortField);\n            });\n        });\n    };\n    /**\n     * Returns an array of table headers as HTMLTableCellElement.\n     * @returns {HTMLTableCellElement[]} An array of table headers.\n     */\n    KTDataTable.prototype._getTableHeaders = function () {\n        if (!this._theadElement) {\n            return [];\n        }\n        return Array.from(this._theadElement.querySelectorAll('th'));\n    };\n    /**\n     * Sets the sort icon in the table header\n     * @param sortField The field to set the sort icon for\n     * @param sortOrder The sort order (ascending or descending)\n     */\n    KTDataTable.prototype._setSortIcon = function (sortField, sortOrder) {\n        var sortClass = sortOrder ? (sortOrder === 'asc' ? this._config.sort.classes.asc : this._config.sort.classes.desc) : '';\n        // Get the appropriate table header element\n        var th = typeof sortField === 'number'\n            ? this._theadElement.querySelectorAll('th')[sortField]\n            : this._theadElement.querySelector(\"th[data-datatable-column=\\\"\".concat(String(sortField), \"\\\"], th[data-datatable-column-sort=\\\"\").concat(String(sortField), \"\\\"]\"));\n        if (th) {\n            var sortElement = th.querySelector(\".\".concat(this._config.sort.classes.base));\n            if (sortElement) {\n                sortElement.className = \"\".concat(this._config.sort.classes.base, \" \").concat(sortClass).trim();\n            }\n        }\n    };\n    /**\n     * Toggles the sort order of a column\n     * @param sortField The field to toggle the sort order for\n     * @returns The new sort order (ascending, descending or unsorted)\n     */\n    KTDataTable.prototype._toggleSortOrder = function (sortField) {\n        var _this = this;\n        // If the sort field is the same as the current sort field,\n        // toggle the sort order. Otherwise, set the sort order to ascending.\n        return (function () {\n            if (_this.getState().sortField === sortField) {\n                switch (_this.getState().sortOrder) {\n                    case 'asc':\n                        return 'desc'; // Descending\n                    case 'desc':\n                        return ''; // Unsorted\n                    default:\n                        return 'asc'; // Ascending\n                }\n            }\n            return 'asc'; // Ascending\n        })();\n    };\n    /**\n     * Update the table content\n     * @param tbodyElement The table body element\n     * @returns {HTMLTableSectionElement} The updated table body element\n     */\n    KTDataTable.prototype._updateTableContent = function (tbodyElement) {\n        var _this = this;\n        var fragment = document.createDocumentFragment();\n        tbodyElement.textContent = ''; // Clear the tbody element\n        if (this._data.length === 0) {\n            this._noticeOnTable(this._config.infoEmpty || '');\n            return tbodyElement;\n        }\n        this._data.forEach(function (item, rowIndex) {\n            var row = document.createElement('tr');\n            if (!_this._config.columns) {\n                var dataRowAttributes_1 = _this.getState().originalDataAttributes\n                    ? _this.getState().originalDataAttributes[rowIndex]\n                    : null;\n                Object.keys(item).forEach(function (key, colIndex) {\n                    var td = document.createElement('td');\n                    td.innerHTML = item[key];\n                    if (dataRowAttributes_1) {\n                        for (var attr in dataRowAttributes_1[colIndex]) {\n                            td.setAttribute(attr, dataRowAttributes_1[colIndex][attr]);\n                        }\n                    }\n                    row.appendChild(td);\n                });\n            }\n            else {\n                Object.keys(_this._config.columns).forEach(function (key) {\n                    var td = document.createElement('td');\n                    var columnDef = _this._config.columns[key];\n                    if (typeof columnDef.render === 'function') {\n                        td.innerHTML = columnDef.render.call(_this, item[key], item, _this);\n                    }\n                    else {\n                        td.textContent = item[key];\n                    }\n                    if (typeof columnDef.createdCell === 'function') {\n                        columnDef.createdCell.call(_this, td, item[key], item, row);\n                    }\n                    row.appendChild(td);\n                });\n            }\n            fragment.appendChild(row);\n        });\n        tbodyElement.appendChild(fragment);\n        return tbodyElement;\n    };\n    /**\n     * Show a notice on the table\n     * @param message The message to show. If empty, the message will be removed\n     * @returns {void}\n     */\n    KTDataTable.prototype._noticeOnTable = function (message) {\n        var _a;\n        if (message === void 0) { message = ''; }\n        var row = this._tableElement.tBodies[0].insertRow();\n        var cell = row.insertCell();\n        cell.colSpan = ((_a = this._getTableHeaders()) === null || _a === void 0 ? void 0 : _a.length) || 0;\n        cell.innerHTML = message;\n    };\n    KTDataTable.prototype._updatePagination = function () {\n        this._removeChildElements(this._sizeElement);\n        this._createPageSizeControls(this._sizeElement);\n        this._removeChildElements(this._paginationElement);\n        this._createPaginationControls(this._infoElement, this._paginationElement);\n    };\n    /**\n     * Removes all child elements from the given container element.\n     * @param container The container element to remove the child elements from.\n     */\n    KTDataTable.prototype._removeChildElements = function (container) {\n        if (!container) {\n            return;\n        }\n        // Loop through all child elements of the container and remove them one by one\n        while (container.firstChild) {\n            // Remove the first child element (which is the first element in the list of child elements)\n            container.removeChild(container.firstChild);\n        }\n    };\n    /**\n     * Creates a container element for the items per page selector.\n     * @param _sizeElement The element to create the page size controls in.\n     * @returns The container element.\n     */\n    KTDataTable.prototype._createPageSizeControls = function (_sizeElement) {\n        var _this = this;\n        // If no element is provided, return early\n        if (!_sizeElement) {\n            return _sizeElement;\n        }\n        // Create <option> elements for each page size option\n        var options = this._config.pageSizes.map(function (size) {\n            var option = document.createElement('option');\n            option.value = String(size);\n            option.text = String(size);\n            option.selected = _this.getState().pageSize === size;\n            return option;\n        });\n        // Add the <option> elements to the provided element\n        _sizeElement.append.apply(_sizeElement, options);\n        // Create an event listener for the \"change\" event on the element\n        var _pageSizeControlsEvent = function (event) {\n            // When the element changes, reload the page with the new page size and page number 1\n            _this._reloadPageSize(Number(event.target.value), 1);\n        };\n        // Bind the event listener to the component instance\n        _sizeElement.onchange = _pageSizeControlsEvent.bind(this);\n        // Return the element\n        return _sizeElement;\n    };\n    /**\n     * Reloads the data with the specified page size and optional page number.\n     * @param pageSize The new page size.\n     * @param page The new page number (optional, defaults to 1).\n     */\n    KTDataTable.prototype._reloadPageSize = function (pageSize, page) {\n        if (page === void 0) { page = 1; }\n        // Update the page size and page number in the state\n        this._config._state.pageSize = pageSize;\n        this._config._state.page = page;\n        // Update the data with the new page size and page number\n        this._updateData();\n    };\n    /**\n     * Creates the pagination controls for the component.\n     * @param _infoElement The element to set the info text in.\n     * @param _paginationElement The element to create the pagination controls in.\n     * @return {HTMLElement} The element containing the pagination controls.\n     */\n    KTDataTable.prototype._createPaginationControls = function (_infoElement, _paginationElement) {\n        if (!_infoElement || !_paginationElement || this._data.length === 0) {\n            return null;\n        }\n        this._setPaginationInfoText(_infoElement);\n        var paginationContainer = this._createPaginationContainer(_paginationElement);\n        if (paginationContainer) {\n            this._createPaginationButtons(paginationContainer);\n        }\n        return paginationContainer;\n    };\n    /**\n     * Sets the info text for the pagination controls.\n     * @param _infoElement The element to set the info text in.\n     */\n    KTDataTable.prototype._setPaginationInfoText = function (_infoElement) {\n        _infoElement.textContent = this._config.info\n            .replace('{start}', (this.getState().page - 1) * this.getState().pageSize + 1 + '')\n            .replace('{end}', Math.min(this.getState().page * this.getState().pageSize, this.getState().totalItems) + '')\n            .replace('{total}', this.getState().totalItems + '');\n    };\n    /**\n     * Creates the container element for the pagination controls.\n     * @param _paginationElement The element to create the pagination controls in.\n     * @return {HTMLElement} The container element.\n     */\n    KTDataTable.prototype._createPaginationContainer = function (_paginationElement) {\n        var paginationContainer = document.createElement('div');\n        paginationContainer.className = 'pagination';\n        _paginationElement.appendChild(paginationContainer);\n        return paginationContainer;\n    };\n    /**\n     * Creates the pagination buttons for the component.\n     * @param paginationContainer The container element for the pagination controls.\n     */\n    KTDataTable.prototype._createPaginationButtons = function (paginationContainer) {\n        var _this = this;\n        var _a = this.getState(), currentPage = _a.page, totalPages = _a.totalPages;\n        var _b = this._config.pagination, previous = _b.previous, next = _b.next, number = _b.number, more = _b.more;\n        // Helper function to create a button\n        var createButton = function (text, className, disabled, handleClick) {\n            var button = document.createElement('button');\n            button.className = className;\n            button.innerHTML = text;\n            button.disabled = disabled;\n            button.onclick = handleClick;\n            return button;\n        };\n        // Add Previous Button\n        paginationContainer.appendChild(createButton(previous.text, \"\".concat(previous.class).concat(currentPage === 1 ? ' disabled' : ''), currentPage === 1, function () { return _this._paginateData(currentPage - 1); }));\n        // Calculate range of pages\n        var pageMoreEnabled = this._config.pageMore;\n        if (pageMoreEnabled) {\n            var maxButtons = this._config.pageMoreLimit;\n            var range_1 = this._calculatePageRange(currentPage, totalPages, maxButtons);\n            // Add start ellipsis\n            if (range_1.start > 1) {\n                paginationContainer.appendChild(createButton(more.text, more.class, false, function () { return _this._paginateData(Math.max(1, range_1.start - 1)); }));\n            }\n            var _loop_1 = function (i) {\n                paginationContainer.appendChild(createButton(number.text.replace('{page}', i.toString()), \"\".concat(number.class).concat(currentPage === i ? ' active disabled' : ''), currentPage === i, function () { return _this._paginateData(i); }));\n            };\n            // Add page buttons\n            for (var i = range_1.start; i <= range_1.end; i++) {\n                _loop_1(i);\n            }\n            // Add end ellipsis\n            if (pageMoreEnabled && range_1.end < totalPages) {\n                paginationContainer.appendChild(createButton(more.text, more.class, false, function () { return _this._paginateData(Math.min(totalPages, range_1.end + 1)); }));\n            }\n        }\n        else {\n            var _loop_2 = function (i) {\n                paginationContainer.appendChild(createButton(number.text.replace('{page}', i.toString()), \"\".concat(number.class).concat(currentPage === i ? ' active disabled' : ''), currentPage === i, function () { return _this._paginateData(i); }));\n            };\n            // Add page buttons\n            for (var i = 1; i <= totalPages; i++) {\n                _loop_2(i);\n            }\n        }\n        // Add Next Button\n        paginationContainer.appendChild(createButton(next.text, \"\".concat(next.class).concat(currentPage === totalPages ? ' disabled' : ''), currentPage === totalPages, function () { return _this._paginateData(currentPage + 1); }));\n    };\n    // New helper method to calculate page range\n    KTDataTable.prototype._calculatePageRange = function (currentPage, totalPages, maxButtons) {\n        var startPage, endPage;\n        var halfMaxButtons = Math.floor(maxButtons / 2);\n        if (totalPages <= maxButtons) {\n            startPage = 1;\n            endPage = totalPages;\n        }\n        else {\n            startPage = Math.max(currentPage - halfMaxButtons, 1);\n            endPage = Math.min(startPage + maxButtons - 1, totalPages);\n            if (endPage - startPage < maxButtons - 1) {\n                startPage = Math.max(endPage - maxButtons + 1, 1);\n            }\n        }\n        return { start: startPage, end: endPage };\n    };\n    /**\n     * Method for handling pagination\n     * @param page - The page number to navigate to\n     */\n    KTDataTable.prototype._paginateData = function (page) {\n        if (page < 1 || !Number.isInteger(page)) {\n            return;\n        }\n        this._fireEvent('pagination', { page: page });\n        this._dispatchEvent('pagination', { page: page });\n        if (page >= 1 && page <= this.getState().totalPages) {\n            this._config._state.page = page;\n            this._updateData();\n        }\n    };\n    // Method to show the loading spinner\n    KTDataTable.prototype._showSpinner = function () {\n        var spinner = this._element.querySelector(this._config.attributes.spinner) || this._createSpinner();\n        if (spinner) {\n            spinner.style.display = 'block';\n        }\n        this._element.classList.add('loading');\n    };\n    // Method to hide the loading spinner\n    KTDataTable.prototype._hideSpinner = function () {\n        var spinner = this._element.querySelector(this._config.attributes.spinner);\n        if (spinner) {\n            spinner.style.display = 'none';\n        }\n        this._element.classList.remove('loading');\n    };\n    // Method to create a spinner element if it doesn't exist\n    KTDataTable.prototype._createSpinner = function () {\n        if (typeof this._config.loading === 'undefined') {\n            return null;\n        }\n        var template = document.createElement('template');\n        template.innerHTML = this._config.loading.template.trim().replace('{content}', this._config.loading.content);\n        var spinner = template.content.firstChild;\n        spinner.setAttribute('data-datatable-spinner', 'true');\n        this._tableElement.appendChild(spinner);\n        return spinner;\n    };\n    /**\n     * Saves the current state of the table to local storage.\n     * @returns {void}\n     */\n    KTDataTable.prototype._saveState = function () {\n        this._fireEvent('stateSave');\n        this._dispatchEvent('stateSave');\n        var ns = this._tableNamespace();\n        if (ns) {\n            localStorage.setItem(ns, JSON.stringify(this.getState()));\n        }\n    };\n    /**\n     * Loads the saved state of the table from local storage, if it exists.\n     * @returns {Object} The saved state of the table, or null if no saved state exists.\n     */\n    KTDataTable.prototype._loadState = function () {\n        var stateString = localStorage.getItem(this._tableNamespace());\n        if (!stateString)\n            return null;\n        try {\n            var state = JSON.parse(stateString);\n            if (state)\n                this._config._state = state;\n            return state;\n        }\n        catch (_a) { } // eslint-disable-line no-empty\n        return null;\n    };\n    KTDataTable.prototype._deleteState = function () {\n        var ns = this._tableNamespace();\n        if (ns) {\n            localStorage.removeItem(ns);\n        }\n    };\n    /**\n     * Gets the namespace for the table's state.\n     * If a namespace is specified in the config, it is used.\n     * Otherwise, if the table element has an ID, it is used.\n     * Otherwise, if the component element has an ID, it is used.\n     * Finally, the component's UID is used.\n     * @returns {string} The namespace for the table's state.\n     */\n    KTDataTable.prototype._tableNamespace = function () {\n        var _a;\n        // Use the specified namespace, if one is given\n        if (this._config.stateNamespace) {\n            return this._config.stateNamespace;\n        }\n        // Fallback to the component's UID\n        return (_a = this._tableId()) !== null && _a !== void 0 ? _a : this._name;\n    };\n    KTDataTable.prototype._tableId = function () {\n        var _a, _b;\n        var id = null;\n        // If the table element has an ID, use that\n        if ((_a = this._tableElement) === null || _a === void 0 ? void 0 : _a.getAttribute('id')) {\n            id = this._tableElement.getAttribute('id');\n        }\n        // If the component element has an ID, use that\n        if ((_b = this._element) === null || _b === void 0 ? void 0 : _b.getAttribute('id')) {\n            id = this._element.getAttribute('id');\n        }\n        return id;\n    };\n    /**\n     * Sorts the data in the table by the specified field.\n     * @param sortField The field to sort by.\n     */\n    KTDataTable.prototype._sort = function (sortField) {\n        // Determine the new sort order based on the current state\n        var newSortOrder = this._toggleSortOrder(sortField);\n        // Update the current sort field and order\n        this._config._state.sortField = sortField;\n        this._config._state.sortOrder = newSortOrder;\n        this._fireEvent('sort', { field: sortField, order: newSortOrder });\n        this._dispatchEvent('sort', { field: sortField, order: newSortOrder });\n        // Fetch data from the server with the new sort order\n        this._updateData();\n    };\n    KTDataTable.prototype._dispose = function () {\n        if (this._headerCheckElement) {\n            this._headerCheckElement.removeEventListener('click', this._checkboxListener);\n        }\n    };\n    KTDataTable.prototype._debounce = function (func, wait) {\n        var timeout;\n        return function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var later = function () {\n                clearTimeout(timeout);\n                func.apply(void 0, args);\n            };\n            clearTimeout(timeout);\n            timeout = window.setTimeout(later, wait);\n        };\n    };\n    /**\n     * Event handlers\n     */\n    KTDataTable.prototype._checkboxHandler = function () {\n        var _this = this;\n        // Handle header checkbox change event\n        this._headerCheckElement.addEventListener('click', this._checkboxListener);\n        // Handle target checbox change event\n        event_handler_1.default.on(document.body, this._config.attributes.checkbox, 'input', function (event) {\n            _this._checkboxUpdate(event); // Update checkbox state based on checked rows\n        });\n    };\n    /**\n     * Checks if element is checked\n     * @returns {boolean}\n     */\n    KTDataTable.prototype._isChecked = function () {\n        return this._headerChecked;\n    };\n    /**\n     * Change checkbox state\n     * @param checked The new state of the checkbox\n     * @returns {void}\n     */\n    KTDataTable.prototype._change = function (checked) {\n        var payload = { cancel: false };\n        this._fireEvent('change', payload);\n        this._dispatchEvent('change', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        this._headerChecked = checked;\n        this._headerCheckElement.checked = checked;\n        if (this._targetElements) {\n            this._targetElements.forEach(function (element) {\n                if (element) {\n                    element.checked = checked;\n                }\n            });\n        }\n        this._fireEvent('changed');\n        this._dispatchEvent('changed');\n    };\n    /**\n     * Toggle checkbox state\n     * @param event The event if available\n     * @returns {void}\n     */\n    KTDataTable.prototype._checkboxToggle = function (event) {\n        var checked = !this._isChecked();\n        var eventType = checked ? 'checked' : 'unchecked';\n        this._fireEvent(eventType);\n        this._dispatchEvent(eventType);\n        this._change(checked);\n    };\n    /**\n     * Update checkbox state based on checked rows\n     * @param event The event if available\n     * @returns {void}\n     */\n    KTDataTable.prototype._checkboxUpdate = function (event) {\n        var checked = 0;\n        var total = this._targetElements.length;\n        for (var i = 0; i < total; i++) {\n            var element = this._targetElements[i];\n            if (!element) {\n                continue;\n            }\n            var row = element.closest('tr');\n            if (!row) {\n                continue;\n            }\n            if (element.checked) {\n                row.classList.add(this._config.checkbox.checkedClass);\n                checked++;\n            }\n            else {\n                row.classList.remove(this._config.checkbox.checkedClass);\n            }\n        }\n        if (checked === 0) {\n            this._headerCheckElement.indeterminate = false;\n            this._change(false);\n        }\n        if (checked > 0 && checked < total) {\n            this._headerCheckElement.indeterminate = true;\n        }\n        if (checked === total) {\n            this._headerCheckElement.indeterminate = false;\n            this._change(true);\n        }\n    };\n    /**\n     * Get checked row IDs\n     * @returns {string[]} An array of checked row IDs\n     */\n    KTDataTable.prototype._getChecked = function () {\n        var checked = [];\n        this._targetElements.forEach(function (element) {\n            if (element && element.checked) {\n                var value = element.value;\n                if (value) {\n                    checked.push(value);\n                }\n            }\n        });\n        return checked;\n    };\n    KTDataTable.prototype.isChecked = function () {\n        return this._isChecked();\n    };\n    KTDataTable.prototype.toggle = function () {\n        this._checkboxToggle();\n    };\n    /**\n     * Check all rows\n     * @returns {void}\n     */\n    KTDataTable.prototype.check = function () {\n        this._change(true);\n        this._checkboxUpdate();\n    };\n    /**\n     * Uncheck all rows\n     * @returns {void}\n     */\n    KTDataTable.prototype.uncheck = function () {\n        this._change(false);\n    };\n    /**\n     * Get checked row IDs\n     * @returns {string[]} An array of checked row IDs\n     */\n    KTDataTable.prototype.getChecked = function () {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return this._getChecked();\n    };\n    KTDataTable.prototype.update = function () {\n        this._checkboxUpdate();\n    };\n    /**\n     * Gets the current state of the table.\n     * @returns {KTDataTableStateInterface} The current state of the table.\n     */\n    KTDataTable.prototype.getState = function () {\n        return __assign({ \n            /**\n             * The current page number.\n             */\n            page: 1, \n            /**\n             * The field that the data is sorted by.\n             */\n            sortField: null, \n            /**\n             * The sort order (ascending or descending).\n             */\n            sortOrder: '', \n            /**\n             * The number of rows to display per page.\n             */\n            pageSize: this._config.pageSize, filters: [] }, this._config._state);\n    };\n    /**\n     * Sorts the data in the table by the specified field.\n     * @param field The field to sort by.\n     */\n    KTDataTable.prototype.sort = function (field) {\n        // Sort the data\n        this._sort(field);\n    };\n    /**\n     * Navigates to the specified page in the data table.\n     * @param page The page number to navigate to.\n     */\n    KTDataTable.prototype.goPage = function (page) {\n        if (page < 1 || !Number.isInteger(page)) {\n            return;\n        }\n        // Navigate to the specified page\n        this._paginateData(page);\n    };\n    /**\n     * Set the page size of the data table.\n     * @param pageSize The new page size.\n     */\n    KTDataTable.prototype.setPageSize = function (pageSize) {\n        if (!Number.isInteger(pageSize)) {\n            return;\n        }\n        /**\n         * Reload the page size of the data table.\n         * @param pageSize The new page size.\n         */\n        this._reloadPageSize(pageSize);\n    };\n    /**\n     * Reloads the data from the server and updates the table.\n     * Triggers the 'reload' event and the 'kt.datatable.reload' custom event.\n     */\n    KTDataTable.prototype.reload = function () {\n        this._fireEvent('reload');\n        this._dispatchEvent('reload');\n        // Fetch the data from the server using the current sort and filter settings\n        this._updateData();\n    };\n    KTDataTable.prototype.redraw = function (page) {\n        if (page === void 0) { page = 1; }\n        this._fireEvent('redraw');\n        this._dispatchEvent('redraw');\n        this._paginateData(page);\n    };\n    /**\n     * Show the loading spinner of the data table.\n     */\n    KTDataTable.prototype.showSpinner = function () {\n        /**\n         * Show the loading spinner of the data table.\n         */\n        this._showSpinner();\n    };\n    /**\n     * Hide the loading spinner of the data table.\n     */\n    KTDataTable.prototype.hideSpinner = function () {\n        /**\n         * Hide the loading spinner of the data table.\n         */\n        this._hideSpinner();\n    };\n    /**\n     * Filter data using the specified filter object.\n     * Replaces the existing filter object for the column with the new one.\n     * @param filter Filter object containing the column name and its value.\n     * @returns The KTDataTable instance.\n     * @throws Error if the filter object is null or undefined.\n     */\n    KTDataTable.prototype.setFilter = function (filter) {\n        this._config._state.filters = __spreadArray(__spreadArray([], (this.getState().filters || []).filter(function (f) { return f.column !== filter.column; }), true), [\n            filter\n        ], false);\n        return this;\n    };\n    KTDataTable.prototype.dispose = function () {\n        this._dispose();\n    };\n    KTDataTable.prototype.search = function (query) {\n        this._config._state.search = query;\n        this._config._state.page = 1;\n        this.reload();\n    };\n    /**\n     * Create KTDataTable instances for all elements with a data-datatable=\"true\" attribute.\n     *\n     * This function should be called after the control(s) have been\n     * loaded and parsed by the browser. It will create instances of\n     * KTDataTable for all elements with a data-datatable=\"true\" attribute.\n     */\n    KTDataTable.createInstances = function () {\n        var _this = this;\n        var elements = document.querySelectorAll('[data-datatable=\"true\"]');\n        elements.forEach(function (element) {\n            if (element.hasAttribute('data-datatable') && !element.classList.contains('datatable-initialized')) {\n                /**\n                 * Create an instance of KTDataTable for the given element\n                 * @param element The element to create an instance for\n                 */\n                var instance = new KTDataTable(element);\n                _this._instances.set(element, instance);\n            }\n        });\n    };\n    /**\n     * Get the KTDataTable instance for a given element.\n     *\n     * @param element The element to retrieve the instance for\n     * @returns The KTDataTable instance or undefined if not found\n     */\n    KTDataTable.getInstance = function (element) {\n        return this._instances.get(element);\n    };\n    /**\n     * Initializes all KTDataTable instances on the page.\n     *\n     * This function should be called after the control(s) have been\n     * loaded and parsed by the browser.\n     */\n    KTDataTable.init = function () {\n        // Create instances of KTDataTable for all elements with a\n        // data-datatable=\"true\" attribute\n        KTDataTable.createInstances();\n    };\n    /**\n     * Static variables\n     */\n    KTDataTable._instances = new Map();\n    return KTDataTable;\n}(component_1.default));\nexports.KTDataTable = KTDataTable;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/datatable/datatable.ts?");

/***/ }),

/***/ "./src/core/components/datatable/index.ts":
/*!************************************************!*\
  !*** ./src/core/components/datatable/index.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDataTable = void 0;\nvar datatable_1 = __webpack_require__(/*! ./datatable */ \"./src/core/components/datatable/datatable.ts\");\nObject.defineProperty(exports, \"KTDataTable\", ({ enumerable: true, get: function () { return datatable_1.KTDataTable; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/datatable/index.ts?");

/***/ }),

/***/ "./src/core/components/dismiss/dismiss.ts":
/*!************************************************!*\
  !*** ./src/core/components/dismiss/dismiss.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDismiss = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTDismiss = /** @class */ (function (_super) {\n    __extends(KTDismiss, _super);\n    function KTDismiss(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'dismiss';\n        _this._defaultConfig = {\n            hiddenClass: 'hidden',\n            mode: 'remove',\n            interrupt: true,\n            target: '',\n        };\n        _this._config = _this._defaultConfig;\n        _this._isAnimating = false;\n        _this._targetElement = null;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._config['mode'] = _this._config['mode'];\n        if (!_this._element)\n            return _this;\n        _this._targetElement = _this._getTargetElement();\n        if (!_this._targetElement) {\n            return _this;\n        }\n        _this._handlers();\n        return _this;\n    }\n    KTDismiss.prototype._getTargetElement = function () {\n        return (dom_1.default.getElement(this._element.getAttribute('data-dismiss')) ||\n            dom_1.default.getElement(this._getOption('target')));\n    };\n    KTDismiss.prototype._handlers = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        this._element.addEventListener('click', function (event) {\n            event.preventDefault();\n            if (_this._getOption('interrupt') === true) {\n                event.stopPropagation();\n            }\n            _this._dismiss();\n        });\n    };\n    KTDismiss.prototype._dismiss = function () {\n        var _this = this;\n        if (this._isAnimating) {\n            return;\n        }\n        var payload = { cancel: false };\n        this._fireEvent('dismiss', payload);\n        this._dispatchEvent('dismiss', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (!this._targetElement)\n            return;\n        this._targetElement.style.opacity = '0';\n        dom_1.default.reflow(this._targetElement);\n        this._isAnimating = true;\n        dom_1.default.transitionEnd(this._targetElement, function () {\n            if (!_this._targetElement)\n                return;\n            _this._isAnimating = false;\n            _this._targetElement.style.opacity = '';\n            if (_this._getOption('mode').toString().toLowerCase() === 'hide') {\n                _this._targetElement.classList.add(_this._getOption('hiddenClass'));\n            }\n            else {\n                dom_1.default.remove(_this._targetElement);\n            }\n            _this._fireEvent('dismissed');\n            _this._dispatchEvent('dismissed');\n        });\n    };\n    KTDismiss.prototype.getTargetElement = function () {\n        return this._targetElement;\n    };\n    KTDismiss.prototype.dismiss = function () {\n        this._dismiss();\n    };\n    KTDismiss.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'dismiss')) {\n            return data_1.default.get(element, 'dismiss');\n        }\n        if (element.getAttribute('data-dismiss') !== \"false\") {\n            return new KTDismiss(element);\n        }\n        return null;\n    };\n    KTDismiss.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTDismiss(element, config);\n    };\n    KTDismiss.createInstances = function () {\n        var elements = document.querySelectorAll('[data-dismiss]:not([data-dismiss=\"false\"])');\n        elements.forEach(function (element) {\n            new KTDismiss(element);\n        });\n    };\n    KTDismiss.init = function () {\n        KTDismiss.createInstances();\n    };\n    return KTDismiss;\n}(component_1.default));\nexports.KTDismiss = KTDismiss;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/dismiss/dismiss.ts?");

/***/ }),

/***/ "./src/core/components/dismiss/index.ts":
/*!**********************************************!*\
  !*** ./src/core/components/dismiss/index.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDismiss = void 0;\nvar dismiss_1 = __webpack_require__(/*! ./dismiss */ \"./src/core/components/dismiss/dismiss.ts\");\nObject.defineProperty(exports, \"KTDismiss\", ({ enumerable: true, get: function () { return dismiss_1.KTDismiss; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/dismiss/index.ts?");

/***/ }),

/***/ "./src/core/components/drawer/drawer.ts":
/*!**********************************************!*\
  !*** ./src/core/components/drawer/drawer.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDrawer = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar utils_1 = __webpack_require__(/*! ../../helpers/utils */ \"./src/core/helpers/utils.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTDrawer = /** @class */ (function (_super) {\n    __extends(KTDrawer, _super);\n    function KTDrawer(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'drawer';\n        _this._defaultConfig = {\n            zindex: '100',\n            enable: true,\n            class: '',\n            hiddenClass: 'hidden',\n            backdrop: true,\n            backdropClass: 'transition-all duration-300 fixed inset-0 bg-gray-900 opacity-25',\n            backdropStatic: false,\n            keyboard: true,\n            disableScroll: true,\n            persistent: false,\n            focus: true\n        };\n        _this._config = _this._defaultConfig;\n        _this._isOpen = false;\n        _this._isTransitioning = false;\n        _this._backdropElement = null;\n        _this._relatedTarget = null;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._handleClose();\n        _this._update();\n        return _this;\n    }\n    KTDrawer.prototype._handleClose = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        event_handler_1.default.on(this._element, '[data-drawer-hide]', 'click', function () {\n            _this._hide();\n        });\n    };\n    KTDrawer.prototype._toggle = function (relatedTarget) {\n        var payload = { cancel: false };\n        this._fireEvent('toggle', payload);\n        this._dispatchEvent('toggle', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (this._isOpen === true) {\n            this._hide();\n        }\n        else {\n            this._show(relatedTarget);\n        }\n    };\n    KTDrawer.prototype._show = function (relatedTarget) {\n        var _this = this;\n        if (this._isOpen || this._isTransitioning) {\n            return;\n        }\n        var payload = { cancel: false };\n        this._fireEvent('show', payload);\n        this._dispatchEvent('show', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        KTDrawer.hide();\n        if (this._getOption('backdrop') === true)\n            this._createBackdrop();\n        if (relatedTarget)\n            this._relatedTarget = relatedTarget;\n        if (!this._element)\n            return;\n        this._isTransitioning = true;\n        this._element.classList.remove(this._getOption('hiddenClass'));\n        this._element.setAttribute('role', 'dialog');\n        this._element.setAttribute('aria-modal', 'true');\n        this._element.setAttribute('tabindex', '-1');\n        var zindex = parseInt(this._getOption('zindex'));\n        if (zindex > 0) {\n            this._element.style.zIndex = \"\".concat(zindex);\n        }\n        if (this._getOption('disableScroll')) {\n            document.body.style.overflow = 'hidden';\n        }\n        dom_1.default.reflow(this._element);\n        this._element.classList.add('open');\n        dom_1.default.transitionEnd(this._element, function () {\n            _this._isTransitioning = false;\n            _this._isOpen = true;\n            if (_this._getOption('focus') === true) {\n                _this._autoFocus();\n            }\n            _this._fireEvent('shown');\n            _this._dispatchEvent('shown');\n        });\n    };\n    KTDrawer.prototype._hide = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        if (this._isOpen === false || this._isTransitioning) {\n            return;\n        }\n        var payload = { cancel: false };\n        this._fireEvent('hide', payload);\n        this._dispatchEvent('hide', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        this._isTransitioning = true;\n        this._element.removeAttribute('role');\n        this._element.removeAttribute('aria-modal');\n        this._element.removeAttribute('tabindex');\n        if (this._getOption('disableScroll')) {\n            document.body.style.overflow = '';\n        }\n        dom_1.default.reflow(this._element);\n        this._element.classList.remove('open');\n        if (this._getOption('backdrop') === true) {\n            this._deleteBackdrop();\n        }\n        dom_1.default.transitionEnd(this._element, function () {\n            if (!_this._element)\n                return;\n            _this._isTransitioning = false;\n            _this._isOpen = false;\n            _this._element.classList.add(_this._getOption('hiddenClass'));\n            _this._element.style.zIndex = '';\n            _this._fireEvent('hidden');\n            _this._dispatchEvent('hidden');\n        });\n    };\n    KTDrawer.prototype._update = function () {\n        if (this._getOption('class').length > 0) {\n            if (this.isEnabled()) {\n                dom_1.default.addClass(this._element, this._getOption('class'));\n            }\n            else {\n                dom_1.default.removeClass(this._element, this._getOption('class'));\n            }\n        }\n    };\n    KTDrawer.prototype._autoFocus = function () {\n        if (!this._element)\n            return;\n        var input = this._element.querySelector('[data-drawer-focus]');\n        if (!input)\n            return;\n        else\n            input.focus();\n    };\n    KTDrawer.prototype._createBackdrop = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        var zindex = parseInt(this._getOption('zindex'));\n        this._backdropElement = document.createElement('DIV');\n        this._backdropElement.style.zIndex = (zindex - 1).toString();\n        this._backdropElement.classList.add('drawer-backdrop');\n        document.body.append(this._backdropElement);\n        dom_1.default.reflow(this._backdropElement);\n        dom_1.default.addClass(this._backdropElement, this._getOption('backdropClass'));\n        this._backdropElement.addEventListener('click', function (event) {\n            event.preventDefault();\n            if (_this._getOption('backdropStatic') === false) {\n                _this._hide();\n            }\n        });\n    };\n    KTDrawer.prototype._deleteBackdrop = function () {\n        var _this = this;\n        if (!this._backdropElement)\n            return;\n        dom_1.default.reflow(this._backdropElement);\n        this._backdropElement.style.opacity = \"0\";\n        dom_1.default.transitionEnd(this._backdropElement, function () {\n            if (!_this._backdropElement)\n                return;\n            dom_1.default.remove(_this._backdropElement);\n        });\n    };\n    KTDrawer.prototype._isEnabled = function () {\n        return this._getOption('enable');\n    };\n    KTDrawer.prototype.toggle = function () {\n        return this._toggle();\n    };\n    KTDrawer.prototype.show = function (relatedTarget) {\n        return this._show(relatedTarget);\n    };\n    KTDrawer.prototype.hide = function () {\n        return this._hide();\n    };\n    KTDrawer.prototype.update = function () {\n        return this._update();\n    };\n    KTDrawer.prototype.getRelatedTarget = function () {\n        return this._relatedTarget;\n    };\n    KTDrawer.prototype.isOpen = function () {\n        return this._isOpen;\n    };\n    KTDrawer.prototype.isEnabled = function () {\n        return this._isEnabled();\n    };\n    KTDrawer.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'drawer')) {\n            return data_1.default.get(element, 'drawer');\n        }\n        if (element.getAttribute('data-drawer') === \"true\") {\n            return new KTDrawer(element);\n        }\n        return null;\n    };\n    KTDrawer.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTDrawer(element, config);\n    };\n    KTDrawer.hide = function () {\n        var elements = document.querySelectorAll('[data-drawer]');\n        elements.forEach(function (element) {\n            var drawer = KTDrawer.getInstance(element);\n            if (drawer && drawer.isOpen()) {\n                drawer.hide();\n            }\n        });\n    };\n    KTDrawer.handleResize = function () {\n        window.addEventListener('resize', function () {\n            var timer;\n            utils_1.default.throttle(timer, function () {\n                document.querySelectorAll('[data-drawer]').forEach(function (element) {\n                    var drawer = KTDrawer.getInstance(element);\n                    drawer.update();\n                    if (drawer && drawer.isOpen() && !drawer.isEnabled()) {\n                        drawer.hide();\n                    }\n                });\n            }, 200);\n        });\n    };\n    KTDrawer.handleToggle = function () {\n        event_handler_1.default.on(document.body, '[data-drawer-toggle]', 'click', function (event, target) {\n            event.stopPropagation();\n            var selector = target.getAttribute(\"data-drawer-toggle\");\n            if (!selector)\n                return;\n            var drawerElement = document.querySelector(selector);\n            var drawer = KTDrawer.getInstance(drawerElement);\n            if (drawer) {\n                drawer.toggle();\n            }\n        });\n    };\n    KTDrawer.handleDismiss = function () {\n        event_handler_1.default.on(document.body, '[data-drawer-dismiss]', 'click', function (event, target) {\n            event.stopPropagation();\n            var modalElement = target.closest('[data-drawer=\"true\"]');\n            if (modalElement) {\n                var modal = KTDrawer.getInstance(modalElement);\n                if (modal) {\n                    modal.hide();\n                }\n            }\n        });\n    };\n    KTDrawer.handleClickAway = function () {\n        document.addEventListener('click', function (event) {\n            var drawerElement = document.querySelector('.open[data-drawer]');\n            if (!drawerElement)\n                return;\n            var drawer = KTDrawer.getInstance(drawerElement);\n            if (!drawer)\n                return;\n            if (drawer.getOption('persistent'))\n                return;\n            if (drawer.getOption('backdrop'))\n                return;\n            if (drawerElement !== event.target &&\n                drawer.getRelatedTarget() !== event.target &&\n                drawerElement.contains(event.target) === false) {\n                drawer.hide();\n            }\n        });\n    };\n    KTDrawer.handleKeyword = function () {\n        document.addEventListener('keydown', function (event) {\n            var drawerElement = document.querySelector('.open[data-drawer]');\n            var drawer = KTDrawer.getInstance(drawerElement);\n            if (!drawer) {\n                return;\n            }\n            // if esc key was not pressed in combination with ctrl or alt or shift\n            if (event.key === 'Escape' && !(event.ctrlKey || event.altKey || event.shiftKey)) {\n                drawer.hide();\n            }\n            if (event.code === 'Tab' && !event.metaKey) {\n                return;\n            }\n        });\n    };\n    KTDrawer.createInstances = function () {\n        var elements = document.querySelectorAll('[data-drawer=\"true\"]');\n        elements.forEach(function (element) {\n            new KTDrawer(element);\n        });\n    };\n    KTDrawer.init = function () {\n        KTDrawer.createInstances();\n        if (window.KT_DRAWER_INITIALIZED !== true) {\n            KTDrawer.handleToggle();\n            KTDrawer.handleDismiss();\n            KTDrawer.handleResize();\n            KTDrawer.handleClickAway();\n            KTDrawer.handleKeyword();\n            window.KT_DRAWER_INITIALIZED = true;\n        }\n    };\n    return KTDrawer;\n}(component_1.default));\nexports.KTDrawer = KTDrawer;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/drawer/drawer.ts?");

/***/ }),

/***/ "./src/core/components/drawer/index.ts":
/*!*********************************************!*\
  !*** ./src/core/components/drawer/index.ts ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDrawer = void 0;\nvar drawer_1 = __webpack_require__(/*! ./drawer */ \"./src/core/components/drawer/drawer.ts\");\nObject.defineProperty(exports, \"KTDrawer\", ({ enumerable: true, get: function () { return drawer_1.KTDrawer; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/drawer/index.ts?");

/***/ }),

/***/ "./src/core/components/dropdown/dropdown.ts":
/*!**************************************************!*\
  !*** ./src/core/components/dropdown/dropdown.ts ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDropdown = void 0;\nvar core_1 = __webpack_require__(/*! @popperjs/core */ \"./node_modules/@popperjs/core/lib/index.js\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar menu_1 = __webpack_require__(/*! ../menu */ \"./src/core/components/menu/index.ts\");\nvar KTDropdown = /** @class */ (function (_super) {\n    __extends(KTDropdown, _super);\n    function KTDropdown(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'dropdown';\n        _this._defaultConfig = {\n            zindex: 105,\n            hoverTimeout: 200,\n            placement: 'bottom-start',\n            placementRtl: 'bottom-end',\n            permanent: false,\n            dismiss: false,\n            trigger: 'click',\n            attach: '',\n            offset: '0px, 5px',\n            offsetRtl: '0px, 5px',\n            hiddenClass: 'hidden'\n        };\n        _this._config = _this._defaultConfig;\n        _this._disabled = false;\n        _this._isTransitioning = false;\n        _this._isOpen = false;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._toggleElement = _this._element.querySelector('.dropdown-toggle, [data-dropdown-toggle]');\n        if (!_this._toggleElement)\n            return _this;\n        _this._contentElement = _this._element.querySelector('.dropdown-content, [data-dropdown-content]');\n        if (!_this._contentElement)\n            return _this;\n        data_1.default.set(_this._contentElement, 'dropdownElement', _this._element);\n        return _this;\n    }\n    KTDropdown.prototype._click = function (event) {\n        event.preventDefault();\n        event.stopPropagation();\n        if (this._disabled === true) {\n            return;\n        }\n        if (this._getOption('trigger') !== 'click') {\n            return;\n        }\n        this._toggle();\n    };\n    KTDropdown.prototype._mouseover = function () {\n        if (this._disabled === true) {\n            return;\n        }\n        if (this._getOption('trigger') !== 'hover') {\n            return;\n        }\n        if (data_1.default.get(this._element, 'hover') === '1') {\n            clearTimeout(data_1.default.get(this._element, 'timeout'));\n            data_1.default.remove(this._element, 'hover');\n            data_1.default.remove(this._element, 'timeout');\n        }\n        this._show();\n    };\n    KTDropdown.prototype._mouseout = function () {\n        var _this = this;\n        if (this._disabled === true) {\n            return;\n        }\n        if (this._getOption('trigger') !== 'hover') {\n            return;\n        }\n        var timeout = setTimeout(function () {\n            if (data_1.default.get(_this._element, 'hover') === '1') {\n                _this._hide();\n            }\n        }, parseInt(this._getOption('hoverTimeout')));\n        data_1.default.set(this._element, 'hover', '1');\n        data_1.default.set(this._element, 'timeout', timeout);\n    };\n    KTDropdown.prototype._toggle = function () {\n        if (this._isOpen) {\n            this._hide();\n        }\n        else {\n            this._show();\n        }\n    };\n    KTDropdown.prototype._show = function () {\n        var _this = this;\n        if (this._isOpen || this._isTransitioning) {\n            return;\n        }\n        var payload = { cancel: false };\n        this._fireEvent('show', payload);\n        this._dispatchEvent('show', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        // Hide all currently shown dropdowns except current one\n        KTDropdown.hide();\n        menu_1.KTMenu.hide(this._element);\n        var zIndex = parseInt(this._getOption('zindex'));\n        var parentZindex = dom_1.default.getHighestZindex(this._element);\n        if (parentZindex !== null && parentZindex >= zIndex) {\n            zIndex = parentZindex + 1;\n        }\n        if (zIndex > 0) {\n            this._contentElement.style.zIndex = zIndex.toString();\n        }\n        this._contentElement.style.display = 'block';\n        this._contentElement.style.opacity = '0';\n        dom_1.default.reflow(this._contentElement);\n        this._contentElement.style.opacity = '1';\n        this._contentElement.classList.remove(this._getOption('hiddenClass'));\n        this._toggleElement.classList.add('active');\n        this._contentElement.classList.add('open');\n        this._element.classList.add('open');\n        this._initPopper();\n        dom_1.default.transitionEnd(this._contentElement, function () {\n            _this._isTransitioning = false;\n            _this._isOpen = true;\n            _this._fireEvent('shown');\n            _this._dispatchEvent('shown');\n        });\n    };\n    KTDropdown.prototype._hide = function () {\n        var _this = this;\n        if (this._isOpen === false || this._isTransitioning) {\n            return;\n        }\n        var payload = { cancel: false };\n        this._fireEvent('hide', payload);\n        this._dispatchEvent('hide', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        this._contentElement.style.opacity = '1';\n        dom_1.default.reflow(this._contentElement);\n        this._contentElement.style.opacity = '0';\n        this._contentElement.classList.remove('open');\n        this._toggleElement.classList.remove('active');\n        this._element.classList.remove('open');\n        dom_1.default.transitionEnd(this._contentElement, function () {\n            _this._isTransitioning = false;\n            _this._isOpen = false;\n            _this._contentElement.classList.add(_this._getOption('hiddenClass'));\n            _this._contentElement.style.display = '';\n            _this._contentElement.style.zIndex = '';\n            // Destroy popper(new)\n            _this._destroyPopper();\n            // Handle dropdown hidden event\n            _this._fireEvent('hidden');\n            _this._dispatchEvent('hidden');\n        });\n    };\n    KTDropdown.prototype._initPopper = function () {\n        var isRtl = dom_1.default.isRTL();\n        // Setup popper instance\n        var reference;\n        var attach = this._getOption('attach');\n        if (attach) {\n            if (attach === 'parent') {\n                reference = this._toggleElement.parentNode;\n            }\n            else {\n                reference = document.querySelector(attach);\n            }\n        }\n        else {\n            reference = this._toggleElement;\n        }\n        if (reference) {\n            var popper = (0, core_1.createPopper)(reference, this._contentElement, this._getPopperConfig());\n            data_1.default.set(this._element, 'popper', popper);\n        }\n    };\n    KTDropdown.prototype._destroyPopper = function () {\n        if (data_1.default.has(this._element, 'popper')) {\n            data_1.default.get(this._element, 'popper').destroy();\n            data_1.default.remove(this._element, 'popper');\n        }\n    };\n    KTDropdown.prototype.__isOpen = function () {\n        return this._element.classList.contains('open') && this._contentElement.classList.contains('open');\n    };\n    KTDropdown.prototype._getPopperConfig = function () {\n        var isRtl = dom_1.default.isRTL();\n        // Placement\n        var placement = this._getOption('placement');\n        if (isRtl && this._getOption('placementRtl')) {\n            placement = this._getOption('placementRtl');\n        }\n        // Offset\n        var offsetValue = this._getOption('offset');\n        if (isRtl && this._getOption('offsetRtl')) {\n            offsetValue = this._getOption('offsetRtl');\n        }\n        var offset = offsetValue ? offsetValue.toString().split(',').map(function (value) { return parseInt(value.trim(), 10); }) : [0, 0];\n        // Strategy\n        var strategy = this._getOption('overflow') === true ? 'absolute' : 'fixed';\n        var altAxis = this._getOption('flip') !== false ? true : false;\n        var popperConfig = {\n            placement: placement,\n            strategy: strategy,\n            modifiers: [\n                {\n                    name: 'offset',\n                    options: {\n                        offset: offset\n                    }\n                },\n                {\n                    name: 'preventOverflow',\n                    options: {\n                        altAxis: altAxis\n                    }\n                },\n                {\n                    name: 'flip',\n                    options: {\n                        flipVariations: false\n                    }\n                }\n            ]\n        };\n        return popperConfig;\n    };\n    KTDropdown.prototype._getToggleElement = function () {\n        return this._toggleElement;\n    };\n    KTDropdown.prototype._getContentElement = function () {\n        return this._contentElement;\n    };\n    // General Methods\n    KTDropdown.prototype.click = function (event) {\n        this._click(event);\n    };\n    KTDropdown.prototype.mouseover = function () {\n        this._mouseover();\n    };\n    KTDropdown.prototype.mouseout = function () {\n        this._mouseout();\n    };\n    KTDropdown.prototype.show = function () {\n        return this._show();\n    };\n    KTDropdown.prototype.hide = function () {\n        this._hide();\n    };\n    KTDropdown.prototype.toggle = function () {\n        this._toggle();\n    };\n    KTDropdown.prototype.getToggleElement = function () {\n        return this._toggleElement;\n    };\n    KTDropdown.prototype.getContentElement = function () {\n        return this._contentElement;\n    };\n    KTDropdown.prototype.isPermanent = function () {\n        return this._getOption('permanent');\n    };\n    KTDropdown.prototype.disable = function () {\n        this._disabled = true;\n    };\n    KTDropdown.prototype.enable = function () {\n        this._disabled = false;\n    };\n    KTDropdown.prototype.isOpen = function () {\n        return this._isOpen;\n    };\n    // Statics methods\n    KTDropdown.getElement = function (reference) {\n        if (reference.hasAttribute('data-dropdown'))\n            return reference;\n        var findElement = reference.closest('[data-dropdown]');\n        if (findElement)\n            return findElement;\n        if ((reference.classList.contains('dropdown-content') || reference.hasAttribute(\"data-dropdown-content\")) && data_1.default.has(reference, 'dropdownElement')) {\n            return data_1.default.get(reference, 'dropdownElement');\n        }\n        return null;\n    };\n    KTDropdown.getInstance = function (element) {\n        element = this.getElement(element);\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'dropdown')) {\n            return data_1.default.get(element, 'dropdown');\n        }\n        if (element.getAttribute('data-dropdown') === \"true\") {\n            return new KTDropdown(element);\n        }\n        return null;\n    };\n    KTDropdown.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTDropdown(element, config);\n    };\n    KTDropdown.update = function () {\n        document.querySelectorAll('.open[data-dropdown]').forEach(function (item) {\n            if (data_1.default.has(item, 'popper')) {\n                data_1.default.get(item, 'popper').forceUpdate();\n            }\n        });\n    };\n    KTDropdown.hide = function (skipElement) {\n        document.querySelectorAll('.open[data-dropdown]').forEach(function (item) {\n            if (skipElement && (skipElement === item || item.contains(skipElement)))\n                return;\n            var dropdown = KTDropdown.getInstance(item);\n            if (dropdown)\n                dropdown.hide();\n        });\n    };\n    KTDropdown.handleClickAway = function () {\n        document.addEventListener('click', function (event) {\n            document.querySelectorAll('.open[data-dropdown]').forEach(function (element) {\n                var dropdown = KTDropdown.getInstance(element);\n                if (dropdown && dropdown.isPermanent() === false) {\n                    var contentElement = dropdown.getContentElement();\n                    if (element === event.target || element.contains(event.target)) {\n                        return;\n                    }\n                    if (contentElement && (contentElement === event.target || contentElement.contains(event.target))) {\n                        return;\n                    }\n                    dropdown.hide();\n                }\n            });\n        });\n    };\n    KTDropdown.handleKeyboard = function () {\n    };\n    KTDropdown.handleMouseover = function () {\n        event_handler_1.default.on(document.body, '.dropdown-toggle, [data-dropdown-toggle]', 'mouseover', function (event, target) {\n            var dropdown = KTDropdown.getInstance(target);\n            if (dropdown !== null && dropdown.getOption('trigger') === 'hover') {\n                return dropdown.mouseover();\n            }\n        });\n    };\n    KTDropdown.handleMouseout = function () {\n        event_handler_1.default.on(document.body, '.dropdown-toggle, [data-dropdown-toggle]', 'mouseout', function (event, target) {\n            var dropdown = KTDropdown.getInstance(target);\n            if (dropdown !== null && dropdown.getOption('trigger') === 'hover') {\n                return dropdown.mouseout();\n            }\n        });\n    };\n    KTDropdown.handleClick = function () {\n        event_handler_1.default.on(document.body, '.dropdown-toggle, [data-dropdown-toggle]', 'click', function (event, target) {\n            var dropdown = KTDropdown.getInstance(target);\n            if (dropdown) {\n                return dropdown.click(event);\n            }\n        });\n    };\n    KTDropdown.handleDismiss = function () {\n        event_handler_1.default.on(document.body, '[data-dropdown-dismiss]', 'click', function (event, target) {\n            var dropdown = KTDropdown.getInstance(target);\n            if (dropdown) {\n                return dropdown.hide();\n            }\n        });\n    };\n    KTDropdown.initHandlers = function () {\n        this.handleClickAway();\n        this.handleKeyboard();\n        this.handleMouseover();\n        this.handleMouseout();\n        this.handleClick();\n        this.handleDismiss();\n    };\n    KTDropdown.createInstances = function () {\n        var elements = document.querySelectorAll('[data-dropdown]:not([data-dropdown=false])');\n        elements.forEach(function (element) {\n            new KTDropdown(element);\n        });\n    };\n    KTDropdown.init = function () {\n        KTDropdown.createInstances();\n        if (window.KT_DROPDOWN_INITIALIZED !== true) {\n            KTDropdown.initHandlers();\n            window.KT_DROPDOWN_INITIALIZED = true;\n        }\n    };\n    return KTDropdown;\n}(component_1.default));\nexports.KTDropdown = KTDropdown;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/dropdown/dropdown.ts?");

/***/ }),

/***/ "./src/core/components/dropdown/index.ts":
/*!***********************************************!*\
  !*** ./src/core/components/dropdown/index.ts ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDropdown = void 0;\nvar dropdown_1 = __webpack_require__(/*! ./dropdown */ \"./src/core/components/dropdown/dropdown.ts\");\nObject.defineProperty(exports, \"KTDropdown\", ({ enumerable: true, get: function () { return dropdown_1.KTDropdown; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/dropdown/index.ts?");

/***/ }),

/***/ "./src/core/components/image-input/image-input.ts":
/*!********************************************************!*\
  !*** ./src/core/components/image-input/image-input.ts ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTImageInput = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTImageInput = /** @class */ (function (_super) {\n    __extends(KTImageInput, _super);\n    function KTImageInput(element, config) {\n        if (config === void 0) { config = null; }\n        var _this = _super.call(this) || this;\n        _this._name = 'image-input';\n        _this._defaultConfig = {\n            hiddenClass: 'hidden'\n        };\n        _this._previewUrl = '';\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._inputElement = _this._element.querySelector('input[type=\"file\"]');\n        _this._hiddenElement = _this._element.querySelector('input[type=\"hidden\"]');\n        _this._removeElement = _this._element.querySelector('[data-image-input-remove]');\n        _this._previewElement = _this._element.querySelector('.image-input-preview');\n        _this._update();\n        _this._handlers();\n        return _this;\n    }\n    KTImageInput.prototype._handlers = function () {\n        var _this = this;\n        event_handler_1.default.on(this._element, '.image-input-placeholder', 'click', function (event) {\n            event.preventDefault();\n            _this._inputElement.click();\n        });\n        this._inputElement.addEventListener('change', function () {\n            _this._change();\n        });\n        this._removeElement.addEventListener('click', function () {\n            _this._remove();\n        });\n    };\n    KTImageInput.prototype._change = function () {\n        var _this = this;\n        var payload = { cancel: false };\n        this._fireEvent('change', payload);\n        this._dispatchEvent('change', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        var reader = new FileReader();\n        reader.onload = function () {\n            _this._previewElement.style.backgroundImage = \"url(\".concat(reader.result, \")\");\n        };\n        reader.readAsDataURL(this._inputElement.files[0]);\n        this._inputElement.value = \"\";\n        this._hiddenElement.value = \"\";\n        this._lastMode = 'new';\n        this._element.classList.add('changed');\n        this._removeElement.classList.remove('hidden');\n        this._element.classList.remove('empty');\n        this._fireEvent('changed');\n        this._dispatchEvent('changed');\n    };\n    KTImageInput.prototype._remove = function () {\n        var payload = { cancel: false };\n        this._fireEvent('remove', payload);\n        this._dispatchEvent('remove', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        this._element.classList.remove('empty');\n        this._element.classList.remove('changed');\n        if (this._lastMode == 'new') {\n            if (this._previewUrl == '')\n                this._removeElement.classList.add(this._getOption('hiddenClass'));\n            if (this._previewUrl) {\n                this._previewElement.style.backgroundImage = \"url(\".concat(this._previewUrl, \")\");\n            }\n            else {\n                this._previewElement.style.backgroundImage = 'none';\n                this._element.classList.add('empty');\n            }\n            this._inputElement.value = \"\";\n            this._hiddenElement.value = \"\";\n            this._lastMode = 'saved';\n        }\n        else if (this._lastMode == 'saved') {\n            if (this._previewUrl == '')\n                this._removeElement.classList.add(this._getOption('hiddenClass'));\n            this._previewElement.style.backgroundImage = 'none';\n            this._element.classList.add('empty');\n            this._hiddenElement.value = \"1\";\n            this._inputElement.value = \"\";\n            this._lastMode = 'placeholder';\n        }\n        else if (this._lastMode == 'placeholder') {\n            if (this._previewUrl == '')\n                this._removeElement.classList.add(this._getOption('hiddenClass'));\n            if (this._previewUrl) {\n                this._previewElement.style.backgroundImage = \"url(\".concat(this._previewUrl, \")\");\n            }\n            else {\n                this._element.classList.add('empty');\n            }\n            this._inputElement.value = \"\";\n            this._hiddenElement.value = \"\";\n            this._lastMode = 'saved';\n        }\n        this._fireEvent('remove');\n        this._dispatchEvent('remove');\n    };\n    KTImageInput.prototype._update = function () {\n        if (this._previewElement.style.backgroundImage) {\n            this._setPreviewUrl(this._previewElement.style.backgroundImage);\n            this._removeElement.classList.remove(this._getOption('hiddenClass'));\n            this._lastMode = 'saved';\n        }\n        else {\n            this._removeElement.classList.add(this._getOption('hiddenClass'));\n            this._element.classList.add('empty');\n            this._lastMode = 'placeholder';\n        }\n    };\n    KTImageInput.prototype._getPreviewUrl = function () {\n        return this._previewUrl;\n    };\n    KTImageInput.prototype._setPreviewUrl = function (url) {\n        this._previewUrl = url.replace(/(url\\(|\\)|\")/g, '');\n    };\n    KTImageInput.prototype.isEmpty = function () {\n        return this._inputElement.value.length === 0;\n    };\n    KTImageInput.prototype.isChanged = function () {\n        return this._inputElement.value.length > 0;\n    };\n    KTImageInput.prototype.remove = function () {\n        this._remove();\n    };\n    KTImageInput.prototype.update = function () {\n        this._update();\n    };\n    KTImageInput.prototype.setPreviewUrl = function (url) {\n        this._setPreviewUrl(url);\n    };\n    KTImageInput.prototype.getPreviewUrl = function () {\n        return this._getPreviewUrl();\n    };\n    KTImageInput.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'image-input')) {\n            return data_1.default.get(element, 'image-input');\n        }\n        if (element.getAttribute('data-image-input') === \"true\") {\n            return new KTImageInput(element);\n        }\n        return null;\n    };\n    KTImageInput.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTImageInput(element, config);\n    };\n    KTImageInput.createInstances = function () {\n        var elements = document.querySelectorAll('[data-image-input=\"true\"]');\n        elements.forEach(function (element) {\n            new KTImageInput(element);\n        });\n    };\n    KTImageInput.init = function () {\n        KTImageInput.createInstances();\n    };\n    return KTImageInput;\n}(component_1.default));\nexports.KTImageInput = KTImageInput;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/image-input/image-input.ts?");

/***/ }),

/***/ "./src/core/components/image-input/index.ts":
/*!**************************************************!*\
  !*** ./src/core/components/image-input/index.ts ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTImageInput = void 0;\nvar image_input_1 = __webpack_require__(/*! ./image-input */ \"./src/core/components/image-input/image-input.ts\");\nObject.defineProperty(exports, \"KTImageInput\", ({ enumerable: true, get: function () { return image_input_1.KTImageInput; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/image-input/index.ts?");

/***/ }),

/***/ "./src/core/components/menu/index.ts":
/*!*******************************************!*\
  !*** ./src/core/components/menu/index.ts ***!
  \*******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTMenu = void 0;\nvar menu_1 = __webpack_require__(/*! ./menu */ \"./src/core/components/menu/menu.ts\");\nObject.defineProperty(exports, \"KTMenu\", ({ enumerable: true, get: function () { return menu_1.KTMenu; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/menu/index.ts?");

/***/ }),

/***/ "./src/core/components/menu/menu.ts":
/*!******************************************!*\
  !*** ./src/core/components/menu/menu.ts ***!
  \******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTMenu = void 0;\nvar core_1 = __webpack_require__(/*! @popperjs/core */ \"./node_modules/@popperjs/core/lib/index.js\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar utils_1 = __webpack_require__(/*! ../../helpers/utils */ \"./src/core/helpers/utils.ts\");\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar constants_1 = __webpack_require__(/*! ../constants */ \"./src/core/components/constants.ts\");\nvar dropdown_1 = __webpack_require__(/*! ../dropdown */ \"./src/core/components/dropdown/index.ts\");\nvar KTMenu = /** @class */ (function (_super) {\n    __extends(KTMenu, _super);\n    function KTMenu(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'menu';\n        _this._defaultConfig = {\n            dropdownZindex: '105',\n            dropdownHoverTimeout: 200,\n            dropdownPlacement: 'bottom',\n            dropdownOffset: '0, 5px',\n            accordionExpandAll: false\n        };\n        _this._config = _this._defaultConfig;\n        _this._disabled = false;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._update();\n        return _this;\n    }\n    KTMenu.prototype._click = function (element, event) {\n        if (element.hasAttribute('href') && element.getAttribute('href') !== '#') {\n            return;\n        }\n        event.preventDefault();\n        event.stopPropagation();\n        if (this._disabled === true) {\n            return;\n        }\n        var itemElement = this._getItemElement(element);\n        if (!itemElement)\n            return;\n        if (this._getItemOption(itemElement, 'trigger') !== 'click') {\n            return;\n        }\n        if (this._getItemOption(itemElement, 'toggle') === false) {\n            this._show(itemElement);\n        }\n        else {\n            this._toggle(itemElement);\n        }\n    };\n    KTMenu.prototype._link = function (element, event) {\n        if (this._disabled === true) {\n            return;\n        }\n        var payload = {\n            cancel: false,\n            element: element,\n            event: event\n        };\n        this._fireEvent('link.click', payload);\n        this._dispatchEvent('link.click', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        var itemElement = this._getItemElement(element);\n        if (this._isItemDropdownPermanent(itemElement) === false) {\n            KTMenu.hide();\n        }\n        payload = {\n            element: element,\n            event: event\n        };\n        this._fireEvent('link.clicked', payload);\n        this._dispatchEvent('link.clicked', payload);\n    };\n    KTMenu.prototype._dismiss = function (element) {\n        var _this = this;\n        var itemElement = this._getItemElement(element);\n        if (!itemElement)\n            return;\n        var itemElements = this._getItemChildElements(itemElement);\n        if (itemElement !== null &&\n            this._getItemToggleMode(itemElement) === 'dropdown') {\n            // hide items dropdown\n            this._hide(itemElement);\n            // Hide all child elements as well\n            itemElements.forEach(function (each) {\n                if (_this._getItemToggleMode(each) === 'dropdown') {\n                    _this._hide(each);\n                }\n            });\n        }\n    };\n    KTMenu.prototype._mouseover = function (element) {\n        var itemElement = this._getItemElement(element);\n        if (!itemElement)\n            return;\n        if (this._disabled === true) {\n            return;\n        }\n        if (itemElement === null) {\n            return;\n        }\n        if (this._getItemOption(itemElement, 'trigger') !== 'hover') {\n            return;\n        }\n        if (data_1.default.get(itemElement, 'hover') === '1') {\n            clearTimeout(data_1.default.get(itemElement, 'timeout'));\n            data_1.default.remove(itemElement, 'hover');\n            data_1.default.remove(itemElement, 'timeout');\n        }\n        this._show(itemElement);\n    };\n    KTMenu.prototype._mouseout = function (element) {\n        var _this = this;\n        var itemElement = this._getItemElement(element);\n        if (!itemElement)\n            return;\n        if (this._disabled === true) {\n            return;\n        }\n        if (this._getItemOption(itemElement, 'trigger') !== 'hover') {\n            return;\n        }\n        var timeout = setTimeout(function () {\n            if (data_1.default.get(itemElement, 'hover') === '1') {\n                _this._hide(itemElement);\n            }\n        }, parseInt(this._getOption('dropdownHoverTimeout')));\n        data_1.default.set(itemElement, 'hover', '1');\n        data_1.default.set(itemElement, 'timeout', timeout);\n    };\n    KTMenu.prototype._toggle = function (itemElement) {\n        if (this._isItemSubShown(itemElement) === true) {\n            this._hide(itemElement);\n        }\n        else {\n            this._show(itemElement);\n        }\n    };\n    KTMenu.prototype._show = function (itemElement) {\n        if (this._isItemSubShown(itemElement) === true) {\n            return;\n        }\n        if (this._getItemToggleMode(itemElement) === 'dropdown') {\n            this._showDropdown(itemElement);\n        }\n        else if (this._getItemToggleMode(itemElement) === 'accordion') {\n            this._showAccordion(itemElement);\n        }\n        // Remember last submenu type\n        data_1.default.set(itemElement, 'toggle', this._getItemToggleMode(itemElement));\n    };\n    KTMenu.prototype._hide = function (itemElement) {\n        if (this._isItemSubShown(itemElement) === false) {\n            return;\n        }\n        if (this._getItemToggleMode(itemElement) === 'dropdown') {\n            this._hideDropdown(itemElement);\n        }\n        else if (this._getItemToggleMode(itemElement) === 'accordion') {\n            this._hideAccordion(itemElement);\n        }\n    };\n    KTMenu.prototype._reset = function (itemElement) {\n        if (this._hasItemSub(itemElement) === false) {\n            return;\n        }\n        var subElement = this._getItemSubElement(itemElement);\n        // Reset sub state if sub type is changed during the window resize\n        if (data_1.default.has(itemElement, 'toggle') &&\n            data_1.default.get(itemElement, 'toggle') !== this._getItemToggleMode(itemElement)) {\n            itemElement.classList.remove('show');\n            subElement === null || subElement === void 0 ? void 0 : subElement.classList.remove('show');\n        }\n    };\n    KTMenu.prototype._update = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        var itemElements = this._element.querySelectorAll('[data-menu-item-trigger]');\n        itemElements.forEach(function (itemElement) {\n            _this._updateItemSubType(itemElement);\n            _this._reset(itemElement);\n        });\n    };\n    KTMenu.prototype._updateItemSubType = function (itemElement) {\n        var subElement = this._getItemSubElement(itemElement);\n        if (subElement) {\n            if (this._getItemToggleMode(itemElement) === 'dropdown') {\n                itemElement.classList.remove('menu-item-accordion');\n                itemElement.classList.add('menu-item-dropdown');\n                subElement.classList.remove('menu-accordion');\n                subElement.classList.add('menu-dropdown');\n            }\n            else {\n                itemElement.classList.remove('menu-item-dropdown');\n                itemElement.classList.add('menu-item-accordion');\n                subElement.classList.remove('menu-dropdown');\n                subElement.classList.add('menu-accordion');\n            }\n        }\n    };\n    KTMenu.prototype._isItemSubShown = function (itemElement) {\n        var subElement = this._getItemSubElement(itemElement);\n        if (subElement !== null) {\n            if (this._getItemToggleMode(itemElement) === 'dropdown') {\n                if (subElement.classList.contains('show') === true &&\n                    subElement.hasAttribute('data-popper-placement') === true) {\n                    return true;\n                }\n                else {\n                    return false;\n                }\n            }\n            else {\n                return itemElement.classList.contains('show');\n            }\n        }\n        else {\n            return false;\n        }\n    };\n    KTMenu.prototype._isItemDropdownPermanent = function (itemElement) {\n        return this._getItemOption(itemElement, 'permanent');\n    };\n    KTMenu.prototype._isItemParentShown = function (itemElement) {\n        var parents = dom_1.default.parents(itemElement, '.menu-item.show');\n        return parents && parents.length > 0 ? true : false;\n    };\n    KTMenu.prototype._isItemSubElement = function (itemElement) {\n        return itemElement.classList.contains('menu-dropdown') || itemElement.classList.contains('menu-accordion');\n    };\n    KTMenu.prototype._hasItemSub = function (itemElement) {\n        return (itemElement.classList.contains('menu-item') &&\n            itemElement.hasAttribute('data-menu-item-trigger'));\n    };\n    KTMenu.prototype._getItemLinkElement = function (itemElement) {\n        return dom_1.default.child(itemElement, '.menu-link, .menu-toggle');\n    };\n    KTMenu.prototype._getItemSubElement = function (itemElement) {\n        if (itemElement.classList.contains('menu-dropdown') === true || itemElement.classList.contains('menu-accordion') === true) {\n            return itemElement;\n        }\n        else if (data_1.default.has(itemElement, 'sub')) {\n            return data_1.default.get(itemElement, 'sub');\n        }\n        else {\n            return dom_1.default.child(itemElement, '.menu-dropdown, .menu-accordion');\n        }\n    };\n    KTMenu.prototype._getItemToggleMode = function (itemElement) {\n        var itemEl = this._getItemElement(itemElement);\n        if (this._getItemOption(itemEl, 'toggle') === 'dropdown') {\n            return 'dropdown';\n        }\n        else {\n            return 'accordion';\n        }\n    };\n    KTMenu.prototype._getItemElement = function (element) {\n        if (element.classList.contains('menu-item') && element.hasAttribute('data-menu-item-toggle')) {\n            return element;\n        }\n        // Element has item DOM reference in it's data storage\n        if (data_1.default.has(element, 'item')) {\n            return data_1.default.get(element, 'item');\n        }\n        // Item is parent of element\n        var itemElement = element.closest('.menu-item[data-menu-item-toggle]');\n        if (itemElement) {\n            return itemElement;\n        }\n        // Element's parent has item DOM reference in it's data storage\n        var subElement = element.closest('.menu-dropdown, .menu-accordion');\n        if (subElement) {\n            if (data_1.default.has(subElement, 'item') === true) {\n                return data_1.default.get(subElement, 'item');\n            }\n        }\n        return null;\n    };\n    KTMenu.prototype._getItemParentElement = function (itemElement) {\n        var subElement = itemElement.closest('.menu-dropdown, .menu-accordion');\n        var parentItem;\n        if (subElement && data_1.default.has(subElement, 'item')) {\n            return data_1.default.get(subElement, 'item');\n        }\n        if (subElement &&\n            (parentItem = subElement.closest('.menu-item[data-menu-item-trigger]'))) {\n            return parentItem;\n        }\n        return null;\n    };\n    KTMenu.prototype._getItemParentElements = function (itemElement) {\n        var parentElements = [];\n        var parentElement;\n        var i = 0;\n        do {\n            parentElement = this._getItemParentElement(itemElement);\n            if (parentElement) {\n                parentElements.push(parentElement);\n                itemElement = parentElement;\n            }\n            i++;\n        } while (parent !== null && i < 20);\n        return parentElements;\n    };\n    KTMenu.prototype._getItemChildElement = function (itemElement) {\n        var selector = itemElement;\n        var element;\n        if (data_1.default.has(itemElement, 'sub')) {\n            selector = data_1.default.get(itemElement, 'sub');\n        }\n        if (selector !== null) {\n            //element = selector.querySelector('.show.menu-item[data-menu-trigger]');\n            element = selector.querySelector('.menu-item[data-menu-item-trigger]');\n            if (element) {\n                return element;\n            }\n            else {\n                return null;\n            }\n        }\n        else {\n            return null;\n        }\n    };\n    KTMenu.prototype._getItemChildElements = function (itemElement) {\n        var children = [];\n        var child;\n        var i = 0;\n        var buffer = itemElement;\n        do {\n            child = this._getItemChildElement(buffer);\n            if (child) {\n                children.push(child);\n                buffer = child;\n            }\n            i++;\n        } while (child !== null && i < 20);\n        return children;\n    };\n    KTMenu.prototype._showDropdown = function (itemElement) {\n        var payload = { cancel: false };\n        this._fireEvent('dropdown.show', payload);\n        this._dispatchEvent('dropdown.show', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        // Hide all currently shown dropdowns except current one\n        KTMenu.hide(itemElement);\n        dropdown_1.KTDropdown.hide(itemElement);\n        var subElement = this._getItemSubElement(itemElement);\n        if (!subElement)\n            return;\n        var width = this._getItemOption(itemElement, 'width');\n        var height = this._getItemOption(itemElement, 'height');\n        // Set z=index\n        var zindex = parseInt(this._getOption('dropdownZindex'));\n        if (parseInt(dom_1.default.getCssProp(subElement, 'z-index')) > zindex) {\n            zindex = parseInt(dom_1.default.getCssProp(subElement, 'z-index'));\n        }\n        if (dom_1.default.getHighestZindex(itemElement) > zindex) {\n            zindex = dom_1.default.getHighestZindex(itemElement) + 1;\n        }\n        subElement.style.zIndex = String(zindex);\n        // end\n        if (width) {\n            subElement.style.width = width;\n        }\n        if (height) {\n            subElement.style.height = height;\n        }\n        subElement.style.display = '';\n        subElement.style.overflow = '';\n        // Init popper(new)\n        this._initDropdownPopper(itemElement, subElement);\n        itemElement.classList.add('show');\n        itemElement.classList.add('menu-item-dropdown');\n        subElement.classList.add('show');\n        // Append the sub the the root of the menu\n        if (this._getItemOption(itemElement, 'overflow') === true) {\n            document.body.appendChild(subElement);\n            subElement.setAttribute('data-menu-sub-overflow', 'true');\n            data_1.default.set(itemElement, 'sub', subElement);\n            data_1.default.set(subElement, 'item', itemElement);\n            data_1.default.set(subElement, 'menu', this);\n        }\n        else {\n            data_1.default.set(subElement, 'item', itemElement);\n        }\n        // Handle dropdown shown event\n        this._fireEvent('dropdown.shown');\n        this._dispatchEvent('dropdown.shown');\n    };\n    KTMenu.prototype._hideDropdown = function (itemElement) {\n        var payload = { cancel: false };\n        this._fireEvent('dropdown.hide', payload);\n        this._dispatchEvent('dropdown.hide', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        var subElement = this._getItemSubElement(itemElement);\n        if (!subElement)\n            return;\n        subElement.style.zIndex = '';\n        subElement.style.width = '';\n        subElement.style.height = '';\n        itemElement.classList.remove('show');\n        itemElement.classList.remove('menu-item-dropdown');\n        subElement.classList.remove('show');\n        // Append the sub back to it's parent\n        if (this._getItemOption(itemElement, 'overflow') === true) {\n            subElement.removeAttribute('data-menu-sub-overflow');\n            if (itemElement.classList.contains('menu-item')) {\n                itemElement.appendChild(subElement);\n            }\n            else {\n                if (!this._element)\n                    return;\n                dom_1.default.insertAfter(this._element, itemElement);\n            }\n            data_1.default.remove(itemElement, 'sub');\n            data_1.default.remove(subElement, 'item');\n            data_1.default.remove(subElement, 'menu');\n        }\n        // Destroy popper(new)\n        this._destroyDropdownPopper(itemElement);\n        // Handle dropdown hidden event\n        this._fireEvent('dropdown.hidden');\n        this._dispatchEvent('dropdown.hidden');\n    };\n    KTMenu.prototype._initDropdownPopper = function (itemElement, subElement) {\n        // Setup popper instance\n        var reference;\n        var attach = this._getItemOption(itemElement, 'attach');\n        if (attach) {\n            if (attach === 'parent') {\n                reference = itemElement.parentNode;\n            }\n            else {\n                reference = document.querySelector(attach);\n            }\n        }\n        else {\n            reference = itemElement;\n        }\n        if (reference) {\n            var popper = (0, core_1.createPopper)(reference, subElement, this._getDropdownPopperConfig(itemElement));\n            data_1.default.set(itemElement, 'popper', popper);\n        }\n    };\n    KTMenu.prototype._destroyDropdownPopper = function (itemElement) {\n        if (data_1.default.has(itemElement, 'popper')) {\n            data_1.default.get(itemElement, 'popper').destroy();\n            data_1.default.remove(itemElement, 'popper');\n        }\n    };\n    KTMenu.prototype._getDropdownPopperConfig = function (itemElement) {\n        var isRtl = dom_1.default.isRTL();\n        // Placement\n        var placement = this._getOption('dropdownPlacement');\n        if (this._getItemOption(itemElement, 'placement')) {\n            placement = this._getItemOption(itemElement, 'placement');\n        }\n        if (isRtl && this._getItemOption(itemElement, 'placementRtl')) {\n            placement = this._getItemOption(itemElement, 'placementRtl');\n        }\n        // Offset\n        var offsetValue = this._getOption('dropdownOffset');\n        if (this._getItemOption(itemElement, 'offset')) {\n            offsetValue = this._getItemOption(itemElement, 'offset');\n        }\n        if (isRtl && this._getItemOption(itemElement, 'offsetRtl')) {\n            offsetValue = this._getItemOption(itemElement, 'offsetRtl');\n        }\n        var offset = offsetValue ? offsetValue.toString().split(',').map(function (value) { return parseInt(value.trim(), 10); }) : [0, 0];\n        // Strategy\n        var strategy = this._getItemOption(itemElement, 'overflow') === true ? 'absolute' : 'fixed';\n        var altAxis = this._getItemOption(itemElement, 'flip') !== false ? true : false;\n        var popperConfig = {\n            placement: placement,\n            strategy: strategy,\n            modifiers: [\n                {\n                    name: 'offset',\n                    options: {\n                        offset: offset\n                    }\n                },\n                {\n                    name: 'preventOverflow',\n                    options: {\n                        altAxis: altAxis\n                    }\n                },\n                {\n                    name: 'flip',\n                    options: {\n                        flipVariations: false\n                    }\n                }\n            ]\n        };\n        return popperConfig;\n    };\n    KTMenu.prototype._showAccordion = function (itemElement) {\n        var _this = this;\n        var payload = { cancel: false };\n        this._fireEvent('accordion.show', payload);\n        this._dispatchEvent('accordion.show', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        var subElement = this._getItemSubElement(itemElement);\n        if (!subElement)\n            return;\n        var expandAll = this._getOption('accordionExpandAll');\n        if (this._getItemOption(itemElement, 'expandAll') === true) {\n            expandAll = true;\n        }\n        else if (this._getItemOption(itemElement, 'expandAll') === false) {\n            expandAll = false;\n        }\n        else if (this._element && this._getItemOption(this._element, 'expandAll') === true) {\n            expandAll = true;\n        }\n        if (expandAll === false) {\n            this._hideAccordions(itemElement);\n        }\n        if (data_1.default.has(itemElement, 'popper') === true) {\n            this._hideDropdown(itemElement);\n        }\n        itemElement.classList.add('transitioning');\n        subElement.style.height = '0px';\n        dom_1.default.reflow(subElement);\n        subElement.style.display = 'flex';\n        subElement.style.overflow = 'hidden';\n        subElement.style.height = \"\".concat(subElement.scrollHeight, \"px\");\n        itemElement.classList.add('show');\n        dom_1.default.transitionEnd(subElement, function () {\n            itemElement.classList.remove('transitioning');\n            subElement.classList.add('show');\n            subElement.style.height = '';\n            subElement.style.display = '';\n            subElement.style.overflow = '';\n            // Handle accordion hidden event\n            _this._fireEvent('accordion.shown', payload);\n            _this._dispatchEvent('accordion.shown', payload);\n        });\n    };\n    KTMenu.prototype._hideAccordion = function (itemElement) {\n        var _this = this;\n        var payload = { cancel: false };\n        this._fireEvent('accordion.hide', payload);\n        this._dispatchEvent('accordion.hide', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        var subElement = this._getItemSubElement(itemElement);\n        if (!subElement)\n            return;\n        itemElement.classList.add('transitioning');\n        itemElement.classList.remove('show');\n        subElement.style.height = \"\".concat(subElement.scrollHeight, \"px\");\n        dom_1.default.reflow(subElement);\n        subElement.style.height = '0px';\n        subElement.style.overflow = 'hidden';\n        dom_1.default.transitionEnd(subElement, function () {\n            subElement.style.overflow = '';\n            itemElement.classList.remove('transitioning');\n            subElement.classList.remove('show');\n            // Handle accordion hidden event\n            _this._fireEvent('accordion.hidden');\n            _this._dispatchEvent('accordion.hidden');\n        });\n    };\n    KTMenu.prototype._setActiveLink = function (linkElement) {\n        var _this = this;\n        var itemElement = this._getItemElement(linkElement);\n        if (!itemElement)\n            return;\n        if (!this._element)\n            return;\n        var parentItems = this._getItemParentElements(itemElement);\n        var activeLinks = this._element.querySelectorAll('.menu-link.active');\n        var activeParentItems = this._element.querySelectorAll('.menu-item.here, .menu-item.show');\n        if (this._getItemToggleMode(itemElement) === 'accordion') {\n            this._showAccordion(itemElement);\n        }\n        else {\n            itemElement.classList.add('here');\n        }\n        parentItems === null || parentItems === void 0 ? void 0 : parentItems.forEach(function (parentItem) {\n            if (_this._getItemToggleMode(parentItem) === 'accordion') {\n                _this._showAccordion(parentItem);\n            }\n            else {\n                parentItem.classList.add('here');\n            }\n        });\n        activeLinks === null || activeLinks === void 0 ? void 0 : activeLinks.forEach(function (activeLink) {\n            activeLink.classList.remove('active');\n        });\n        activeParentItems === null || activeParentItems === void 0 ? void 0 : activeParentItems.forEach(function (activeParentItem) {\n            if (activeParentItem.contains(itemElement) === false) {\n                activeParentItem.classList.remove('here');\n                activeParentItem.classList.remove('show');\n            }\n        });\n        linkElement.classList.add('active');\n    };\n    KTMenu.prototype._getLinkByAttribute = function (value, name) {\n        if (name === void 0) { name = 'href'; }\n        if (!this._element)\n            return null;\n        var linkElement = this._element.querySelector(\"'.menu-link[\".concat(name, \"=\\\"\").concat(value, \"\\\"]\"));\n        return linkElement && null;\n    };\n    KTMenu.prototype._hideAccordions = function (itemElement) {\n        var _this = this;\n        if (!this._element)\n            return;\n        var itemsToHide = this._element.querySelectorAll('.show[data-menu-item-trigger]');\n        itemsToHide.forEach(function (itemToHide) {\n            if (_this._getItemToggleMode(itemToHide) === 'accordion' &&\n                itemToHide !== itemElement &&\n                (itemElement === null || itemElement === void 0 ? void 0 : itemElement.contains(itemToHide)) === false &&\n                itemToHide.contains(itemElement) === false) {\n                _this._hideAccordion(itemToHide);\n            }\n        });\n    };\n    KTMenu.prototype._getItemOption = function (element, name) {\n        var attr;\n        var value = null;\n        name = utils_1.default.camelReverseCase(name);\n        if (element && element.hasAttribute(\"data-menu-item-\".concat(name))) {\n            attr = element.getAttribute(\"data-menu-item-\".concat(name));\n            if (!attr)\n                return null;\n            value = this._getResponsiveOption(attr);\n        }\n        return value;\n    };\n    // General Methods\n    KTMenu.prototype.getItemTriggerMode = function (itemElement) {\n        return this._getItemOption(itemElement, 'trigger');\n    };\n    KTMenu.prototype.getItemToggleMode = function (element) {\n        return this._getItemToggleMode(element);\n    };\n    KTMenu.prototype.click = function (element, event) {\n        this._click(element, event);\n    };\n    KTMenu.prototype.link = function (element, event) {\n        this._link(element, event);\n    };\n    KTMenu.prototype.dismiss = function (element) {\n        this._dismiss(element);\n    };\n    KTMenu.prototype.mouseover = function (element) {\n        this._mouseover(element);\n    };\n    KTMenu.prototype.mouseout = function (element) {\n        this._mouseout(element);\n    };\n    KTMenu.prototype.show = function (itemElement) {\n        return this._show(itemElement);\n    };\n    KTMenu.prototype.hide = function (itemElement) {\n        this._hide(itemElement);\n    };\n    KTMenu.prototype.toggle = function (itemElement) {\n        this._toggle(itemElement);\n    };\n    KTMenu.prototype.reset = function (itemElement) {\n        this._reset(itemElement);\n    };\n    KTMenu.prototype.update = function () {\n        this._update();\n    };\n    KTMenu.prototype.setActiveLink = function (link) {\n        this._setActiveLink(link);\n    };\n    KTMenu.prototype.getLinkByAttribute = function (value, name) {\n        if (name === void 0) { name = 'href'; }\n        return this._getLinkByAttribute(value, name);\n    };\n    KTMenu.prototype.getItemLinkElement = function (itemElement) {\n        return this._getItemLinkElement(itemElement);\n    };\n    KTMenu.prototype.getItemElement = function (element) {\n        return this._getItemElement(element);\n    };\n    KTMenu.prototype.getItemSubElement = function (itemElement) {\n        return this._getItemSubElement(itemElement);\n    };\n    KTMenu.prototype.getItemParentElements = function (itemElement) {\n        return this._getItemParentElements(itemElement);\n    };\n    KTMenu.prototype.isItemSubShown = function (itemElement) {\n        return this._isItemSubShown(itemElement);\n    };\n    KTMenu.prototype.isItemParentShown = function (itemElement) {\n        return this._isItemParentShown(itemElement);\n    };\n    KTMenu.prototype.isItemDropdownPermanent = function (itemElement) {\n        return this._isItemDropdownPermanent(itemElement);\n    };\n    KTMenu.prototype.disable = function () {\n        this._disabled = true;\n    };\n    KTMenu.prototype.enable = function () {\n        this._disabled = false;\n    };\n    KTMenu.prototype.hideAccordions = function (itemElement) {\n        this._hideAccordions(itemElement);\n    };\n    // Statics methods\n    KTMenu.getInstance = function (element) {\n        if (!element) {\n            return null;\n        }\n        // Element has menu DOM reference in it's DATA storage\n        if (data_1.default.has(element, 'menu')) {\n            return data_1.default.get(element, 'menu');\n        }\n        // Element has .menu parent\n        var menuElement = element.closest('[data-menu]');\n        if (menuElement && data_1.default.has(menuElement, 'menu')) {\n            return data_1.default.get(menuElement, 'menu');\n        }\n        else if (menuElement && menuElement.getAttribute(\"data-menu\") === \"true\") {\n            return new KTMenu(menuElement);\n        }\n        var subElement = element.closest('[data-menu-sub-overflow=\"true\"]');\n        if (subElement && data_1.default.has(subElement, 'menu')) {\n            return data_1.default.get(subElement, 'menu');\n        }\n        // Element has a parent with DOM reference to .menu in it's DATA storage\n        if (element.classList.contains('menu-link') || element.classList.contains('menu-toggle')) {\n            var subElement_1 = (element.closest('.menu-dropdown') || element.closest('.menu-accordion'));\n            if (data_1.default.has(subElement_1, 'menu')) {\n                return data_1.default.get(subElement_1, 'menu');\n            }\n        }\n        return null;\n    };\n    KTMenu.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTMenu(element, config);\n    };\n    KTMenu.hide = function (skipElement) {\n        var itemElements = document.querySelectorAll('.show.menu-item-dropdown[data-menu-item-trigger]');\n        itemElements.forEach(function (itemElement) {\n            var _a;\n            var menu = KTMenu.getInstance(itemElement);\n            if (menu && menu.getItemToggleMode(itemElement) === 'dropdown') {\n                if (skipElement) {\n                    if (itemElement &&\n                        ((_a = menu.getItemSubElement(itemElement)) === null || _a === void 0 ? void 0 : _a.contains(skipElement)) === false &&\n                        itemElement.contains(skipElement) === false &&\n                        itemElement !== skipElement) {\n                        menu.hide(itemElement);\n                    }\n                }\n                else {\n                    menu.hide(itemElement);\n                }\n            }\n        });\n    };\n    KTMenu.updateDropdowns = function () {\n        var itemElements = document.querySelectorAll('.show.menu-item-dropdown[data-menu-item-trigger]');\n        itemElements.forEach(function (itemElement) {\n            if (data_1.default.has(itemElement, 'popper')) {\n                data_1.default.get(itemElement, 'popper').forceUpdate();\n            }\n        });\n    };\n    KTMenu.updateByLinkAttribute = function (value, name) {\n        if (name === void 0) { name = 'href'; }\n        var elements = document.querySelectorAll('[data-menu]');\n        elements.forEach(function (element) {\n            var menu = KTMenu.getInstance(element);\n            if (menu) {\n                var link = menu.getLinkByAttribute(value, name);\n                if (link) {\n                    menu.setActiveLink(link);\n                }\n            }\n        });\n    };\n    KTMenu.handleClickAway = function () {\n        document.addEventListener('click', function (event) {\n            var itemElements = document.querySelectorAll('.show.menu-item-dropdown[data-menu-item-trigger]:not([data-menu-item-static=\"true\"])');\n            itemElements.forEach(function (itemElement) {\n                var menu = KTMenu.getInstance(itemElement);\n                if (menu &&\n                    menu.getItemToggleMode(itemElement) === 'dropdown') {\n                    var subElement = menu.getItemSubElement(itemElement);\n                    if (itemElement === event.target ||\n                        itemElement.contains(event.target)) {\n                        return;\n                    }\n                    if (subElement && (subElement === event.target || subElement.contains(event.target))) {\n                        return;\n                    }\n                    menu.hide(itemElement);\n                }\n            });\n        });\n    };\n    KTMenu.findFocused = function () {\n        var linkElement = document.querySelector('.menu-link:focus, .menu-toggle:focus');\n        if (linkElement && dom_1.default.isVisible(linkElement)) {\n            return linkElement;\n        }\n        else {\n            return null;\n        }\n    };\n    KTMenu.getFocusLink = function (linkElement, direction, preFocus) {\n        if (preFocus === void 0) { preFocus = false; }\n        if (!linkElement)\n            return null;\n        var itemElement = linkElement.parentElement;\n        if (!itemElement || !itemElement.classList.contains('menu-item'))\n            return null;\n        if (direction === 'next') {\n            var nextElement = linkElement.nextElementSibling;\n            if (nextElement && (nextElement.matches('.menu-accordion' + (!preFocus ? '.show' : '')) || nextElement.matches('.menu-dropdown' + (!preFocus ? '.show' : '')))) {\n                var itemElement2 = dom_1.default.child(nextElement, '.menu-item');\n                return dom_1.default.child(itemElement2, '.menu-link');\n            }\n            else {\n                var nextElement2 = itemElement.nextElementSibling;\n                if (nextElement2 && nextElement2.classList.contains('menu-item')) {\n                    var nextLink = dom_1.default.child(nextElement2, '.menu-link');\n                    if (nextLink) {\n                        return nextLink;\n                    }\n                }\n            }\n        }\n        else {\n            var prevElement = itemElement.previousElementSibling;\n            if (prevElement) {\n                if (prevElement && prevElement.classList.contains('menu-item')) {\n                    var nextLink = dom_1.default.child(prevElement, '.menu-link');\n                    if (nextLink) {\n                        return nextLink;\n                    }\n                }\n            }\n            else {\n                var parentElement = itemElement.parentElement;\n                if (parentElement && (parentElement.matches('.menu-accordion' + (!preFocus ? '.show' : '')) || parentElement.matches('.menu-dropdown' + (!preFocus ? '.show' : '')))) {\n                    var prevElement2 = parentElement.previousElementSibling;\n                    if (prevElement2.classList.contains('menu-link')) {\n                        return prevElement2;\n                    }\n                }\n            }\n        }\n        return null;\n    };\n    KTMenu.handleKeyboard = function () {\n        var _this = this;\n        document.addEventListener('keydown', function (event) {\n            if (constants_1.KT_ACCESSIBILITY_KEYS.includes(event.key) && !(event.ctrlKey || event.altKey || event.shiftKey)) {\n                var currentFocused = _this.findFocused();\n                if (!currentFocused)\n                    return;\n                if (['ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {\n                    var direction = ['ArrowDown', 'ArrowRight'].includes(event.key) ? 'next' : 'previouse';\n                    var newFocusLink = _this.getFocusLink(currentFocused, direction);\n                    event.preventDefault();\n                    if (newFocusLink) {\n                        newFocusLink.focus();\n                        newFocusLink.classList.add('focus');\n                    }\n                }\n                if (event.key === 'Enter') {\n                    var menu = _this.getInstance(currentFocused);\n                    var itemElement = menu.getItemElement(currentFocused);\n                    var subShown = menu.isItemSubShown(itemElement);\n                    if (!menu)\n                        return;\n                    if (menu.getItemToggleMode(itemElement) === 'accordion') {\n                        currentFocused.dispatchEvent(new MouseEvent('click', {\n                            bubbles: true\n                        }));\n                    }\n                    if (menu.getItemToggleMode(itemElement) === 'dropdown') {\n                        if (menu.getItemTriggerMode(itemElement) === 'click') {\n                            currentFocused.dispatchEvent(new MouseEvent('click', {\n                                bubbles: true\n                            }));\n                        }\n                        else {\n                            if (subShown) {\n                                currentFocused.dispatchEvent(new MouseEvent('mouseout', {\n                                    bubbles: true\n                                }));\n                            }\n                            else {\n                                currentFocused.dispatchEvent(new MouseEvent('mouseover', {\n                                    bubbles: true\n                                }));\n                            }\n                        }\n                    }\n                    if (subShown) {\n                        var subFocus = _this.getFocusLink(currentFocused, 'next', true);\n                        if (subFocus) {\n                            subFocus.focus();\n                        }\n                    }\n                    else {\n                        currentFocused.focus();\n                    }\n                    event.preventDefault();\n                }\n                if (event.key === 'Escape') {\n                    var items = document.querySelectorAll('.show.menu-item-dropdown[data-menu-item-trigger]:not([data-menu-item-static=\"true\"])');\n                    items.forEach(function (item) {\n                        var menu = KTMenu.getInstance(item);\n                        if (menu &&\n                            menu.getItemToggleMode(item) === 'dropdown') {\n                            menu.hide(item);\n                        }\n                    });\n                }\n            }\n        }, false);\n    };\n    KTMenu.handleMouseover = function () {\n        event_handler_1.default.on(document.body, '[data-menu-item-trigger], .menu-dropdown', 'mouseover', function (event, target) {\n            var menu = KTMenu.getInstance(target);\n            if (menu !== null && menu.getItemToggleMode(target) === 'dropdown') {\n                return menu.mouseover(target);\n            }\n        });\n    };\n    KTMenu.handleMouseout = function () {\n        event_handler_1.default.on(document.body, '[data-menu-item-trigger], .menu-dropdown', 'mouseout', function (event, target) {\n            var menu = KTMenu.getInstance(target);\n            if (menu !== null && menu.getItemToggleMode(target) === 'dropdown') {\n                return menu.mouseout(target);\n            }\n        });\n    };\n    KTMenu.handleClick = function () {\n        event_handler_1.default.on(document.body, '.menu-item[data-menu-item-trigger] > .menu-link, .menu-item[data-menu-item-trigger] > .menu-label .menu-toggle, .menu-item[data-menu-item-trigger] > .menu-toggle, [data-menu-item-trigger]:not(.menu-item):not([data-menu-item-trigger=\"auto\"])', 'click', function (event, target) {\n            var menu = KTMenu.getInstance(target);\n            if (menu !== null) {\n                return menu.click(target, event);\n            }\n        });\n        event_handler_1.default.on(document.body, '.menu-item:not([data-menu-item-trigger]) > .menu-link', 'click', function (event, target) {\n            var menu = KTMenu.getInstance(target);\n            if (menu !== null) {\n                if (target.tagName == 'a' || target.hasAttribute('href')) {\n                    menu.dismiss(target);\n                }\n                return menu.link(target, event);\n            }\n        });\n    };\n    KTMenu.handleDismiss = function () {\n        event_handler_1.default.on(document.body, '[data-menu-dismiss=\"true\"]', 'click', function (event, target) {\n            var menu = KTMenu.getInstance(target);\n            if (menu !== null) {\n                return menu.dismiss(target);\n            }\n        });\n    };\n    KTMenu.handleResize = function () {\n        window.addEventListener('resize', function () {\n            var timer;\n            utils_1.default.throttle(timer, function () {\n                // Locate and update Offcanvas instances on window resize\n                var elements = document.querySelectorAll('[data-menu]');\n                elements.forEach(function (element) {\n                    var _a;\n                    (_a = KTMenu.getInstance(element)) === null || _a === void 0 ? void 0 : _a.update();\n                });\n            }, 200);\n        });\n    };\n    KTMenu.initHandlers = function () {\n        this.handleDismiss();\n        this.handleClickAway();\n        this.handleKeyboard();\n        this.handleMouseover();\n        this.handleMouseout();\n        this.handleClick();\n        this.handleResize();\n    };\n    KTMenu.createInstances = function () {\n        var elements = document.querySelectorAll('[data-menu]:not([data-menu=false])');\n        elements.forEach(function (element) {\n            new KTMenu(element);\n        });\n    };\n    KTMenu.init = function () {\n        KTMenu.createInstances();\n        if (window.KT_MENU_INITIALIZED !== true) {\n            KTMenu.initHandlers();\n            window.KT_MENU_INITIALIZED = true;\n        }\n    };\n    return KTMenu;\n}(component_1.default));\nexports.KTMenu = KTMenu;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/menu/menu.ts?");

/***/ }),

/***/ "./src/core/components/modal/index.ts":
/*!********************************************!*\
  !*** ./src/core/components/modal/index.ts ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTModal = void 0;\nvar modal_1 = __webpack_require__(/*! ./modal */ \"./src/core/components/modal/modal.ts\");\nObject.defineProperty(exports, \"KTModal\", ({ enumerable: true, get: function () { return modal_1.KTModal; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/modal/index.ts?");

/***/ }),

/***/ "./src/core/components/modal/modal.ts":
/*!********************************************!*\
  !*** ./src/core/components/modal/modal.ts ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTModal = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTModal = /** @class */ (function (_super) {\n    __extends(KTModal, _super);\n    function KTModal(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'modal';\n        _this._defaultConfig = {\n            zindex: '90',\n            backdrop: true,\n            backdropClass: 'transition-all duration-300 fixed inset-0 bg-gray-900 opacity-25',\n            backdropStatic: false,\n            keyboard: true,\n            disableScroll: true,\n            persistent: false,\n            focus: true,\n            hiddenClass: 'hidden'\n        };\n        _this._config = _this._defaultConfig;\n        _this._isOpen = false;\n        _this._isTransitioning = false;\n        _this._backdropElement = null;\n        _this._targetElement = null;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._handlers();\n        return _this;\n    }\n    KTModal.prototype._handlers = function () {\n        var _this = this;\n        this._element.addEventListener('click', function (event) {\n            if (_this._element !== event.target)\n                return;\n            if (_this._getOption('backdropStatic') === false) {\n                _this._hide();\n            }\n        });\n    };\n    KTModal.prototype._toggle = function (targetElement) {\n        var payload = { cancel: false };\n        this._fireEvent('toggle', payload);\n        this._dispatchEvent('toggle', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (this._isOpen === true) {\n            this._hide();\n        }\n        else {\n            this._show(targetElement);\n        }\n    };\n    KTModal.prototype._show = function (targetElement) {\n        var _this = this;\n        if (this._isOpen || this._isTransitioning) {\n            return;\n        }\n        //const beforeScroll = this.fireEvent('beforeScroll', this.el);\n        //this.dispatch('beforeScroll.hs.scrollspy', this.el, this.el);\n        //if (beforeScroll instanceof Promise) beforeScroll.then(() => scrollFn());\n        //else scrollFn();\n        if (targetElement)\n            this._targetElement = targetElement;\n        var payload = { cancel: false };\n        this._fireEvent('show', payload);\n        this._dispatchEvent('show', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        KTModal.hide();\n        if (!this._element)\n            return;\n        this._isTransitioning = true;\n        this._element.setAttribute('role', 'dialog');\n        this._element.setAttribute('aria-modal', 'true');\n        this._element.setAttribute('tabindex', '-1');\n        this._setZindex();\n        if (this._getOption('backdrop') === true)\n            this._createBackdrop();\n        if (this._getOption('disableScroll')) {\n            document.body.style.overflow = 'hidden';\n        }\n        this._element.style.display = 'block';\n        dom_1.default.reflow(this._element);\n        this._element.classList.add('open');\n        this._element.classList.remove(this._getOption('hiddenClass'));\n        dom_1.default.transitionEnd(this._element, function () {\n            _this._isTransitioning = false;\n            _this._isOpen = true;\n            if (_this._getOption('focus') === true) {\n                _this._autoFocus();\n            }\n            _this._fireEvent('shown');\n            _this._dispatchEvent('shown');\n        });\n    };\n    KTModal.prototype._hide = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        if (this._isOpen === false || this._isTransitioning) {\n            return;\n        }\n        var payload = { cancel: false };\n        this._fireEvent('hide', payload);\n        this._dispatchEvent('hide', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        this._isTransitioning = true;\n        this._element.removeAttribute('role');\n        this._element.removeAttribute('aria-modal');\n        this._element.removeAttribute('tabindex');\n        if (this._getOption('disableScroll')) {\n            document.body.style.overflow = '';\n        }\n        dom_1.default.reflow(this._element);\n        this._element.classList.remove('open');\n        if (this._getOption('backdrop') === true) {\n            this._deleteBackdrop();\n        }\n        dom_1.default.transitionEnd(this._element, function () {\n            if (!_this._element)\n                return;\n            _this._isTransitioning = false;\n            _this._isOpen = false;\n            _this._element.style.display = '';\n            _this._element.classList.add(_this._getOption('hiddenClass'));\n            _this._fireEvent('hidden');\n            _this._dispatchEvent('hidden');\n        });\n    };\n    KTModal.prototype._setZindex = function () {\n        var zindex = parseInt(this._getOption('zindex'));\n        if (parseInt(dom_1.default.getCssProp(this._element, 'z-index')) > zindex) {\n            zindex = parseInt(dom_1.default.getCssProp(this._element, 'z-index'));\n        }\n        if (dom_1.default.getHighestZindex(this._element) > zindex) {\n            zindex = dom_1.default.getHighestZindex(this._element) + 1;\n        }\n        this._element.style.zIndex = String(zindex);\n    };\n    KTModal.prototype._autoFocus = function () {\n        if (!this._element)\n            return;\n        var input = this._element.querySelector('[data-modal-input-focus]');\n        if (!input)\n            return;\n        else\n            input.focus();\n    };\n    KTModal.prototype._createBackdrop = function () {\n        if (!this._element)\n            return;\n        var zindex = parseInt(dom_1.default.getCssProp(this._element, 'z-index'));\n        this._backdropElement = document.createElement('DIV');\n        this._backdropElement.style.zIndex = (zindex - 1).toString();\n        this._backdropElement.classList.add('modal-backdrop');\n        document.body.append(this._backdropElement);\n        dom_1.default.reflow(this._backdropElement);\n        dom_1.default.addClass(this._backdropElement, this._getOption('backdropClass'));\n    };\n    KTModal.prototype._deleteBackdrop = function () {\n        var _this = this;\n        if (!this._backdropElement)\n            return;\n        dom_1.default.reflow(this._backdropElement);\n        this._backdropElement.style.opacity = \"0\";\n        dom_1.default.transitionEnd(this._backdropElement, function () {\n            if (!_this._backdropElement)\n                return;\n            dom_1.default.remove(_this._backdropElement);\n        });\n    };\n    KTModal.prototype.toggle = function (targetElement) {\n        return this._toggle(targetElement);\n    };\n    KTModal.prototype.show = function (targetElement) {\n        return this._show(targetElement);\n    };\n    KTModal.prototype.hide = function () {\n        return this._hide();\n    };\n    KTModal.prototype.getTargetElement = function () {\n        return this._targetElement;\n    };\n    KTModal.prototype.isOpen = function () {\n        return this._isOpen;\n    };\n    KTModal.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'modal')) {\n            return data_1.default.get(element, 'modal');\n        }\n        if (element.getAttribute('data-modal') === \"true\") {\n            return new KTModal(element);\n        }\n        return null;\n    };\n    KTModal.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTModal(element, config);\n    };\n    KTModal.hide = function () {\n        var elements = document.querySelectorAll('[data-modal]');\n        elements.forEach(function (element) {\n            var modal = KTModal.getInstance(element);\n            if (modal && modal.isOpen()) {\n                modal.hide();\n            }\n        });\n    };\n    KTModal.handleToggle = function () {\n        event_handler_1.default.on(document.body, '[data-modal-toggle]', 'click', function (event, target) {\n            event.stopPropagation();\n            var selector = target.getAttribute(\"data-modal-toggle\");\n            if (!selector)\n                return;\n            var modalElement = document.querySelector(selector);\n            var modal = KTModal.getInstance(modalElement);\n            if (modal) {\n                modal.toggle(target);\n            }\n        });\n    };\n    KTModal.handleDismiss = function () {\n        event_handler_1.default.on(document.body, '[data-modal-dismiss]', 'click', function (event, target) {\n            event.stopPropagation();\n            var modalElement = target.closest('[data-modal]');\n            if (modalElement) {\n                var modal = KTModal.getInstance(modalElement);\n                if (modal) {\n                    modal.hide();\n                }\n            }\n        });\n    };\n    KTModal.handleClickAway = function () {\n        document.addEventListener('click', function (event) {\n            var modalElement = document.querySelector('.open[data-modal]');\n            if (!modalElement)\n                return;\n            var modal = KTModal.getInstance(modalElement);\n            if (!modal)\n                return;\n            if (modal.getOption('persistent'))\n                return;\n            if (modal.getOption('backdrop'))\n                return;\n            if (modalElement !== event.target &&\n                modal.getTargetElement() !== event.target &&\n                modalElement.contains(event.target) === false) {\n                modal.hide();\n            }\n        });\n    };\n    KTModal.handleKeyword = function () {\n        document.addEventListener('keydown', function (event) {\n            var modalElement = document.querySelector('.open[data-modal]');\n            var modal = KTModal.getInstance(modalElement);\n            if (!modal) {\n                return;\n            }\n            // if esc key was not pressed in combination with ctrl or alt or shift\n            if (event.key === 'Escape' && !(event.ctrlKey || event.altKey || event.shiftKey)) {\n                modal.hide();\n            }\n            if (event.code === 'Tab' && !event.metaKey) {\n                return;\n            }\n        });\n    };\n    KTModal.createInstances = function () {\n        var elements = document.querySelectorAll('[data-modal=\"true\"]');\n        elements.forEach(function (element) {\n            new KTModal(element);\n        });\n    };\n    KTModal.init = function () {\n        KTModal.createInstances();\n        if (window.KT_MODAL_INITIALIZED !== true) {\n            KTModal.handleToggle();\n            KTModal.handleDismiss();\n            KTModal.handleClickAway();\n            KTModal.handleKeyword();\n            window.KT_MODAL_INITIALIZED = true;\n        }\n    };\n    return KTModal;\n}(component_1.default));\nexports.KTModal = KTModal;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/modal/modal.ts?");

/***/ }),

/***/ "./src/core/components/reparent/index.ts":
/*!***********************************************!*\
  !*** ./src/core/components/reparent/index.ts ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTReparent = void 0;\nvar reparent_1 = __webpack_require__(/*! ./reparent */ \"./src/core/components/reparent/reparent.ts\");\nObject.defineProperty(exports, \"KTReparent\", ({ enumerable: true, get: function () { return reparent_1.KTReparent; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/reparent/index.ts?");

/***/ }),

/***/ "./src/core/components/reparent/reparent.ts":
/*!**************************************************!*\
  !*** ./src/core/components/reparent/reparent.ts ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTReparent = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar utils_1 = __webpack_require__(/*! ../../helpers/utils */ \"./src/core/helpers/utils.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTReparent = /** @class */ (function (_super) {\n    __extends(KTReparent, _super);\n    function KTReparent(element, config) {\n        if (config === void 0) { config = null; }\n        var _this = _super.call(this) || this;\n        _this._name = 'reparent';\n        _this._defaultConfig = {\n            mode: '',\n            target: ''\n        };\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._update();\n        return _this;\n    }\n    KTReparent.prototype._update = function () {\n        if (!this._element)\n            return;\n        var target = this._getOption('target');\n        var targetEl = dom_1.default.getElement(target);\n        var mode = this._getOption('mode');\n        if (targetEl && this._element.parentNode !== targetEl) {\n            if (mode === 'prepend') {\n                targetEl.prepend(this._element);\n            }\n            else if (mode === 'append') {\n                targetEl.append(this._element);\n            }\n        }\n    };\n    KTReparent.prototype.update = function () {\n        this._update();\n    };\n    KTReparent.handleResize = function () {\n        window.addEventListener('resize', function () {\n            var timer;\n            utils_1.default.throttle(timer, function () {\n                document.querySelectorAll('[data-reparent]').forEach(function (element) {\n                    var reparent = KTReparent.getInstance(element);\n                    reparent === null || reparent === void 0 ? void 0 : reparent.update();\n                });\n            }, 200);\n        });\n    };\n    KTReparent.getInstance = function (element) {\n        return data_1.default.get(element, 'reparent');\n    };\n    KTReparent.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTReparent(element, config);\n    };\n    KTReparent.createInstances = function () {\n        var elements = document.querySelectorAll('[data-reparent=\"true\"]');\n        elements.forEach(function (element) {\n            new KTReparent(element);\n        });\n    };\n    KTReparent.init = function () {\n        KTReparent.createInstances();\n        if (window.KT_REPARENT_INITIALIZED !== true) {\n            KTReparent.handleResize();\n            window.KT_REPARENT_INITIALIZED = true;\n        }\n    };\n    return KTReparent;\n}(component_1.default));\nexports.KTReparent = KTReparent;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/reparent/reparent.ts?");

/***/ }),

/***/ "./src/core/components/scrollable/index.ts":
/*!*************************************************!*\
  !*** ./src/core/components/scrollable/index.ts ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTScrollable = void 0;\nvar scrollable_1 = __webpack_require__(/*! ./scrollable */ \"./src/core/components/scrollable/scrollable.ts\");\nObject.defineProperty(exports, \"KTScrollable\", ({ enumerable: true, get: function () { return scrollable_1.KTScrollable; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/scrollable/index.ts?");

/***/ }),

/***/ "./src/core/components/scrollable/scrollable.ts":
/*!******************************************************!*\
  !*** ./src/core/components/scrollable/scrollable.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTScrollable = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar utils_1 = __webpack_require__(/*! ../../helpers/utils */ \"./src/core/helpers/utils.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTScrollable = /** @class */ (function (_super) {\n    __extends(KTScrollable, _super);\n    function KTScrollable(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'scrollable';\n        _this._defaultConfig = {\n            save: true,\n            dependencies: '',\n            wrappers: '',\n            offset: ''\n        };\n        _this._config = _this._defaultConfig;\n        _this._elementId = null;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        if (!_this._element)\n            return _this;\n        _this._elementId = _this._element.getAttribute('id');\n        _this._handlers();\n        _this._update();\n        return _this;\n    }\n    KTScrollable.prototype._handlers = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        this._element.addEventListener('scroll', function () {\n            if (!_this._element)\n                return;\n            localStorage.setItem(\"\".concat(_this._elementId, \"st\"), _this._element.scrollTop.toString());\n        });\n    };\n    KTScrollable.prototype._update = function () {\n        this._setupHeight();\n        this._setupState();\n    };\n    KTScrollable.prototype._setupHeight = function () {\n        if (!this._element)\n            return;\n        var heightType = this._getHeightType();\n        var height = this._getHeight();\n        // Set height\n        if (height && height != '0' && height.length > 0) {\n            this._element.style.setProperty(heightType, height);\n        }\n        else {\n            this._element.style.setProperty(heightType, '');\n        }\n    };\n    KTScrollable.prototype._setupState = function () {\n        if (!this._element)\n            return;\n        var stateEnabled = this._getOption('state') === true;\n        var elementIdExists = Boolean(this._elementId);\n        if (stateEnabled && elementIdExists) {\n            var storedPosition = localStorage.getItem(this._elementId + 'st');\n            if (storedPosition) {\n                var pos = parseInt(storedPosition);\n                if (pos > 0) {\n                    this._element.scroll({\n                        top: pos,\n                        behavior: 'instant'\n                    });\n                }\n            }\n        }\n    };\n    KTScrollable.prototype._getHeight = function () {\n        var height = this._getHeightOption();\n        if (height !== null && typeof height === 'string' && height.toLowerCase() === 'auto') {\n            return this._getAutoHeight();\n        }\n        else if (height) {\n            return parseInt(height).toString() + 'px';\n        }\n        else {\n            return '0';\n        }\n    };\n    KTScrollable.prototype._getAutoHeight = function () {\n        var _this = this;\n        if (!this._element)\n            return '';\n        var height = dom_1.default.getViewPort().height;\n        var dependencies = this._getOption('dependencies');\n        var wrappers = this._getOption('wrappers');\n        var offset = this._getOption('offset');\n        height -= this._getElementSpacing(this._element);\n        if (dependencies && dependencies.length > 0) {\n            var elements = document.querySelectorAll(dependencies);\n            elements.forEach(function (element) {\n                if (dom_1.default.getCssProp(element, 'display') === 'none') {\n                    return;\n                }\n                height -= _this._getElementHeight(element);\n            });\n        }\n        if (wrappers && wrappers.length > 0) {\n            var elements = document.querySelectorAll(wrappers);\n            elements.forEach(function (element) {\n                if (dom_1.default.getCssProp(element, 'display') === 'none') {\n                    return;\n                }\n                height -= _this._getElementSpacing(element);\n            });\n        }\n        if (offset && offset.length > 0) {\n            height -= parseInt(offset);\n        }\n        return height.toString() + 'px';\n    };\n    KTScrollable.prototype._getElementHeight = function (element) {\n        var height = 0;\n        if (!element) {\n            return height;\n        }\n        var computedStyle = window.getComputedStyle(element);\n        if (computedStyle.height) {\n            height += parseInt(computedStyle.height);\n        }\n        if (computedStyle.marginTop) {\n            height += parseInt(computedStyle.marginTop);\n        }\n        if (computedStyle.marginBottom) {\n            height += parseInt(computedStyle.marginBottom);\n        }\n        if (computedStyle.borderTopWidth) {\n            height += parseInt(computedStyle.borderTopWidth);\n        }\n        if (computedStyle.borderBottomWidth) {\n            height += parseInt(computedStyle.borderBottomWidth);\n        }\n        return height;\n    };\n    KTScrollable.prototype._getElementSpacing = function (element) {\n        var spacing = 0;\n        if (!element) {\n            return spacing;\n        }\n        var computedStyle = window.getComputedStyle(element);\n        if (computedStyle.marginTop) {\n            spacing += parseInt(computedStyle.marginTop);\n        }\n        if (computedStyle.marginBottom) {\n            spacing += parseInt(computedStyle.marginBottom);\n        }\n        if (computedStyle.paddingTop) {\n            spacing += parseInt(computedStyle.paddingTop);\n        }\n        if (computedStyle.paddingBottom) {\n            spacing += parseInt(computedStyle.paddingBottom);\n        }\n        if (computedStyle.borderTopWidth) {\n            spacing += parseInt(computedStyle.borderTopWidth);\n        }\n        if (computedStyle.borderBottomWidth) {\n            spacing += parseInt(computedStyle.borderBottomWidth);\n        }\n        return spacing;\n    };\n    KTScrollable.prototype._getHeightType = function () {\n        if (this._getOption('minHeight')) {\n            return 'min-height';\n        }\n        if (this._getOption('maxHeight')) {\n            return 'max-height';\n        }\n        else {\n            return 'height';\n        }\n    };\n    KTScrollable.prototype._getHeightOption = function () {\n        var heightType = this._getHeightType();\n        if (heightType == 'min-height') {\n            return this._getOption('minHeight');\n        }\n        if (heightType == 'max-height') {\n            return this._getOption('maxHeight');\n        }\n        else {\n            return this._getOption('height');\n        }\n    };\n    KTScrollable.prototype.update = function () {\n        return this._update();\n    };\n    KTScrollable.prototype.getHeight = function () {\n        return this._getHeight();\n    };\n    KTScrollable.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'scrollable')) {\n            return data_1.default.get(element, 'scrollable');\n        }\n        if (element.getAttribute('data-scrollable') === \"true\") {\n            return new KTScrollable(element);\n        }\n        return null;\n    };\n    KTScrollable.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTScrollable(element, config);\n    };\n    KTScrollable.createInstances = function () {\n        var elements = document.querySelectorAll('[data-scrollable=\"true\"]');\n        elements.forEach(function (element) {\n            new KTScrollable(element);\n        });\n    };\n    KTScrollable.handleResize = function () {\n        window.addEventListener('resize', function () {\n            var timer;\n            utils_1.default.throttle(timer, function () {\n                // Locate and update scrollable instances on window resize\n                var elements = document.querySelectorAll('[data-scrollable]');\n                elements.forEach(function (element) {\n                    var _a;\n                    (_a = KTScrollable.getInstance(element)) === null || _a === void 0 ? void 0 : _a.update();\n                });\n            }, 200);\n        });\n    };\n    KTScrollable.init = function () {\n        KTScrollable.createInstances();\n        if (window.KT_SCROLL_INITIALIZED !== true) {\n            KTScrollable.handleResize();\n            window.KT_SCROLL_INITIALIZED = true;\n        }\n    };\n    return KTScrollable;\n}(component_1.default));\nexports.KTScrollable = KTScrollable;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/scrollable/scrollable.ts?");

/***/ }),

/***/ "./src/core/components/scrollspy/index.ts":
/*!************************************************!*\
  !*** ./src/core/components/scrollspy/index.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTScrollspy = void 0;\nvar scrollspy_1 = __webpack_require__(/*! ./scrollspy */ \"./src/core/components/scrollspy/scrollspy.ts\");\nObject.defineProperty(exports, \"KTScrollspy\", ({ enumerable: true, get: function () { return scrollspy_1.KTScrollspy; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/scrollspy/index.ts?");

/***/ }),

/***/ "./src/core/components/scrollspy/scrollspy.ts":
/*!****************************************************!*\
  !*** ./src/core/components/scrollspy/scrollspy.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTScrollspy = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTScrollspy = /** @class */ (function (_super) {\n    __extends(KTScrollspy, _super);\n    function KTScrollspy(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'scrollspy';\n        _this._defaultConfig = {\n            target: 'body',\n            offset: 0,\n            smooth: true\n        };\n        _this._config = _this._defaultConfig;\n        _this._targetElement = null;\n        _this._anchorElements = null;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        if (!_this._element)\n            return _this;\n        var targetElement = _this._getTarget() === 'body' ? document : dom_1.default.getElement(_this._getTarget());\n        if (!targetElement)\n            return _this;\n        _this._targetElement = targetElement;\n        _this._anchorElements = _this._element.querySelectorAll('[data-scrollspy-anchor]');\n        if (!_this._anchorElements)\n            return _this;\n        _this._handlers();\n        _this._update();\n        return _this;\n    }\n    KTScrollspy.prototype._getTarget = function () {\n        return (this._element.getAttribute('data-scrollspy-target') || this._getOption('target'));\n    };\n    KTScrollspy.prototype._handlers = function () {\n        var _this = this;\n        if (!this._anchorElements)\n            return;\n        this._targetElement.addEventListener('scroll', function () {\n            _this._anchorElements.forEach(function (anchorElement) {\n                _this._updateAnchor(anchorElement);\n            });\n        });\n        event_handler_1.default.on(this._element, '[data-scrollspy-anchor]', 'click', function (event, target) {\n            event.preventDefault();\n            _this._scrollTo(target);\n        });\n    };\n    KTScrollspy.prototype._scrollTo = function (anchorElement) {\n        if (!anchorElement)\n            return;\n        var sectionElement = dom_1.default.getElement(anchorElement.getAttribute('href'));\n        if (!sectionElement)\n            return;\n        var targetElement = this._targetElement === document ? window : this._targetElement;\n        if (!targetElement)\n            return;\n        var offset = parseInt(this._getOption('offset'));\n        if (anchorElement.getAttribute('data-scrollspy-anchor-offset')) {\n            offset = parseInt(anchorElement.getAttribute('data-scrollspy-anchor-offset'));\n        }\n        var scrollTop = sectionElement.offsetTop - offset;\n        if ('scrollTo' in targetElement) {\n            targetElement.scrollTo({\n                top: scrollTop,\n                left: 0,\n                behavior: this._getOption('smooth') ? 'smooth' : 'instant',\n            });\n        }\n    };\n    KTScrollspy.prototype._updateAnchor = function (anchorElement) {\n        var sectionElement = dom_1.default.getElement(anchorElement.getAttribute('href'));\n        if (!sectionElement)\n            return;\n        if (!dom_1.default.isVisible(anchorElement))\n            return;\n        if (!this._anchorElements)\n            return;\n        var scrollPosition = this._targetElement === document ? (document.documentElement.scrollTop || document.body.scrollTop) : this._targetElement.scrollTop;\n        var offset = parseInt(this._getOption('offset'));\n        if (anchorElement.getAttribute('data-scrollspy-anchor-offset')) {\n            offset = parseInt(anchorElement.getAttribute('data-scrollspy-anchor-offset'));\n        }\n        var offsetTop = sectionElement.offsetTop;\n        if ((scrollPosition + offset) >= offsetTop) {\n            this._anchorElements.forEach(function (anchorElement) {\n                anchorElement.classList.remove('active');\n            });\n            var payload = { element: anchorElement };\n            this._fireEvent('activate', payload);\n            this._dispatchEvent('activate', payload);\n            anchorElement.classList.add('active');\n            var parentAnchorElements = dom_1.default.parents(anchorElement, '[data-scrollspy-group]');\n            if (parentAnchorElements) {\n                parentAnchorElements.forEach(function (parentAnchorElement) {\n                    var _a;\n                    (_a = parentAnchorElement.querySelector('[data-scrollspy-anchor]')) === null || _a === void 0 ? void 0 : _a.classList.add('active');\n                });\n            }\n        }\n    };\n    KTScrollspy.prototype._update = function () {\n        var _this = this;\n        if (!this._anchorElements)\n            return;\n        this._anchorElements.forEach(function (anchorElement) {\n            _this._updateAnchor(anchorElement);\n        });\n    };\n    KTScrollspy.prototype._isActive = function (anchorElement) {\n        return anchorElement.classList.contains('active');\n    };\n    KTScrollspy.prototype.updateAnchor = function (anchorElement) {\n        this._updateAnchor(anchorElement);\n    };\n    KTScrollspy.prototype.isActive = function (anchorElement) {\n        return this._isActive(anchorElement);\n    };\n    KTScrollspy.prototype.update = function () {\n        this.update();\n    };\n    KTScrollspy.prototype.scrollTo = function (anchorElement) {\n        this._scrollTo(anchorElement);\n    };\n    KTScrollspy.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'scrollspy')) {\n            return data_1.default.get(element, 'scrollspy');\n        }\n        if (element.getAttribute('data-scrollspy') === \"true\") {\n            return new KTScrollspy(element);\n        }\n        return null;\n    };\n    KTScrollspy.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTScrollspy(element, config);\n    };\n    KTScrollspy.createInstances = function () {\n        var elements = document.querySelectorAll('[data-scrollspy=\"true\"]');\n        elements.forEach(function (element) {\n            new KTScrollspy(element);\n        });\n    };\n    KTScrollspy.init = function () {\n        KTScrollspy.createInstances();\n    };\n    return KTScrollspy;\n}(component_1.default));\nexports.KTScrollspy = KTScrollspy;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/scrollspy/scrollspy.ts?");

/***/ }),

/***/ "./src/core/components/scrollto/index.ts":
/*!***********************************************!*\
  !*** ./src/core/components/scrollto/index.ts ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTScrollto = void 0;\nvar scrollto_1 = __webpack_require__(/*! ./scrollto */ \"./src/core/components/scrollto/scrollto.ts\");\nObject.defineProperty(exports, \"KTScrollto\", ({ enumerable: true, get: function () { return scrollto_1.KTScrollto; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/scrollto/index.ts?");

/***/ }),

/***/ "./src/core/components/scrollto/scrollto.ts":
/*!**************************************************!*\
  !*** ./src/core/components/scrollto/scrollto.ts ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTScrollto = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTScrollto = /** @class */ (function (_super) {\n    __extends(KTScrollto, _super);\n    function KTScrollto(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'scrollto';\n        _this._defaultConfig = {\n            smooth: true,\n            parent: 'body',\n            target: '',\n            offset: 0,\n        };\n        _this._config = _this._defaultConfig;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        if (!_this._element)\n            return _this;\n        _this._targetElement = _this._getTargetElement();\n        if (!_this._targetElement) {\n            return _this;\n        }\n        _this._handlers();\n        return _this;\n    }\n    KTScrollto.prototype._getTargetElement = function () {\n        return (dom_1.default.getElement(this._element.getAttribute('data-scrollto')) ||\n            dom_1.default.getElement(this._getOption('target')));\n    };\n    KTScrollto.prototype._handlers = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        this._element.addEventListener('click', function (event) {\n            event.preventDefault();\n            _this._scroll();\n        });\n    };\n    KTScrollto.prototype._scroll = function () {\n        var pos = this._targetElement.offsetTop + parseInt(this._getOption('offset'));\n        var parent = dom_1.default.getElement(this._getOption('parent'));\n        if (!parent || parent === document.body) {\n            parent = window;\n        }\n        parent.scrollTo({\n            top: pos,\n            behavior: this._getOption('smooth') ? 'smooth' : 'instant',\n        });\n    };\n    KTScrollto.prototype.scroll = function () {\n        this._scroll();\n    };\n    KTScrollto.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'scrollto')) {\n            return data_1.default.get(element, 'scrollto');\n        }\n        if (element.getAttribute('data-scrollto') !== \"false\") {\n            return new KTScrollto(element);\n        }\n        return null;\n    };\n    KTScrollto.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTScrollto(element, config);\n    };\n    KTScrollto.createInstances = function () {\n        var elements = document.querySelectorAll('[data-scrollto]:not([data-scrollto=\"false\"])');\n        elements.forEach(function (element) {\n            new KTScrollto(element);\n        });\n    };\n    KTScrollto.init = function () {\n        KTScrollto.createInstances();\n    };\n    return KTScrollto;\n}(component_1.default));\nexports.KTScrollto = KTScrollto;\n;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/scrollto/scrollto.ts?");

/***/ }),

/***/ "./src/core/components/stepper/index.ts":
/*!**********************************************!*\
  !*** ./src/core/components/stepper/index.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTStepper = void 0;\nvar stepper_1 = __webpack_require__(/*! ./stepper */ \"./src/core/components/stepper/stepper.ts\");\nObject.defineProperty(exports, \"KTStepper\", ({ enumerable: true, get: function () { return stepper_1.KTStepper; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/stepper/index.ts?");

/***/ }),

/***/ "./src/core/components/stepper/stepper.ts":
/*!************************************************!*\
  !*** ./src/core/components/stepper/stepper.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTStepper = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTStepper = /** @class */ (function (_super) {\n    __extends(KTStepper, _super);\n    function KTStepper(element, config) {\n        if (config === void 0) { config = null; }\n        var _this = _super.call(this) || this;\n        _this._name = 'stepper';\n        _this._defaultConfig = {\n            hiddenClass: 'hidden',\n            activeStep: 1\n        };\n        _this._config = _this._defaultConfig;\n        _this._activeStep = 0;\n        _this._nextElement = null;\n        _this._backElement = null;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        if (!_this._element)\n            return _this;\n        _this._nextElement = _this._element.querySelector('[data-stepper-next]');\n        _this._backElement = _this._element.querySelector('[data-stepper-back]');\n        _this._activeStep = 1;\n        if (_this._getOption('activeStep') !== _this._activeStep) {\n            _this._go(_this._getOption('activeStep'));\n        }\n        _this._update();\n        _this._handlers();\n        return _this;\n    }\n    KTStepper.prototype._handlers = function () {\n        var _this = this;\n        if (!this._nextElement) {\n            console.error('data-stepper-next not found');\n            return;\n        }\n        if (this._nextElement) {\n            this._nextElement.addEventListener('click', function (event) {\n                event.preventDefault();\n                _this._goNext();\n            });\n        }\n        if (this._backElement) {\n            this._backElement.addEventListener('click', function (event) {\n                event.preventDefault();\n                _this._goBack();\n            });\n        }\n    };\n    KTStepper.prototype._update = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        var state = '';\n        if (this._activeStep === this._getTotalSteps()) {\n            state = 'last';\n        }\n        else if (this._activeStep === 1) {\n            state = 'first';\n        }\n        else {\n            state = 'between';\n        }\n        this._element.classList.remove('first');\n        this._element.classList.remove('last');\n        this._element.classList.remove('between');\n        this._element.classList.add(state);\n        this._getItemElements().forEach(function (element, index) {\n            var contentElement = dom_1.default.getElement(element.getAttribute('data-stepper-item'));\n            if (!contentElement)\n                return;\n            element.classList.remove('active');\n            element.classList.remove('completed');\n            element.classList.remove('pending');\n            var numberElement = element.querySelector('[data-stepper-number]');\n            if (numberElement)\n                numberElement.innerHTML = String(index + 1);\n            if (index + 1 == _this._activeStep) {\n                element.classList.add('active');\n                contentElement.classList.remove(_this._getOption('hiddenClass'));\n            }\n            else {\n                contentElement.classList.add(_this._getOption('hiddenClass'));\n                if (index + 1 < _this._activeStep) {\n                    element.classList.add('completed');\n                }\n                else {\n                    element.classList.add('pending');\n                }\n            }\n        });\n    };\n    KTStepper.prototype._getItemElements = function () {\n        var elements = [];\n        this._element.querySelectorAll('[data-stepper-item]').forEach(function (element) {\n            if (dom_1.default.isVisible(element)) {\n                elements.push(element);\n            }\n        });\n        return elements;\n    };\n    KTStepper.prototype._go = function (step) {\n        if (step === this._activeStep || step > this._getTotalSteps() || step < 0)\n            return;\n        var payload = { step: step, cancel: false };\n        this._fireEvent('change', payload);\n        this._dispatchEvent('change', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        this._activeStep = step;\n        this._update();\n        this._fireEvent('changed');\n        this._dispatchEvent('changed');\n    };\n    KTStepper.prototype._goTo = function (itemElement) {\n        var step = this._getStep(itemElement);\n        this._go(step);\n    };\n    KTStepper.prototype._getStep = function (itemElement) {\n        var step = -1;\n        this._getItemElements().forEach(function (element, index) {\n            if (element === itemElement) {\n                step = index + 1;\n                return;\n            }\n        });\n        return step;\n    };\n    KTStepper.prototype._getItemElement = function (step) {\n        return this._getItemElements()[step - 1];\n    };\n    KTStepper.prototype._getTotalSteps = function () {\n        return this._getItemElements().length;\n    };\n    KTStepper.prototype._goNext = function () {\n        var step;\n        if (this._getTotalSteps() >= (this._activeStep + 1)) {\n            step = this._activeStep + 1;\n        }\n        else {\n            step = this._getTotalSteps();\n        }\n        this._go(step);\n    };\n    KTStepper.prototype._goBack = function () {\n        var step;\n        if ((this._activeStep - 1) > 1) {\n            step = this._activeStep - 1;\n        }\n        else {\n            step = 1;\n        }\n        this._go(step);\n    };\n    KTStepper.prototype._goLast = function () {\n        var step = this._getTotalSteps();\n        this._go(step);\n    };\n    KTStepper.prototype._goFirst = function () {\n        var step = 1;\n        this._go(step);\n    };\n    KTStepper.prototype._isLast = function () {\n        return this._getTotalSteps() === this._activeStep + 1;\n    };\n    KTStepper.prototype._isFirst = function () {\n        return this._activeStep === 1;\n    };\n    KTStepper.prototype.isLast = function () {\n        return this._isLast();\n    };\n    KTStepper.prototype.isFirst = function () {\n        return this._isFirst();\n    };\n    KTStepper.prototype.go = function (step) {\n        this._go(step);\n    };\n    KTStepper.prototype.goTo = function (itemElement) {\n        this.goTo(itemElement);\n    };\n    KTStepper.prototype.goFirst = function () {\n        this._goFirst();\n    };\n    KTStepper.prototype.goLast = function () {\n        this._goLast();\n    };\n    KTStepper.prototype.goNext = function () {\n        this._goNext();\n    };\n    KTStepper.prototype.goBack = function () {\n        this._goBack();\n    };\n    KTStepper.prototype.update = function () {\n        this._update();\n    };\n    KTStepper.prototype.getStep = function (itemElement) {\n        return this._getStep(itemElement);\n    };\n    KTStepper.prototype.getItemElement = function (step) {\n        return this._getItemElement(step);\n    };\n    KTStepper.prototype.getTotalSteps = function () {\n        return this._getTotalSteps();\n    };\n    KTStepper.prototype.getItemElements = function () {\n        return this._getItemElements();\n    };\n    KTStepper.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'stepper')) {\n            return data_1.default.get(element, 'stepper');\n        }\n        if (element.getAttribute('data-stepper') === \"true\") {\n            return new KTStepper(element);\n        }\n        return null;\n    };\n    KTStepper.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTStepper(element, config);\n    };\n    KTStepper.createInstances = function () {\n        var elements = document.querySelectorAll('[data-stepper=\"true\"]');\n        elements.forEach(function (element) {\n            new KTStepper(element);\n        });\n    };\n    KTStepper.init = function () {\n        KTStepper.createInstances();\n    };\n    return KTStepper;\n}(component_1.default));\nexports.KTStepper = KTStepper;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/stepper/stepper.ts?");

/***/ }),

/***/ "./src/core/components/sticky/index.ts":
/*!*********************************************!*\
  !*** ./src/core/components/sticky/index.ts ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTSticky = void 0;\nvar sticky_1 = __webpack_require__(/*! ./sticky */ \"./src/core/components/sticky/sticky.ts\");\nObject.defineProperty(exports, \"KTSticky\", ({ enumerable: true, get: function () { return sticky_1.KTSticky; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/sticky/index.ts?");

/***/ }),

/***/ "./src/core/components/sticky/sticky.ts":
/*!**********************************************!*\
  !*** ./src/core/components/sticky/sticky.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTSticky = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar utils_1 = __webpack_require__(/*! ../../helpers/utils */ \"./src/core/helpers/utils.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTSticky = /** @class */ (function (_super) {\n    __extends(KTSticky, _super);\n    function KTSticky(element, config) {\n        if (config === void 0) { config = null; }\n        var _this = _super.call(this) || this;\n        _this._name = 'sticky';\n        _this._defaultConfig = {\n            target: 'body',\n            name: '',\n            class: '',\n            top: '',\n            start: '',\n            end: '',\n            width: '',\n            zindex: '',\n            offset: 0,\n            reverse: false,\n            release: '',\n            activate: '',\n        };\n        _this._config = _this._defaultConfig;\n        _this._targetElement = null;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._releaseElement = dom_1.default.getElement(_this._getOption('release'));\n        _this._activateElement = dom_1.default.getElement(_this._getOption('activate'));\n        _this._wrapperElement = _this._element.closest('[data-sticky-wrapper]');\n        _this._attributeRoot = \"data-sticky-\".concat(_this._getOption('name'));\n        _this._eventTriggerState = true;\n        _this._lastScrollTop = 0;\n        var targetElement = _this._getTarget() === 'body' ? document : dom_1.default.getElement(_this._getTarget());\n        if (!targetElement)\n            return _this;\n        _this._targetElement = targetElement;\n        _this._handlers();\n        _this._process();\n        _this._update();\n        return _this;\n    }\n    KTSticky.prototype._getTarget = function () {\n        return (this._element.getAttribute('data-sticky-target') || this._getOption('target'));\n    };\n    KTSticky.prototype._handlers = function () {\n        var _this = this;\n        window.addEventListener('resize', function () {\n            var timer;\n            utils_1.default.throttle(timer, function () {\n                _this._update();\n            }, 200);\n        });\n        this._targetElement.addEventListener('scroll', function () {\n            _this._process();\n        });\n    };\n    KTSticky.prototype._process = function () {\n        var reverse = this._getOption('reverse');\n        var offset = this._getOffset();\n        if (offset < 0) {\n            this._disable();\n            return;\n        }\n        var st = this._getTarget() === 'body' ? dom_1.default.getScrollTop() : this._targetElement.scrollTop;\n        var release = (this._releaseElement && dom_1.default.isPartiallyInViewport(this._releaseElement));\n        // Release on reverse scroll mode\n        if (reverse === true) {\n            // Forward scroll mode\n            if (st > offset && !release) {\n                if (document.body.hasAttribute(this._attributeRoot) === false) {\n                    if (this._enable() === false) {\n                        return;\n                    }\n                    document.body.setAttribute(this._attributeRoot, 'on');\n                }\n                if (this._eventTriggerState === true) {\n                    var payload = { active: true };\n                    this._fireEvent('change', payload);\n                    this._dispatchEvent('change', payload);\n                    this._eventTriggerState = false;\n                }\n                // Back scroll mode\n            }\n            else {\n                if (document.body.hasAttribute(this._attributeRoot) === true) {\n                    this._disable();\n                    if (release) {\n                        this._element.classList.add('release');\n                    }\n                    document.body.removeAttribute(this._attributeRoot);\n                }\n                if (this._eventTriggerState === false) {\n                    var payload = { active: false };\n                    this._fireEvent('change', payload);\n                    this._dispatchEvent('change', payload);\n                    this._eventTriggerState = true;\n                }\n            }\n            this._lastScrollTop = st;\n            // Classic scroll mode\n        }\n        else {\n            // Forward scroll mode\n            if (st > offset && !release) {\n                if (document.body.hasAttribute(this._attributeRoot) === false) {\n                    if (this._enable() === false) {\n                        return;\n                    }\n                    document.body.setAttribute(this._attributeRoot, 'on');\n                }\n                if (this._eventTriggerState === true) {\n                    var payload = { active: true };\n                    this._fireEvent('change', payload);\n                    this._dispatchEvent('change', payload);\n                    this._eventTriggerState = false;\n                }\n                // Back scroll mode\n            }\n            else { // back scroll mode\n                if (document.body.hasAttribute(this._attributeRoot) === true) {\n                    this._disable();\n                    if (release) {\n                        this._element.classList.add('release');\n                    }\n                    document.body.removeAttribute(this._attributeRoot);\n                }\n                if (this._eventTriggerState === false) {\n                    var payload = { active: false };\n                    this._fireEvent('change', payload);\n                    this._dispatchEvent('change', payload);\n                    this._eventTriggerState = true;\n                }\n            }\n        }\n    };\n    KTSticky.prototype._getOffset = function () {\n        var offset = parseInt(this._getOption('offset'));\n        var activateElement = dom_1.default.getElement(this._getOption('activate'));\n        if (activateElement) {\n            offset = Math.abs(offset - activateElement.offsetTop);\n        }\n        return offset;\n    };\n    KTSticky.prototype._enable = function () {\n        if (!this._element)\n            return false;\n        var width = this._getOption('width');\n        var top = this._getOption('top');\n        var start = this._getOption('start');\n        var end = this._getOption('end');\n        var height = this._calculateHeight();\n        var zindex = this._getOption('zindex');\n        var classList = this._getOption('class');\n        if (height + parseInt(top) > dom_1.default.getViewPort().height) {\n            return false;\n        }\n        if (width) {\n            var targetElement = document.querySelector(width);\n            if (targetElement) {\n                width = dom_1.default.getCssProp(targetElement, 'width');\n            }\n            else if (width == 'auto') {\n                width = dom_1.default.getCssProp(this._element, 'width');\n            }\n            this._element.style.width = \"\".concat(Math.round(parseFloat(width)), \"px\");\n        }\n        if (top) {\n            this._element.style.top = \"\".concat(top, \"px\");\n        }\n        if (start) {\n            if (start === 'auto') {\n                var offsetLeft = dom_1.default.offset(this._element).left;\n                if (offsetLeft >= 0) {\n                    this._element.style.insetInlineStart = \"\".concat(offsetLeft, \"px\");\n                }\n            }\n            else {\n                this._element.style.insetInlineStart = \"\".concat(start, \"px\");\n            }\n        }\n        if (end) {\n            if (end === 'auto') {\n                var offseRight = dom_1.default.offset(this._element).right;\n                if (offseRight >= 0) {\n                    this._element.style.insetInlineEnd = \"\".concat(offseRight, \"px\");\n                }\n            }\n            else {\n                this._element.style.insetInlineEnd = \"\".concat(end, \"px\");\n            }\n        }\n        if (zindex) {\n            this._element.style.zIndex = zindex;\n            this._element.style.position = 'fixed';\n        }\n        if (classList) {\n            dom_1.default.addClass(this._element, classList);\n        }\n        if (this._wrapperElement) {\n            this._wrapperElement.style.height = \"\".concat(height, \"px\");\n        }\n        this._element.classList.add('active');\n        this._element.classList.remove('release');\n        return true;\n    };\n    KTSticky.prototype._disable = function () {\n        if (!this._element)\n            return;\n        this._element.style.top = '';\n        this._element.style.width = '';\n        this._element.style.left = '';\n        this._element.style.right = '';\n        this._element.style.zIndex = '';\n        this._element.style.position = '';\n        var classList = this._getOption('class');\n        if (this._wrapperElement) {\n            this._wrapperElement.style.height = '';\n        }\n        if (classList) {\n            dom_1.default.removeClass(this._element, classList);\n        }\n        this._element.classList.remove('active');\n    };\n    KTSticky.prototype._update = function () {\n        if (this._isActive()) {\n            this._disable();\n            this._enable();\n        }\n        else {\n            this._disable();\n        }\n    };\n    KTSticky.prototype._calculateHeight = function () {\n        if (!this._element)\n            return 0;\n        var height = parseFloat(dom_1.default.getCssProp(this._element, 'height'));\n        height += parseFloat(dom_1.default.getCssProp(this._element, 'margin-top'));\n        height += parseFloat(dom_1.default.getCssProp(this._element, 'margin-bottom'));\n        if (dom_1.default.getCssProp(this._element, 'border-top')) {\n            height = height + parseFloat(dom_1.default.getCssProp(this._element, 'border-top'));\n        }\n        if (dom_1.default.getCssProp(this._element, 'border-bottom')) {\n            height = height + parseFloat(dom_1.default.getCssProp(this._element, 'border-bottom'));\n        }\n        return height;\n    };\n    KTSticky.prototype._isActive = function () {\n        return this._element.classList.contains('active');\n    };\n    KTSticky.prototype.update = function () {\n        this._update();\n    };\n    KTSticky.prototype.isActive = function () {\n        return this._isActive();\n    };\n    KTSticky.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'sticky')) {\n            return data_1.default.get(element, 'sticky');\n        }\n        if (element.getAttribute('data-sticky') === \"true\") {\n            return new KTSticky(element);\n        }\n        return null;\n    };\n    KTSticky.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTSticky(element, config);\n    };\n    KTSticky.createInstances = function () {\n        var elements = document.querySelectorAll('[data-sticky=\"true\"]');\n        elements.forEach(function (element) {\n            new KTSticky(element);\n        });\n    };\n    KTSticky.init = function () {\n        KTSticky.createInstances();\n    };\n    return KTSticky;\n}(component_1.default));\nexports.KTSticky = KTSticky;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/sticky/sticky.ts?");

/***/ }),

/***/ "./src/core/components/tabs/index.ts":
/*!*******************************************!*\
  !*** ./src/core/components/tabs/index.ts ***!
  \*******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTTabs = void 0;\nvar tabs_1 = __webpack_require__(/*! ./tabs */ \"./src/core/components/tabs/tabs.ts\");\nObject.defineProperty(exports, \"KTTabs\", ({ enumerable: true, get: function () { return tabs_1.KTTabs; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/tabs/index.ts?");

/***/ }),

/***/ "./src/core/components/tabs/tabs.ts":
/*!******************************************!*\
  !*** ./src/core/components/tabs/tabs.ts ***!
  \******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTTabs = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTTabs = /** @class */ (function (_super) {\n    __extends(KTTabs, _super);\n    function KTTabs(element, config) {\n        var _this = _super.call(this) || this;\n        _this._name = 'tabs';\n        _this._defaultConfig = {\n            hiddenClass: '',\n        };\n        _this._config = _this._defaultConfig;\n        _this._currentTabElement = null;\n        _this._currentContentElement = null;\n        _this._lastTabElement = null;\n        _this._lastContentElement = null;\n        _this._tabElements = null;\n        _this._isTransitioning = false;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        if (!_this._element)\n            return _this;\n        _this._tabElements = _this._element.querySelectorAll('[data-tab-toggle]');\n        _this._currentTabElement = _this._element.querySelector('.active[data-tab-toggle]');\n        _this._currentContentElement = _this._currentTabElement && (dom_1.default.getElement(_this._currentTabElement.getAttribute('data-tab-toggle')) || dom_1.default.getElement(_this._currentTabElement.getAttribute('href'))) || null;\n        _this._handlers();\n        return _this;\n    }\n    KTTabs.prototype._handlers = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        event_handler_1.default.on(this._element, '[data-tab-toggle]', 'click', function (event, target) {\n            event.preventDefault();\n            _this._show(target);\n        });\n    };\n    KTTabs.prototype._show = function (tabElement) {\n        var _this = this;\n        var _a, _b, _c, _d, _e, _f;\n        if (this._isShown(tabElement) || this._isTransitioning)\n            return;\n        var payload = { cancel: false };\n        this._fireEvent('show', payload);\n        this._dispatchEvent('show', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        (_a = this._currentTabElement) === null || _a === void 0 ? void 0 : _a.classList.remove('active');\n        (_b = this._currentContentElement) === null || _b === void 0 ? void 0 : _b.classList.add(this._getOption('hiddenClass'));\n        this._lastTabElement = this._currentTabElement;\n        (_c = this._getDropdownToggleElement(this._lastTabElement)) === null || _c === void 0 ? void 0 : _c.classList.remove('active');\n        this._lastContentElement = this._currentContentElement;\n        this._currentTabElement = tabElement;\n        this._currentContentElement = dom_1.default.getElement(tabElement.getAttribute('data-tab-toggle')) || dom_1.default.getElement(tabElement.getAttribute('href'));\n        (_d = this._currentTabElement) === null || _d === void 0 ? void 0 : _d.classList.add('active');\n        (_e = this._currentContentElement) === null || _e === void 0 ? void 0 : _e.classList.remove(this._getOption('hiddenClass'));\n        (_f = this._getDropdownToggleElement(this._currentTabElement)) === null || _f === void 0 ? void 0 : _f.classList.add('active');\n        this._currentContentElement.style.opacity = '0';\n        dom_1.default.reflow(this._currentContentElement);\n        this._currentContentElement.style.opacity = '1';\n        dom_1.default.transitionEnd(this._currentContentElement, function () {\n            _this._isTransitioning = false;\n            _this._currentContentElement.style.opacity = '';\n            _this._fireEvent('shown');\n            _this._dispatchEvent('shown');\n        });\n    };\n    KTTabs.prototype._getDropdownToggleElement = function (element) {\n        var containerElement = element.closest('.dropdown');\n        if (containerElement) {\n            return containerElement.querySelector('.dropdown-toggle');\n        }\n        else {\n            return null;\n        }\n    };\n    KTTabs.prototype._isShown = function (tabElement) {\n        return tabElement.classList.contains('active');\n    };\n    KTTabs.prototype.isShown = function (tabElement) {\n        return this._isShown(tabElement);\n    };\n    KTTabs.prototype.show = function (tabElement) {\n        return this._show(tabElement);\n    };\n    KTTabs.keyboardArrow = function () {\n    };\n    KTTabs.keyboardJump = function () {\n    };\n    KTTabs.handleAccessibility = function () {\n    };\n    KTTabs.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'tabs')) {\n            return data_1.default.get(element, 'tabs');\n        }\n        if (element.getAttribute('data-tabs') === \"true\") {\n            return new KTTabs(element);\n        }\n        return null;\n    };\n    KTTabs.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTTabs(element, config);\n    };\n    KTTabs.createInstances = function () {\n        var elements = document.querySelectorAll('[data-tabs=\"true\"]');\n        elements.forEach(function (element) {\n            new KTTabs(element);\n        });\n    };\n    KTTabs.init = function () {\n        KTTabs.createInstances();\n        if (window.KT_TABS_INITIALIZED !== true) {\n            KTTabs.handleAccessibility();\n            window.KT_TABS_INITIALIZED = true;\n        }\n    };\n    return KTTabs;\n}(component_1.default));\nexports.KTTabs = KTTabs;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/tabs/tabs.ts?");

/***/ }),

/***/ "./src/core/components/theme/index.ts":
/*!********************************************!*\
  !*** ./src/core/components/theme/index.ts ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTTheme = void 0;\nvar theme_1 = __webpack_require__(/*! ./theme */ \"./src/core/components/theme/theme.ts\");\nObject.defineProperty(exports, \"KTTheme\", ({ enumerable: true, get: function () { return theme_1.KTTheme; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/theme/index.ts?");

/***/ }),

/***/ "./src/core/components/theme/theme.ts":
/*!********************************************!*\
  !*** ./src/core/components/theme/theme.ts ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTTheme = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar event_handler_1 = __webpack_require__(/*! ../../helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTTheme = /** @class */ (function (_super) {\n    __extends(KTTheme, _super);\n    function KTTheme(element, config) {\n        if (config === void 0) { config = null; }\n        var _this = _super.call(this) || this;\n        _this._name = 'theme';\n        _this._defaultConfig = {\n            mode: 'light',\n            class: true,\n            attribute: 'data-theme-mode',\n        };\n        _this._mode = null;\n        _this._currentMode = null;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._setMode((localStorage.getItem('theme') || _this._getOption('mode')));\n        _this._handlers();\n        return _this;\n    }\n    KTTheme.prototype._handlers = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        event_handler_1.default.on(this._element, '[data-theme-toggle=\"true\"]', 'click', function () {\n            _this._toggle();\n        });\n        event_handler_1.default.on(this._element, '[data-theme-switch]', 'click', function (event, target) {\n            event.preventDefault();\n            var mode = target.getAttribute('data-theme-switch');\n            _this._setMode(mode);\n        });\n    };\n    KTTheme.prototype._toggle = function () {\n        var mode = this._currentMode === 'light' ? 'dark' : 'light';\n        this._setMode(mode);\n    };\n    KTTheme.prototype._setMode = function (mode) {\n        if (!this._element)\n            return;\n        var payload = { cancel: false };\n        this._fireEvent('change', payload);\n        this._dispatchEvent('change', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        var currentMode = mode;\n        if (mode === 'system') {\n            currentMode = this._getSystemMode();\n        }\n        this._mode = mode;\n        this._currentMode = currentMode;\n        this._bindMode();\n        this._updateState();\n        localStorage.setItem('theme', this._mode);\n        this._element.setAttribute('data-theme-mode', mode);\n        this._fireEvent('changed', {});\n        this._dispatchEvent('changed', {});\n    };\n    KTTheme.prototype._getMode = function () {\n        return localStorage.getItem('theme') || this._mode;\n    };\n    KTTheme.prototype._getSystemMode = function () {\n        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n    };\n    KTTheme.prototype._bindMode = function () {\n        if (!this._currentMode || !this._element) {\n            return;\n        }\n        if (this._getOption('class')) {\n            this._element.classList.remove('dark');\n            this._element.classList.remove('light');\n            this._element.removeAttribute(this._getOption('attribute'));\n            this._element.classList.add(this._currentMode);\n        }\n        else {\n            this._element.classList.remove(this._currentMode);\n            this._element.setAttribute(this._getOption('attribute'), this._currentMode);\n        }\n    };\n    KTTheme.prototype._updateState = function () {\n        var _this = this;\n        var elements = document.querySelectorAll('input[type=\"checkbox\"][data-theme-state]');\n        elements.forEach(function (element) {\n            if (element.getAttribute('data-theme-state') === _this._mode) {\n                element.checked = true;\n            }\n        });\n    };\n    KTTheme.prototype.getMode = function () {\n        return this._getMode();\n    };\n    KTTheme.prototype.setMode = function (mode) {\n        this.setMode(mode);\n    };\n    KTTheme.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'theme')) {\n            return data_1.default.get(element, 'theme');\n        }\n        if (element.getAttribute('data-theme') !== \"false\") {\n            return new KTTheme(element);\n        }\n        return null;\n    };\n    KTTheme.getOrCreateInstance = function (element, config) {\n        if (element === void 0) { element = document.body; }\n        return this.getInstance(element) || new KTTheme(element, config);\n    };\n    KTTheme.createInstances = function () {\n        var elements = document.querySelectorAll('[data-theme]:not([data-theme=\"false\"]');\n        elements.forEach(function (element) {\n            new KTTheme(element);\n        });\n    };\n    KTTheme.init = function () {\n        KTTheme.createInstances();\n    };\n    return KTTheme;\n}(component_1.default));\nexports.KTTheme = KTTheme;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/theme/theme.ts?");

/***/ }),

/***/ "./src/core/components/toggle-password/index.ts":
/*!******************************************************!*\
  !*** ./src/core/components/toggle-password/index.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTTogglePassword = void 0;\nvar toggle_password_1 = __webpack_require__(/*! ./toggle-password */ \"./src/core/components/toggle-password/toggle-password.ts\");\nObject.defineProperty(exports, \"KTTogglePassword\", ({ enumerable: true, get: function () { return toggle_password_1.KTTogglePassword; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/toggle-password/index.ts?");

/***/ }),

/***/ "./src/core/components/toggle-password/toggle-password.ts":
/*!****************************************************************!*\
  !*** ./src/core/components/toggle-password/toggle-password.ts ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTTogglePassword = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTTogglePassword = /** @class */ (function (_super) {\n    __extends(KTTogglePassword, _super);\n    function KTTogglePassword(element, config) {\n        if (config === void 0) { config = null; }\n        var _this = _super.call(this) || this;\n        _this._name = 'toggle-password';\n        _this._defaultConfig = {\n            permanent: false\n        };\n        _this._config = _this._defaultConfig;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._triggerElement = _this._element.querySelector('[data-toggle-password-trigger]');\n        _this._inputElement = _this._element.querySelector('input');\n        if (!_this._triggerElement || !_this._inputElement) {\n            return _this;\n        }\n        _this._handlers();\n        return _this;\n    }\n    KTTogglePassword.prototype._handlers = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        this._triggerElement.addEventListener('click', function (e) {\n            _this._toggle();\n        });\n        this._inputElement.addEventListener('input', function () {\n            _this._update();\n        });\n    };\n    KTTogglePassword.prototype._toggle = function () {\n        if (!this._element)\n            return;\n        var payload = { cancel: false };\n        this._fireEvent('toggle', payload);\n        this._dispatchEvent('toggle', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (this._isVisible()) {\n            this._element.classList.remove('active');\n            this._setVisible(false);\n        }\n        else {\n            this._element.classList.add('active');\n            this._setVisible(true);\n        }\n        this._fireEvent('toggled');\n        this._dispatchEvent('toggled');\n    };\n    KTTogglePassword.prototype._update = function () {\n        if (!this._element)\n            return;\n        if (this._getOption('permanent') === false) {\n            if (this._isVisible()) {\n                this._setVisible(false);\n            }\n        }\n    };\n    KTTogglePassword.prototype._isVisible = function () {\n        return this._inputElement.getAttribute('type') === 'text';\n    };\n    KTTogglePassword.prototype._setVisible = function (flag) {\n        if (flag) {\n            this._inputElement.setAttribute('type', 'text');\n        }\n        else {\n            this._inputElement.setAttribute('type', 'password');\n        }\n    };\n    KTTogglePassword.prototype.toggle = function () {\n        this._toggle();\n    };\n    KTTogglePassword.prototype.setVisible = function (flag) {\n        this._setVisible(flag);\n    };\n    KTTogglePassword.prototype.isVisible = function () {\n        return this._isVisible();\n    };\n    KTTogglePassword.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'toggle-password')) {\n            return data_1.default.get(element, 'toggle-password');\n        }\n        if (element.getAttribute('data-toggle-password') === \"true\") {\n            return new KTTogglePassword(element);\n        }\n        return null;\n    };\n    KTTogglePassword.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTTogglePassword(element, config);\n    };\n    KTTogglePassword.createInstances = function () {\n        var elements = document.querySelectorAll('[data-toggle-password=\"true\"]');\n        elements.forEach(function (element) {\n            new KTTogglePassword(element);\n        });\n    };\n    KTTogglePassword.init = function () {\n        KTTogglePassword.createInstances();\n    };\n    return KTTogglePassword;\n}(component_1.default));\nexports.KTTogglePassword = KTTogglePassword;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/toggle-password/toggle-password.ts?");

/***/ }),

/***/ "./src/core/components/toggle/index.ts":
/*!*********************************************!*\
  !*** ./src/core/components/toggle/index.ts ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTToggle = void 0;\nvar toggle_1 = __webpack_require__(/*! ./toggle */ \"./src/core/components/toggle/toggle.ts\");\nObject.defineProperty(exports, \"KTToggle\", ({ enumerable: true, get: function () { return toggle_1.KTToggle; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/toggle/index.ts?");

/***/ }),

/***/ "./src/core/components/toggle/toggle.ts":
/*!**********************************************!*\
  !*** ./src/core/components/toggle/toggle.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTToggle = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar KTToggle = /** @class */ (function (_super) {\n    __extends(KTToggle, _super);\n    function KTToggle(element, config) {\n        if (config === void 0) { config = null; }\n        var _this = _super.call(this) || this;\n        _this._name = 'toggle';\n        _this._defaultConfig = {\n            target: '',\n            activeClass: 'active',\n            class: '',\n            removeClass: '',\n            attribute: ''\n        };\n        _this._config = _this._defaultConfig;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._targetElement = _this._getTargetElement();\n        if (!_this._targetElement) {\n            return _this;\n        }\n        _this._handlers();\n        return _this;\n    }\n    KTToggle.prototype._handlers = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        this._element.addEventListener('click', function () {\n            _this._toggle();\n        });\n    };\n    KTToggle.prototype._getTargetElement = function () {\n        return (dom_1.default.getElement(this._element.getAttribute('data-toggle')) ||\n            dom_1.default.getElement(this._getOption('target')));\n    };\n    KTToggle.prototype._toggle = function () {\n        if (!this._element)\n            return;\n        var payload = { cancel: false };\n        this._fireEvent('toggle', payload);\n        this._dispatchEvent('toggle', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        this._element.classList.toggle(this._getOption('activeClass'));\n        this._update();\n        this._fireEvent('toggled');\n        this._dispatchEvent('toggled');\n    };\n    KTToggle.prototype._update = function () {\n        if (!this._targetElement)\n            return;\n        if (this._getOption('removeClass')) {\n            dom_1.default.removeClass(this._targetElement, this._getOption('removeClass'));\n        }\n        if (!this._isActive()) {\n            if (this._getOption('class')) {\n                dom_1.default.addClass(this._targetElement, this._getOption('class'));\n            }\n            if (this._getOption('attribute')) {\n                this._targetElement.setAttribute(this._getOption('attribute'), 'true');\n            }\n        }\n        else {\n            if (this._getOption('class')) {\n                dom_1.default.removeClass(this._targetElement, this._getOption('class'));\n            }\n            if (this._getOption('attribute')) {\n                this._targetElement.removeAttribute(this._getOption('attribute'));\n            }\n        }\n    };\n    KTToggle.prototype._isActive = function () {\n        if (!this._element)\n            return false;\n        return (dom_1.default.hasClass(this._targetElement, this._getOption('class')) ||\n            this._targetElement.hasAttribute(this._getOption('attribute')));\n    };\n    KTToggle.prototype.toggle = function () {\n        this._toggle();\n    };\n    KTToggle.prototype.update = function () {\n        this._update();\n    };\n    KTToggle.prototype.isActive = function () {\n        return this._isActive();\n    };\n    KTToggle.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'toggle')) {\n            return data_1.default.get(element, 'toggle');\n        }\n        if (element.getAttribute('data-toggle') !== \"false\") {\n            return new KTToggle(element);\n        }\n        return null;\n    };\n    KTToggle.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTToggle(element, config);\n    };\n    KTToggle.createInstances = function () {\n        var elements = document.querySelectorAll('[data-toggle]:not([data-toggle=\"false\"])');\n        elements.forEach(function (element) {\n            new KTToggle(element);\n        });\n    };\n    KTToggle.init = function () {\n        KTToggle.createInstances();\n    };\n    return KTToggle;\n}(component_1.default));\nexports.KTToggle = KTToggle;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/toggle/toggle.ts?");

/***/ }),

/***/ "./src/core/components/tooltip/index.ts":
/*!**********************************************!*\
  !*** ./src/core/components/tooltip/index.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTTooltip = void 0;\nvar tooltip_1 = __webpack_require__(/*! ./tooltip */ \"./src/core/components/tooltip/tooltip.ts\");\nObject.defineProperty(exports, \"KTTooltip\", ({ enumerable: true, get: function () { return tooltip_1.KTTooltip; } }));\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/tooltip/index.ts?");

/***/ }),

/***/ "./src/core/components/tooltip/tooltip.ts":
/*!************************************************!*\
  !*** ./src/core/components/tooltip/tooltip.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable max-len */\n/* eslint-disable require-jsdoc */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTTooltip = void 0;\nvar data_1 = __webpack_require__(/*! ../../helpers/data */ \"./src/core/helpers/data.ts\");\nvar dom_1 = __webpack_require__(/*! ../../helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar component_1 = __webpack_require__(/*! ../component */ \"./src/core/components/component.ts\");\nvar core_1 = __webpack_require__(/*! @popperjs/core */ \"./node_modules/@popperjs/core/lib/index.js\");\nvar KTTooltip = /** @class */ (function (_super) {\n    __extends(KTTooltip, _super);\n    function KTTooltip(element, config) {\n        if (config === void 0) { config = null; }\n        var _this = _super.call(this) || this;\n        _this._name = 'tooltip';\n        _this._defaultConfig = {\n            target: '',\n            hiddenClass: 'hidden',\n            trigger: 'hover',\n            placement: 'top',\n            placementRtl: 'top',\n            container: '',\n            strategy: 'fixed',\n            offset: '0, 5px',\n            offsetRtl: '0, 5px',\n            delayShow: 0,\n            delayHide: 0,\n            permanent: false,\n            zindex: '100'\n        };\n        _this._config = _this._defaultConfig;\n        _this._isOpen = false;\n        _this._transitioning = false;\n        if (data_1.default.has(element, _this._name))\n            return _this;\n        _this._init(element);\n        _this._buildConfig(config);\n        _this._targetElement = _this._getTargetElement();\n        if (!_this._targetElement) {\n            return _this;\n        }\n        _this._handlers();\n        return _this;\n    }\n    KTTooltip.prototype._getTargetElement = function () {\n        return (dom_1.default.getElement(this._element.getAttribute('data-tooltip')) ||\n            this._element.querySelector('.tooltip, .popover, [data-tooltip-content]') ||\n            dom_1.default.getElement(this._getOption('target')));\n    };\n    KTTooltip.prototype._handlers = function () {\n        var _this = this;\n        if (!this._element)\n            return;\n        if (this._getOption('trigger') === 'click') {\n            this._element.addEventListener('click', function () { return _this._toggle(); });\n        }\n        if (this._getOption('trigger') === 'focus') {\n            this._element.addEventListener('focus', function () { return _this._toggle(); });\n            this._element.addEventListener('blur', function () { return _this._hide(); });\n        }\n        if (this._getOption('trigger') === 'hover') {\n            this._element.addEventListener('mouseenter', function () { return _this._show(); });\n            this._element.addEventListener('mouseleave', function () { return _this._hide(); });\n        }\n    };\n    KTTooltip.prototype._show = function () {\n        var _this = this;\n        if (this._timeout) {\n            clearTimeout(this._timeout);\n        }\n        if (this._isOpen)\n            return;\n        this._timeout = setTimeout(function () {\n            var payload = { cancel: false };\n            _this._fireEvent('show', payload);\n            _this._dispatchEvent('show', payload);\n            if (payload.cancel === true) {\n                return;\n            }\n            if (!_this._targetElement) {\n                return;\n            }\n            if (!_this._element)\n                return;\n            _this._createPopper();\n            _this._handleContainer();\n            _this._setZindex();\n            _this._targetElement.classList.add('show');\n            _this._targetElement.classList.remove(_this._getOption('hiddenClass'));\n            _this._targetElement.style.opacity = '0';\n            dom_1.default.reflow(_this._targetElement);\n            _this._targetElement.style.opacity = '1';\n            _this._transitioning = true;\n            _this._isOpen = true;\n            dom_1.default.transitionEnd(_this._targetElement, function () {\n                _this._targetElement.style.opacity = '';\n                _this._transitioning = false;\n                _this._fireEvent('shown');\n                _this._dispatchEvent('shown');\n            });\n        }, this._getOption('delayShow'));\n    };\n    KTTooltip.prototype._hide = function () {\n        var _this = this;\n        if (this._timeout) {\n            clearTimeout(this._timeout);\n        }\n        if (!this._isOpen)\n            return;\n        this._timeout = setTimeout(function () {\n            var payload = { cancel: false };\n            _this._fireEvent('hide', payload);\n            _this._dispatchEvent('hide', payload);\n            if (payload.cancel === true) {\n                return;\n            }\n            if (!_this._targetElement) {\n                return;\n            }\n            _this._targetElement.style.opacity = '1';\n            dom_1.default.reflow(_this._targetElement);\n            _this._targetElement.style.opacity = '0';\n            _this._transitioning = true;\n            _this._isOpen = false;\n            dom_1.default.transitionEnd(_this._targetElement, function () {\n                _this._popper.destroy();\n                _this._targetElement.classList.remove('show');\n                _this._targetElement.classList.add(_this._getOption('hiddenClass'));\n                _this._targetElement.style.opacity = '';\n                _this._transitioning = false;\n                _this._fireEvent('hidden');\n                _this._dispatchEvent('hidden');\n            });\n        }, this._getOption('delayHide'));\n    };\n    KTTooltip.prototype._toggle = function () {\n        var payload = { cancel: false };\n        this._fireEvent('toggle', payload);\n        this._dispatchEvent('toggle', payload);\n        if (payload.cancel === true) {\n            return;\n        }\n        if (this._isOpen) {\n            this._hide();\n        }\n        else {\n            this._show();\n        }\n    };\n    KTTooltip.prototype._createPopper = function () {\n        if (!this._element)\n            return;\n        var isRtl = dom_1.default.isRTL();\n        // Placement\n        var placement = this._getOption('placement');\n        if (isRtl && this._getOption('placementRtl')) {\n            placement = this._getOption('placementRtl');\n        }\n        // Offset\n        var offsetValue = this._getOption('offset');\n        if (isRtl && this._getOption('offsetRtl')) {\n            offsetValue = this._getOption('offsetRtl');\n        }\n        var offset = offsetValue ? offsetValue.toString().split(',').map(function (value) { return parseInt(value.trim(), 10); }) : [0, 0];\n        if (!this._targetElement) {\n            return;\n        }\n        this._popper = (0, core_1.createPopper)(this._element, this._targetElement, {\n            placement: placement,\n            strategy: this._getOption('strategy'),\n            modifiers: [\n                {\n                    name: 'offset',\n                    options: {\n                        offset: offset\n                    }\n                }\n            ]\n        });\n    };\n    KTTooltip.prototype._handleContainer = function () {\n        var _a;\n        if (this._getOption('container')) {\n            if (this._getOption('container') === 'body') {\n                document.body.appendChild(this._targetElement);\n            }\n            else {\n                (_a = document.querySelector(this._getOption('container'))) === null || _a === void 0 ? void 0 : _a.appendChild(this._targetElement);\n            }\n        }\n    };\n    KTTooltip.prototype._setZindex = function () {\n        var zindex = parseInt(this._getOption('zindex'));\n        if (parseInt(dom_1.default.getCssProp(this._element, 'z-index')) > zindex) {\n            zindex = parseInt(dom_1.default.getCssProp(this._element, 'z-index'));\n        }\n        if (dom_1.default.getHighestZindex(this._element) > zindex) {\n            zindex = dom_1.default.getHighestZindex(this._element) + 1;\n        }\n        this._targetElement.style.zIndex = String(zindex);\n    };\n    KTTooltip.prototype.show = function () {\n        this._show();\n    };\n    KTTooltip.prototype.hide = function () {\n        this._hide();\n    };\n    KTTooltip.prototype.toggle = function () {\n        this._toggle();\n    };\n    KTTooltip.prototype.getContentElement = function () {\n        return this._targetElement;\n    };\n    KTTooltip.prototype.isOpen = function () {\n        return this._isOpen;\n    };\n    KTTooltip.prototype.getTriggerOption = function () {\n        return this._getOption('trigger');\n    };\n    KTTooltip.prototype.isPermanent = function () {\n        return this._getOption('permanent');\n    };\n    KTTooltip.initHandlers = function () {\n        document.addEventListener('click', function (event) {\n            document.querySelectorAll('[data-tooltip]').forEach(function (tooltipElement) {\n                var tooltip = KTTooltip.getInstance(tooltipElement);\n                if (tooltip && tooltip.isOpen() && tooltip.getTriggerOption() !== 'hover' && !tooltip.isPermanent()) {\n                    var contentElement = tooltip.getContentElement();\n                    if (contentElement && (contentElement === event.target || contentElement.contains(event.target))) {\n                        return;\n                    }\n                    else {\n                        tooltip.hide();\n                    }\n                }\n            });\n        });\n    };\n    KTTooltip.getInstance = function (element) {\n        if (!element)\n            return null;\n        if (data_1.default.has(element, 'tooltip')) {\n            return data_1.default.get(element, 'tooltip');\n        }\n        if (element.getAttribute('data-tooltip') !== \"false\") {\n            return new KTTooltip(element);\n        }\n        return null;\n    };\n    KTTooltip.getOrCreateInstance = function (element, config) {\n        return this.getInstance(element) || new KTTooltip(element, config);\n    };\n    KTTooltip.createInstances = function () {\n        document.querySelectorAll('[data-tooltip]:not([data-tooltip=\"false\"]').forEach(function (element) {\n            new KTTooltip(element);\n        });\n    };\n    KTTooltip.init = function () {\n        KTTooltip.createInstances();\n        if (window.KT_TOOLTIP_INITIALIZED !== true) {\n            KTTooltip.initHandlers();\n            window.KT_TOOLTIP_INITIALIZED = true;\n        }\n    };\n    return KTTooltip;\n}(component_1.default));\nexports.KTTooltip = KTTooltip;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/components/tooltip/tooltip.ts?");

/***/ }),

/***/ "./src/core/helpers/data.ts":
/*!**********************************!*\
  !*** ./src/core/helpers/data.ts ***!
  \**********************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar KTElementMap = new Map();\nvar KTData = {\n    set: function (element, key, value) {\n        if (!KTElementMap.has(element)) {\n            KTElementMap.set(element, new Map());\n        }\n        var valueMap = KTElementMap.get(element);\n        valueMap.set(key, value);\n    },\n    get: function (element, key) {\n        if (KTElementMap.has(element)) {\n            return KTElementMap.get(element).get(key) || null;\n        }\n        return null;\n    },\n    has: function (element, key) {\n        return KTElementMap.has(element) && KTElementMap.get(element).has(key);\n    },\n    remove: function (element, key) {\n        if (!KTElementMap.has(element) || !KTElementMap.get(element).has(key)) {\n            return;\n        }\n        var valueMap = KTElementMap.get(element);\n        valueMap.delete(key);\n        if (valueMap.size === 0) {\n            KTElementMap.delete(element);\n        }\n    }\n};\nexports[\"default\"] = KTData;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/helpers/data.ts?");

/***/ }),

/***/ "./src/core/helpers/dom.ts":
/*!*********************************!*\
  !*** ./src/core/helpers/dom.ts ***!
  \*********************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/* eslint-disable max-len */\nvar utils_1 = __webpack_require__(/*! ./utils */ \"./src/core/helpers/utils.ts\");\nvar KTDom = {\n    isRTL: function () {\n        var htmlTag = document.documentElement; // Access the <html> tag\n        // Check if the \"dir\" attribute is present and its value is \"rtl\"\n        var dir = htmlTag.getAttribute('dir');\n        return dir === 'rtl';\n    },\n    isElement: function (element) {\n        if (element && element instanceof HTMLElement) {\n            return true;\n        }\n        else {\n            return false;\n        }\n    },\n    getElement: function (element) {\n        if (this.isElement(element)) {\n            return element;\n        }\n        if (element && element.length > 0) {\n            return document.querySelector(utils_1.default.parseSelector(element));\n        }\n        return null;\n    },\n    remove: function (element) {\n        if (this.isElement(element) && element.parentNode) {\n            element.parentNode.removeChild(element);\n        }\n    },\n    hasClass: function (element, className) {\n        // Split classNames string into an array of individual class names\n        var classes = className.split(' ');\n        // Loop through each class name\n        for (var _i = 0, classes_1 = classes; _i < classes_1.length; _i++) {\n            var className_1 = classes_1[_i];\n            // Check if the element has the current class name\n            if (!element.classList.contains(className_1)) {\n                // Return false if any class is missing\n                return false;\n            }\n        }\n        // Return true if all classes are present\n        return true;\n    },\n    addClass: function (element, className) {\n        var classNames = className.split(' ');\n        if (element.classList) {\n            for (var i = 0; i < classNames.length; i++) {\n                if (classNames[i] && classNames[i].length > 0) {\n                    element.classList.add(classNames[i].trim());\n                }\n            }\n        }\n        else if (!this.hasClass(element, className)) {\n            for (var x = 0; x < classNames.length; x++) {\n                element.className += ' ' + classNames[x].trim();\n            }\n        }\n    },\n    removeClass: function (element, className) {\n        var classNames = className.split(' ');\n        if (element.classList) {\n            for (var i = 0; i < classNames.length; i++) {\n                element.classList.remove(classNames[i].trim());\n            }\n        }\n        else if (this.hasClass(element, className)) {\n            for (var x = 0; x < classNames.length; x++) {\n                element.className = element.className.replace(new RegExp('\\\\b' + classNames[x].trim() + '\\\\b', 'g'), '');\n            }\n        }\n    },\n    getCssProp: function (element, prop) {\n        return (element ? window.getComputedStyle(element).getPropertyValue(prop) : '').replace(' ', '');\n    },\n    setCssProp: function (element, prop, value) {\n        if (element) {\n            window.getComputedStyle(element).setProperty(prop, value);\n        }\n    },\n    offsetOld: function (element) {\n        if (!element)\n            return { top: 0, left: 0, right: 0, bottom: 0 };\n        // Return zeros for disconnected and hidden (display: none) elements (gh-2310)\n        // Support: IE <=11 only\n        // Running getBoundingClientRect on a\n        // disconnected node in IE throws an error\n        var rect = element.getBoundingClientRect();\n        var view = element.ownerDocument.defaultView;\n        return {\n            top: rect.top + ((view === null || view === void 0 ? void 0 : view.scrollY) || 0),\n            left: rect.left,\n            right: window.innerWidth - rect.right,\n            bottom: 0\n        };\n    },\n    offset: function (element) {\n        if (!element)\n            return { top: 0, left: 0, right: 0, bottom: 0 };\n        var rect = element.getBoundingClientRect();\n        return {\n            top: rect.top,\n            left: rect.left,\n            right: window.innerWidth - rect.right,\n            bottom: window.innerHeight - rect.top,\n        };\n    },\n    getIndex: function (element) {\n        var _a;\n        var children = Array.from(((_a = element.parentNode) === null || _a === void 0 ? void 0 : _a.children) || []);\n        return children.indexOf(element);\n    },\n    parents: function (element, selector) {\n        var parents = [];\n        // Push each parent element to the array\n        for (element && element !== document.documentElement; (element = element.parentElement);) {\n            if (selector) {\n                if (element.matches(selector)) {\n                    parents.push(element);\n                }\n                continue;\n            }\n            parents.push(element);\n        }\n        // Return our parent array\n        return parents;\n    },\n    siblings: function (element) {\n        var parent = element.parentNode;\n        if (!parent)\n            return [];\n        return Array.from(parent.children).filter(function (child) { return child !== element; });\n    },\n    children: function (element, selector) {\n        if (!element || !element.childNodes) {\n            return null;\n        }\n        var result = [];\n        var l = element.childNodes.length;\n        var i = 0;\n        for (i = 0; i < l; i++) {\n            if (element.childNodes[i].nodeType == 1 && element.childNodes[i].matches(selector)) {\n                result.push(element.childNodes[i]);\n            }\n        }\n        return result;\n    },\n    child: function (element, selector) {\n        var children = KTDom.children(element, selector);\n        return children ? children[0] : null;\n    },\n    isVisible: function (element) {\n        if (!this.isElement(element) || element.getClientRects().length === 0) {\n            return false;\n        }\n        // eslint-disable-next-line max-len\n        return getComputedStyle(element).getPropertyValue('visibility') === 'visible';\n    },\n    isDisabled: function (element) {\n        if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n            return true;\n        }\n        if (element.classList.contains('disabled')) {\n            return true;\n        }\n        if (typeof element.disabled !== 'undefined') {\n            return element.disabled;\n        }\n        return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false';\n    },\n    transitionEnd: function (element, callback) {\n        var duration = this.getCSSTransitionDuration(element);\n        setTimeout(function () {\n            callback();\n        }, duration);\n    },\n    animationEnd: function (element, callback) {\n        var duration = this.getCSSAnimationDuration(element);\n        setTimeout(function () {\n            callback();\n        }, duration);\n    },\n    getCSSTransitionDuration: function (element) {\n        return (parseFloat(window.getComputedStyle(element).transitionDuration)) * 1000;\n    },\n    getCSSAnimationDuration: function (element) {\n        return (parseFloat(window.getComputedStyle(element).animationDuration)) * 1000;\n    },\n    reflow: function (element) {\n        element.offsetHeight;\n    },\n    insertAfter: function (element, referenceNode) {\n        var parentNode = referenceNode.parentNode;\n        if (parentNode) {\n            parentNode.insertBefore(element, referenceNode.nextSibling);\n        }\n    },\n    getHighestZindex: function (element) {\n        var position, value;\n        while (element && element !== document.documentElement) {\n            // Ignore z-index if position is set to a value where z-index is ignored by the browser\n            // This makes behavior of this function consistent across browsers\n            // WebKit always returns auto if the element is positioned\n            position = element.style.position;\n            if (position === \"absolute\" || position === \"relative\" || position === \"fixed\") {\n                // IE returns 0 when zIndex is not specified\n                // other browsers return a string\n                // we ignore the case of nested elements with an explicit value of 0\n                // <div style=\"z-index: -10;\"><div style=\"z-index: 0;\"></div></div>\n                value = parseInt(element.style.zIndex);\n                if (!isNaN(value) && value !== 0) {\n                    return value;\n                }\n            }\n            element = element.parentNode;\n        }\n        return 1;\n    },\n    isParentOrElementHidden: function (element) {\n        if (!element) {\n            return false;\n        }\n        var computedStyle = window.getComputedStyle(element);\n        if (computedStyle.display === 'none') {\n            return true;\n        }\n        return this.isParentOrElementHidden(element.parentElement);\n    },\n    getViewPort: function () {\n        return {\n            width: window.innerWidth,\n            height: window.innerHeight\n        };\n    },\n    getScrollTop: function () {\n        return (document.scrollingElement || document.documentElement).scrollTop;\n    },\n    isInViewport: function (element) {\n        var rect = element.getBoundingClientRect();\n        return (rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth));\n    },\n    isPartiallyInViewport: function (element) {\n        var x = element.getBoundingClientRect().left;\n        var y = element.getBoundingClientRect().top;\n        var ww = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n        var hw = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n        var w = element.clientWidth;\n        var h = element.clientHeight;\n        return ((y < hw && y + h > 0) && (x < ww && x + w > 0));\n    },\n    isVisibleInParent: function (child, parent) {\n        var childRect = child.getBoundingClientRect();\n        var parentRect = parent.getBoundingClientRect();\n        // Check if the child element is visible\n        if (child.offsetParent === null || getComputedStyle(child).visibility === 'hidden' || getComputedStyle(child).display === 'none') {\n            return false;\n        }\n        // Check if the child is within the vertical bounds of the parent\n        var isVisibleVertically = childRect.top >= parentRect.top && childRect.bottom <= parentRect.bottom;\n        // Check if the child is within the horizontal bounds of the parent\n        var isVisibleHorizontally = childRect.left >= parentRect.left && childRect.right <= parentRect.right;\n        return isVisibleVertically && isVisibleHorizontally;\n    },\n    getRelativeTopPosition: function (child, parent) {\n        var childRect = child.getBoundingClientRect();\n        var parentRect = parent.getBoundingClientRect();\n        // Calculate the relative top position\n        var relativeTop = childRect.top - parentRect.top;\n        return relativeTop;\n    },\n    getDataAttributes: function (element, prefix) {\n        if (!element) {\n            return {};\n        }\n        prefix = utils_1.default.camelCase(prefix);\n        var attributes = {};\n        var keys = Object.keys(element.dataset).filter(function (key) { return key.startsWith(prefix); });\n        for (var _i = 0, keys_1 = keys; _i < keys_1.length; _i++) {\n            var key = keys_1[_i];\n            var normalizedKey = key.replace(prefix, '');\n            normalizedKey = utils_1.default.uncapitalize(normalizedKey);\n            attributes[normalizedKey] = utils_1.default.parseDataAttribute(element.dataset[key]);\n        }\n        return attributes;\n    },\n    ready: function (callback) {\n        if (document.readyState === 'loading') {\n            document.addEventListener('DOMContentLoaded', function () {\n                callback();\n            });\n            document.addEventListener(\"livewire:navigated\", function () {\n                callback();\n            });\n        }\n        else {\n            callback();\n        }\n    }\n};\nexports[\"default\"] = KTDom;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/helpers/dom.ts?");

/***/ }),

/***/ "./src/core/helpers/event-handler.ts":
/*!*******************************************!*\
  !*** ./src/core/helpers/event-handler.ts ***!
  \*******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar utils_1 = __webpack_require__(/*! ./utils */ \"./src/core/helpers/utils.ts\");\nvar KTDelegatedEventHandlers = {};\nvar KTEventHandler = {\n    on: function (element, selector, eventName, handler) {\n        var _this = this;\n        if (element === null) {\n            return null;\n        }\n        var eventId = utils_1.default.geUID('event');\n        KTDelegatedEventHandlers[eventId] = function (event) {\n            var targets = element.querySelectorAll(selector);\n            var target = event.target;\n            while (target && target !== element) {\n                for (var i = 0, j = targets.length; i < j; i++) {\n                    if (target === targets[i]) {\n                        handler.call(_this, event, target);\n                    }\n                }\n                target = target.parentNode;\n            }\n        };\n        element.addEventListener(eventName, KTDelegatedEventHandlers[eventId]);\n        return eventId;\n    },\n    off: function (element, eventName, eventId) {\n        if (!element || KTDelegatedEventHandlers[eventId] === null) {\n            return;\n        }\n        element.removeEventListener(eventName, KTDelegatedEventHandlers[eventId]);\n        delete KTDelegatedEventHandlers[eventId];\n    }\n};\nexports[\"default\"] = KTEventHandler;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/helpers/event-handler.ts?");

/***/ }),

/***/ "./src/core/helpers/utils.ts":
/*!***********************************!*\
  !*** ./src/core/helpers/utils.ts ***!
  \***********************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar KTUtils = {\n    geUID: function (prefix) {\n        if (prefix === void 0) { prefix = ''; }\n        return prefix + Math.floor(Math.random() * (new Date()).getTime());\n    },\n    getBreakpoint: function (breakpoint) {\n        var value = KTUtils.getCssVar(\"--tw-\".concat(breakpoint));\n        if (value) {\n            return parseInt(value.trim());\n        }\n        else {\n            return -1;\n        }\n    },\n    getCssVar: function (variable) {\n        var hex = getComputedStyle(document.documentElement).getPropertyValue(variable);\n        if (hex && hex.length > 0) {\n            hex = hex.trim();\n        }\n        return hex;\n    },\n    parseDataAttribute: function (value) {\n        if (value === 'true') {\n            return true;\n        }\n        if (value === 'false') {\n            return false;\n        }\n        if (value === Number(value).toString()) {\n            return Number(value);\n        }\n        if (value === '' || value === 'null') {\n            return null;\n        }\n        if (typeof value !== 'string') {\n            return value;\n        }\n        try {\n            return KTUtils.parseJson(value);\n        }\n        catch (_a) {\n            return value;\n        }\n    },\n    parseJson: function (value) {\n        return value && value.length > 0 ? JSON.parse(decodeURIComponent(value)) : null;\n    },\n    parseSelector: function (selector) {\n        if (selector && window.CSS && window.CSS.escape) {\n            // Escape any IDs in the selector using CSS.escape\n            selector = selector.replace(/#([^\\s\"#']+)/g, function (match, id) { return \"#\".concat(window.CSS.escape(id)); });\n        }\n        return selector;\n    },\n    capitalize: function (value) {\n        return value.charAt(0).toUpperCase() + value.slice(1);\n    },\n    uncapitalize: function (value) {\n        return value.charAt(0).toLowerCase() + value.slice(1);\n    },\n    camelCase: function (value) {\n        return value.replace(/-([a-z])/g, function (match, letter) {\n            return letter.toUpperCase();\n        });\n    },\n    camelReverseCase: function (str) {\n        return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n    },\n    isRTL: function () {\n        var htmlElement = document.querySelector('html');\n        return Boolean(htmlElement && htmlElement.getAttribute('direction') === 'rtl');\n    },\n    throttle: function (timer, func, delay) {\n        // If setTimeout is already scheduled, no need to do anything\n        if (timer) {\n            return;\n        }\n        // Schedule a setTimeout after delay seconds\n        timer = setTimeout(function () {\n            func();\n            // Once setTimeout function execution is finished, timerId = undefined so that in <br>\n            // the next scroll event function execution can be scheduled by the setTimeout\n            clearTimeout(timer);\n        }, delay);\n    },\n    checksum: function (value) {\n        var hash = 0;\n        for (var i = 0; i < value.length; i++) {\n            hash = ((hash << 5) - hash + value.charCodeAt(i)) | 0;\n        }\n        return ('0000000' + (hash >>> 0).toString(16)).slice(-8);\n    },\n};\nexports[\"default\"] = KTUtils;\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/helpers/utils.ts?");

/***/ }),

/***/ "./src/core/index.ts":
/*!***************************!*\
  !*** ./src/core/index.ts ***!
  \***************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n* Metronic\n* @author: Keenthemes\n* Copyright 2024 Keenthemes\n*/\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KTDataTable = exports.KTTogglePassword = exports.KTImageInput = exports.KTTheme = exports.KTStepper = exports.KTTooltip = exports.KTToggle = exports.KTReparent = exports.KTSticky = exports.KTScrollto = exports.KTScrollable = exports.KTScrollspy = exports.KTAccordion = exports.KTTabs = exports.KTDismiss = exports.KTCollapse = exports.KTDrawer = exports.KTModal = exports.KTDropdown = exports.KTMenu = void 0;\nvar dom_1 = __webpack_require__(/*! ./helpers/dom */ \"./src/core/helpers/dom.ts\");\nvar utils_1 = __webpack_require__(/*! ./helpers/utils */ \"./src/core/helpers/utils.ts\");\nvar event_handler_1 = __webpack_require__(/*! ./helpers/event-handler */ \"./src/core/helpers/event-handler.ts\");\nvar menu_1 = __webpack_require__(/*! ./components/menu */ \"./src/core/components/menu/index.ts\");\nvar dropdown_1 = __webpack_require__(/*! ./components/dropdown */ \"./src/core/components/dropdown/index.ts\");\nvar modal_1 = __webpack_require__(/*! ./components/modal */ \"./src/core/components/modal/index.ts\");\nvar drawer_1 = __webpack_require__(/*! ./components/drawer */ \"./src/core/components/drawer/index.ts\");\nvar collapse_1 = __webpack_require__(/*! ./components/collapse */ \"./src/core/components/collapse/index.ts\");\nvar dismiss_1 = __webpack_require__(/*! ./components/dismiss */ \"./src/core/components/dismiss/index.ts\");\nvar tabs_1 = __webpack_require__(/*! ./components/tabs */ \"./src/core/components/tabs/index.ts\");\nvar accordion_1 = __webpack_require__(/*! ./components/accordion */ \"./src/core/components/accordion/index.ts\");\nvar scrollspy_1 = __webpack_require__(/*! ./components/scrollspy */ \"./src/core/components/scrollspy/index.ts\");\nvar scrollable_1 = __webpack_require__(/*! ./components/scrollable */ \"./src/core/components/scrollable/index.ts\");\nvar scrollto_1 = __webpack_require__(/*! ./components/scrollto */ \"./src/core/components/scrollto/index.ts\");\nvar sticky_1 = __webpack_require__(/*! ./components/sticky */ \"./src/core/components/sticky/index.ts\");\nvar reparent_1 = __webpack_require__(/*! ./components/reparent */ \"./src/core/components/reparent/index.ts\");\nvar toggle_1 = __webpack_require__(/*! ./components/toggle */ \"./src/core/components/toggle/index.ts\");\nvar tooltip_1 = __webpack_require__(/*! ./components/tooltip */ \"./src/core/components/tooltip/index.ts\");\nvar stepper_1 = __webpack_require__(/*! ./components/stepper */ \"./src/core/components/stepper/index.ts\");\nvar theme_1 = __webpack_require__(/*! ./components/theme */ \"./src/core/components/theme/index.ts\");\nvar image_input_1 = __webpack_require__(/*! ./components/image-input */ \"./src/core/components/image-input/index.ts\");\nvar toggle_password_1 = __webpack_require__(/*! ./components/toggle-password */ \"./src/core/components/toggle-password/index.ts\");\nvar datatable_1 = __webpack_require__(/*! ./components/datatable */ \"./src/core/components/datatable/index.ts\");\nvar menu_2 = __webpack_require__(/*! ./components/menu */ \"./src/core/components/menu/index.ts\");\nObject.defineProperty(exports, \"KTMenu\", ({ enumerable: true, get: function () { return menu_2.KTMenu; } }));\nvar dropdown_2 = __webpack_require__(/*! ./components/dropdown */ \"./src/core/components/dropdown/index.ts\");\nObject.defineProperty(exports, \"KTDropdown\", ({ enumerable: true, get: function () { return dropdown_2.KTDropdown; } }));\nvar modal_2 = __webpack_require__(/*! ./components/modal */ \"./src/core/components/modal/index.ts\");\nObject.defineProperty(exports, \"KTModal\", ({ enumerable: true, get: function () { return modal_2.KTModal; } }));\nvar drawer_2 = __webpack_require__(/*! ./components/drawer */ \"./src/core/components/drawer/index.ts\");\nObject.defineProperty(exports, \"KTDrawer\", ({ enumerable: true, get: function () { return drawer_2.KTDrawer; } }));\nvar collapse_2 = __webpack_require__(/*! ./components/collapse */ \"./src/core/components/collapse/index.ts\");\nObject.defineProperty(exports, \"KTCollapse\", ({ enumerable: true, get: function () { return collapse_2.KTCollapse; } }));\nvar dismiss_2 = __webpack_require__(/*! ./components/dismiss */ \"./src/core/components/dismiss/index.ts\");\nObject.defineProperty(exports, \"KTDismiss\", ({ enumerable: true, get: function () { return dismiss_2.KTDismiss; } }));\nvar tabs_2 = __webpack_require__(/*! ./components/tabs */ \"./src/core/components/tabs/index.ts\");\nObject.defineProperty(exports, \"KTTabs\", ({ enumerable: true, get: function () { return tabs_2.KTTabs; } }));\nvar accordion_2 = __webpack_require__(/*! ./components/accordion */ \"./src/core/components/accordion/index.ts\");\nObject.defineProperty(exports, \"KTAccordion\", ({ enumerable: true, get: function () { return accordion_2.KTAccordion; } }));\nvar scrollspy_2 = __webpack_require__(/*! ./components/scrollspy */ \"./src/core/components/scrollspy/index.ts\");\nObject.defineProperty(exports, \"KTScrollspy\", ({ enumerable: true, get: function () { return scrollspy_2.KTScrollspy; } }));\nvar scrollable_2 = __webpack_require__(/*! ./components/scrollable */ \"./src/core/components/scrollable/index.ts\");\nObject.defineProperty(exports, \"KTScrollable\", ({ enumerable: true, get: function () { return scrollable_2.KTScrollable; } }));\nvar scrollto_2 = __webpack_require__(/*! ./components/scrollto */ \"./src/core/components/scrollto/index.ts\");\nObject.defineProperty(exports, \"KTScrollto\", ({ enumerable: true, get: function () { return scrollto_2.KTScrollto; } }));\nvar sticky_2 = __webpack_require__(/*! ./components/sticky */ \"./src/core/components/sticky/index.ts\");\nObject.defineProperty(exports, \"KTSticky\", ({ enumerable: true, get: function () { return sticky_2.KTSticky; } }));\nvar reparent_2 = __webpack_require__(/*! ./components/reparent */ \"./src/core/components/reparent/index.ts\");\nObject.defineProperty(exports, \"KTReparent\", ({ enumerable: true, get: function () { return reparent_2.KTReparent; } }));\nvar toggle_2 = __webpack_require__(/*! ./components/toggle */ \"./src/core/components/toggle/index.ts\");\nObject.defineProperty(exports, \"KTToggle\", ({ enumerable: true, get: function () { return toggle_2.KTToggle; } }));\nvar tooltip_2 = __webpack_require__(/*! ./components/tooltip */ \"./src/core/components/tooltip/index.ts\");\nObject.defineProperty(exports, \"KTTooltip\", ({ enumerable: true, get: function () { return tooltip_2.KTTooltip; } }));\nvar stepper_2 = __webpack_require__(/*! ./components/stepper */ \"./src/core/components/stepper/index.ts\");\nObject.defineProperty(exports, \"KTStepper\", ({ enumerable: true, get: function () { return stepper_2.KTStepper; } }));\nvar theme_2 = __webpack_require__(/*! ./components/theme */ \"./src/core/components/theme/index.ts\");\nObject.defineProperty(exports, \"KTTheme\", ({ enumerable: true, get: function () { return theme_2.KTTheme; } }));\nvar image_input_2 = __webpack_require__(/*! ./components/image-input */ \"./src/core/components/image-input/index.ts\");\nObject.defineProperty(exports, \"KTImageInput\", ({ enumerable: true, get: function () { return image_input_2.KTImageInput; } }));\nvar toggle_password_2 = __webpack_require__(/*! ./components/toggle-password */ \"./src/core/components/toggle-password/index.ts\");\nObject.defineProperty(exports, \"KTTogglePassword\", ({ enumerable: true, get: function () { return toggle_password_2.KTTogglePassword; } }));\nvar datatable_2 = __webpack_require__(/*! ./components/datatable */ \"./src/core/components/datatable/index.ts\");\nObject.defineProperty(exports, \"KTDataTable\", ({ enumerable: true, get: function () { return datatable_2.KTDataTable; } }));\nvar KTComponents = {\n    init: function () {\n        menu_1.KTMenu.init();\n        dropdown_1.KTDropdown.init();\n        modal_1.KTModal.init();\n        drawer_1.KTDrawer.init();\n        collapse_1.KTCollapse.init();\n        dismiss_1.KTDismiss.init();\n        tabs_1.KTTabs.init();\n        accordion_1.KTAccordion.init();\n        scrollspy_1.KTScrollspy.init();\n        scrollable_1.KTScrollable.init();\n        scrollto_1.KTScrollto.init();\n        sticky_1.KTSticky.init();\n        reparent_1.KTReparent.init();\n        toggle_1.KTToggle.init();\n        tooltip_1.KTTooltip.init();\n        stepper_1.KTStepper.init();\n        theme_1.KTTheme.init();\n        image_input_1.KTImageInput.init();\n        toggle_password_1.KTTogglePassword.init();\n        datatable_1.KTDataTable.init();\n    }\n};\nwindow.KTUtils = utils_1.default;\nwindow.KTDom = dom_1.default;\nwindow.KTEventHandler = event_handler_1.default;\nwindow.KTMenu = menu_1.KTMenu;\nwindow.KTDropdown = dropdown_1.KTDropdown;\nwindow.KTModal = modal_1.KTModal;\nwindow.KTDrawer = drawer_1.KTDrawer;\nwindow.KTCollapse = collapse_1.KTCollapse;\nwindow.KTDismiss = dismiss_1.KTDismiss;\nwindow.KTTabs = tabs_1.KTTabs;\nwindow.KTAccordion = accordion_1.KTAccordion;\nwindow.KTScrollspy = scrollspy_1.KTScrollspy;\nwindow.KTScrollable = scrollable_1.KTScrollable;\nwindow.KTScrollto = scrollto_1.KTScrollto;\nwindow.KTSticky = sticky_1.KTSticky;\nwindow.KTReparent = reparent_1.KTReparent;\nwindow.KTToggle = toggle_1.KTToggle;\nwindow.KTTooltip = tooltip_1.KTTooltip;\nwindow.KTStepper = stepper_1.KTStepper;\nwindow.KTTheme = theme_1.KTTheme;\nwindow.KTImageInput = image_input_1.KTImageInput;\nwindow.KTTogglePassword = toggle_password_1.KTTogglePassword;\nwindow.KTDataTable = datatable_1.KTDataTable;\nwindow.KTComponents = KTComponents;\nexports[\"default\"] = KTComponents;\ndom_1.default.ready(function () {\n    KTComponents.init();\n});\n\n\n//# sourceURL=webpack://metronic-tailwind-html/./src/core/index.ts?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./src/app/datatables/allowed-ip-addresses.ts");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});