<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('areas', function (Blueprint $table) {
            // Drop the unique constraint first
            $table->dropUnique(['city_id', 'name']);
            // Drop the indexes
            $table->dropIndex(['name']);
            $table->dropIndex(['city_id', 'name']);
            // Drop the name column since it's now handled by translations
            $table->dropColumn('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('areas', function (Blueprint $table) {
            // Add back the name column
            $table->string('name');
            // Add back the indexes
            $table->index('name');
            $table->index(['city_id', 'name']);
            // Add back the unique constraint
            $table->unique(['city_id', 'name']);
        });
    }
};
