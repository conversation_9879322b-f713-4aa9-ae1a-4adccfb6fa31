<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->string('type')->default('general'); // general, order, promotion, system, etc.
            $table->boolean('is_read')->default(false);
            $table->timestamps();
            $table->softDeletes();

            // Indexes for better performance
            $table->index('customer_id');
            $table->index('type');
            $table->index('is_read');
            $table->index('created_at');
            $table->index(['customer_id', 'is_read']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
