<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('favourite_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('provider_service_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            // Indexes
            $table->index('customer_id');
            $table->index('provider_service_id');
            
            // Unique constraint to prevent duplicate favourites
            $table->unique(['customer_id', 'provider_service_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('favourite_services');
    }
};
