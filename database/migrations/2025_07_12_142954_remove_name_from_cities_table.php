<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cities', function (Blueprint $table) {
            // Drop the unique constraint first
            $table->dropUnique(['name']);
            // Drop the index
            $table->dropIndex(['name']);
            // Drop the name column since it's now handled by translations
            $table->dropColumn('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cities', function (Blueprint $table) {
            // Add back the name column
            $table->string('name')->unique();
            // Add back the index
            $table->index('name');
        });
    }
};
