<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('provider_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_provider_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_category_id')->nullable()->constrained()->onDelete('set null');
            $table->string('image')->nullable(); // Image/File URL
            $table->string('title');
            $table->decimal('price', 10, 2); // Up to 99,999,999.99
            $table->decimal('rating', 3, 2)->default(0.00); // 0.00 to 5.00
            $table->integer('duration')->default(0); // Duration in minutes
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('service_provider_id');
            $table->index('service_category_id');
            $table->index('title');
            $table->index('price');
            $table->index('rating');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('provider_services');
    }
};
