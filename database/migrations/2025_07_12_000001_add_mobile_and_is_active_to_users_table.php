<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('mobile', 15)->nullable()->after('email');
            $table->boolean('is_active')->default(true)->after('mobile');
            $table->softDeletes()->after('updated_at');
            
            // Add indexes for better performance
            $table->index('mobile');
            $table->index('is_active');
            $table->index('deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['mobile']);
            $table->dropIndex(['is_active']);
            $table->dropIndex(['deleted_at']);
            $table->dropColumn(['mobile', 'is_active', 'deleted_at']);
        });
    }
};
