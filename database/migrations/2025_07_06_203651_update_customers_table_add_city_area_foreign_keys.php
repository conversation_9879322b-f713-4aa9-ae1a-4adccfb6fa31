<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Add new foreign key columns
            $table->foreignId('city_id')->nullable()->constrained()->onDelete('set null')->after('gender');
            $table->foreignId('area_id')->nullable()->constrained()->onDelete('set null')->after('city_id');

            // Add indexes for the new columns
            $table->index('city_id');
            $table->index('area_id');
            $table->index(['city_id', 'area_id']);
        });

        // Drop the old string columns in a separate statement to avoid conflicts
        Schema::table('customers', function (Blueprint $table) {
            // Drop the old composite index first
            $table->dropIndex(['city', 'area']);

            // Drop the old string columns
            $table->dropColumn(['city', 'area']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Add back the old string columns
            $table->string('city')->nullable()->after('gender');
            $table->string('area')->nullable()->after('city');

            // Add back the old composite index
            $table->index(['city', 'area']);
        });

        Schema::table('customers', function (Blueprint $table) {
            // Drop the new foreign key columns and their indexes
            $table->dropIndex(['city_id', 'area_id']);
            $table->dropIndex(['area_id']);
            $table->dropIndex(['city_id']);
            $table->dropForeign(['area_id']);
            $table->dropForeign(['city_id']);
            $table->dropColumn(['city_id', 'area_id']);
        });
    }
};
