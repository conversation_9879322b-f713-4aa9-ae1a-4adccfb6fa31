<?php

namespace Database\Seeders;

use App\Models\ServiceCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ServiceCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Beauty & Wellness',
                'description' => 'Professional beauty and wellness services',
                'icon' => 'https://via.placeholder.com/100x100.png/beauty'
            ],
            [
                'name' => 'Home Services',
                'description' => 'Home maintenance and improvement services',
                'icon' => 'https://via.placeholder.com/100x100.png/home'
            ],
            [
                'name' => 'Health & Fitness',
                'description' => 'Health, fitness and medical services',
                'icon' => 'https://via.placeholder.com/100x100.png/health'
            ],
            [
                'name' => 'Automotive',
                'description' => 'Car maintenance and repair services',
                'icon' => 'https://via.placeholder.com/100x100.png/automotive'
            ],
            [
                'name' => 'Education & Training',
                'description' => 'Educational and skill development services',
                'icon' => 'https://via.placeholder.com/100x100.png/education'
            ],
            [
                'name' => 'Technology',
                'description' => 'IT and technology related services',
                'icon' => 'https://via.placeholder.com/100x100.png/technology'
            ],
            [
                'name' => 'Event Planning',
                'description' => 'Event organization and planning services',
                'icon' => 'https://via.placeholder.com/100x100.png/events'
            ],
            [
                'name' => 'Legal Services',
                'description' => 'Legal consultation and services',
                'icon' => 'https://via.placeholder.com/100x100.png/legal'
            ],
            [
                'name' => 'Financial Services',
                'description' => 'Financial planning and advisory services',
                'icon' => 'https://via.placeholder.com/100x100.png/finance'
            ],
            [
                'name' => 'Pet Services',
                'description' => 'Pet care and veterinary services',
                'icon' => 'https://via.placeholder.com/100x100.png/pets'
            ],
        ];

        foreach ($categories as $category) {
            ServiceCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }
    }
}
