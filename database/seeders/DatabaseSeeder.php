<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Customer;
use App\Models\ServiceCategory;
use App\Models\ServiceProvider;
use App\Models\ProviderService;
use App\Models\MainImageSlider;
use App\Models\WorkingHour;
use App\Models\ServiceProviderGallery;
use App\Models\ProviderServiceImage;
use App\Models\FavouriteService;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create cities and areas first
        $this->call([
            CitySeeder::class,
            AreaSeeder::class,
            ServiceCategorySeeder::class,
        ]);

        // Create test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Get service categories
        $categories = ServiceCategory::all();

        // Create customers
        $customers = Customer::factory(50)->create();

        // Create service providers
        $providers = ServiceProvider::factory(20)->create();

        // Create working hours for each provider
        foreach ($providers as $provider) {
            $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

            foreach ($days as $day) {
                WorkingHour::create([
                    'service_provider_id' => $provider->id,
                    'day' => $day,
                    'open_time' => $day === 'sunday' ? null : '09:00',
                    'close_time' => $day === 'sunday' ? null : '18:00',
                    'is_off_day' => $day === 'sunday',
                ]);
            }

            // Create gallery images for each provider
            ServiceProviderGallery::factory(rand(3, 8))->create([
                'service_provider_id' => $provider->id,
            ]);
        }

        // Create provider services
        foreach ($providers as $provider) {
            $serviceCount = rand(3, 10);

            for ($i = 0; $i < $serviceCount; $i++) {
                $service = ProviderService::factory()->create([
                    'service_provider_id' => $provider->id,
                    'service_category_id' => $categories->random()->id,
                ]);

                // Create service images
                ProviderServiceImage::factory(rand(2, 5))->create([
                    'provider_service_id' => $service->id,
                ]);
            }
        }

        // Create some favourite services
        $services = ProviderService::where('is_active', true)->get();
        foreach ($customers->take(30) as $customer) {
            $favouriteServices = $services->random(rand(1, 5));

            foreach ($favouriteServices as $service) {
                FavouriteService::firstOrCreate([
                    'customer_id' => $customer->id,
                    'provider_service_id' => $service->id,
                ]);
            }
        }

        // Create main image sliders
        MainImageSlider::factory(5)->create();

        $this->command->info('Database seeded successfully!');
    }
}
