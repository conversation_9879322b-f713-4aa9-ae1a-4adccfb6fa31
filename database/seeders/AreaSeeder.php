<?php

namespace Database\Seeders;

use App\Models\Area;
use App\Models\City;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AreaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $cityAreas = [
            'Riyadh' => [
                ['ar' => 'العليا', 'en' => 'Al Olaya'],
                ['ar' => 'الملز', 'en' => 'Al Malaz'],
                ['ar' => 'النسيم', 'en' => 'Al Naseem'],
                ['ar' => 'الورود', 'en' => 'Al Wurud'],
                ['ar' => 'حي الملك فهد', 'en' => 'King Fahd District'],
                ['ar' => 'الصحافة', 'en' => 'Al Sahafa'],
                ['ar' => 'النخيل', 'en' => '<PERSON> Na<PERSON>eel'],
                ['ar' => 'الحمراء', 'en' => 'Al Hamra'],
                ['ar' => 'الروضة', 'en' => 'Al Rawdah'],
                ['ar' => 'السليمانية', 'en' => 'Al Sulaimaniyah']
            ],
            'Jeddah' => [
                ['ar' => 'البلد', 'en' => 'Al Balad'],
                ['ar' => 'الحمراء', 'en' => 'Al Hamra'],
                ['ar' => 'الروضة', 'en' => 'Al Rawdah'],
                ['ar' => 'السلامة', 'en' => 'Al Salamah'],
                ['ar' => 'الزهراء', 'en' => 'Al Zahra'],
                ['ar' => 'الأندلس', 'en' => 'Al Andalus'],
                ['ar' => 'الفيصلية', 'en' => 'Al Faisaliyah'],
                ['ar' => 'الشرفية', 'en' => 'Al Sharafiyah'],
                ['ar' => 'الكورنيش', 'en' => 'Al Corniche'],
                ['ar' => 'الرحاب', 'en' => 'Al Rehab']
            ],
            'Mecca' => [
                ['ar' => 'الحرم', 'en' => 'Al Haram'],
                ['ar' => 'العزيزية', 'en' => 'Al Aziziyah'],
                ['ar' => 'المنصور', 'en' => 'Al Mansour'],
                ['ar' => 'الشبيكة', 'en' => 'Al Shubaikah'],
                ['ar' => 'الكعكية', 'en' => 'Al Kakiyah'],
                ['ar' => 'المعابدة', 'en' => 'Al Maabdah'],
                ['ar' => 'الهنداوية', 'en' => 'Al Hindawiyah'],
                ['ar' => 'الرصيفة', 'en' => 'Al Rusaifah'],
                ['ar' => 'التنعيم', 'en' => 'Al Taneem'],
                ['ar' => 'العوالي', 'en' => 'Al Awali']
            ],
            'Medina' => [
                ['ar' => 'الحرم', 'en' => 'Al Haram'],
                ['ar' => 'العيون', 'en' => 'Al Uyun'],
                ['ar' => 'الأزهر', 'en' => 'Al Azhar'],
                ['ar' => 'الخالدية', 'en' => 'Al Khalidiyah'],
                ['ar' => 'الرانونا', 'en' => 'Al Ranuna'],
                ['ar' => 'التلال', 'en' => 'Al Tilal'],
                ['ar' => 'الفتح', 'en' => 'Al Fath'],
                ['ar' => 'النور', 'en' => 'Al Noor'],
                ['ar' => 'الهجرة', 'en' => 'Al Hijrah'],
                ['ar' => 'الأنصار', 'en' => 'Al Ansar']
            ],
            'Dammam' => [
                ['ar' => 'الفيصلية', 'en' => 'Al Faisaliyah'],
                ['ar' => 'الشاطئ', 'en' => 'Al Shati'],
                ['ar' => 'الآدمة', 'en' => 'Al Adamah'],
                ['ar' => 'الجلوية', 'en' => 'Al Jalawiyah'],
                ['ar' => 'النور', 'en' => 'Al Noor'],
                ['ar' => 'البادية', 'en' => 'Al Badiyah'],
                ['ar' => 'المحمدية', 'en' => 'Al Muhammadiyah'],
                ['ar' => 'الروضة', 'en' => 'Al Rawdah'],
                ['ar' => 'المزروعية', 'en' => 'Al Mazruiyah'],
                ['ar' => 'الكورنيش', 'en' => 'Al Corniche']
            ]
        ];

        foreach ($cityAreas as $cityEnglishName => $areas) {
            // Find city by English translation
            $city = City::whereTranslation('name', $cityEnglishName, 'en')->first();

            if ($city) {
                foreach ($areas as $areaTranslations) {
                    // Check if area already exists by English name (for backward compatibility)
                    $existingArea = Area::where('city_id', $city->id)
                        ->whereTranslation('name', $areaTranslations['en'], 'en')
                        ->first();

                    if (!$existingArea) {
                        // Create new area with translations
                        $area = Area::create(['city_id' => $city->id]);

                        // Add translations
                        foreach ($areaTranslations as $locale => $name) {
                            $area->translateOrNew($locale)->name = $name;
                        }

                        $area->save();
                    }
                }
            }
        }
    }
}
