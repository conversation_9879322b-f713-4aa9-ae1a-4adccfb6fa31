<?php

namespace Database\Seeders;

use App\Models\City;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $cities = [
            [
                'ar' => 'الرياض',
                'en' => 'Riyadh'
            ],
            [
                'ar' => 'جدة',
                'en' => 'Jeddah'
            ],
            [
                'ar' => 'مكة المكرمة',
                'en' => 'Mecca'
            ],
            [
                'ar' => 'المدينة المنورة',
                'en' => 'Medina'
            ],
            [
                'ar' => 'الدمام',
                'en' => 'Dammam'
            ],
            [
                'ar' => 'الخبر',
                'en' => 'Khobar'
            ],
            [
                'ar' => 'الظهران',
                'en' => 'Dhahran'
            ],
            [
                'ar' => 'الطائف',
                'en' => 'Taif'
            ],
            [
                'ar' => 'بريدة',
                'en' => 'Buraidah'
            ],
            [
                'ar' => 'تبوك',
                'en' => 'Tabuk'
            ],
            [
                'ar' => 'حائل',
                'en' => 'Hail'
            ],
            [
                'ar' => 'خميس مشيط',
                'en' => 'Khamis Mushait'
            ],
            [
                'ar' => 'الهفوف',
                'en' => 'Hofuf'
            ],
            [
                'ar' => 'المبرز',
                'en' => 'Mubarraz'
            ],
            [
                'ar' => 'نجران',
                'en' => 'Najran'
            ],
            [
                'ar' => 'ينبع',
                'en' => 'Yanbu'
            ],
            [
                'ar' => 'أبها',
                'en' => 'Abha'
            ],
            [
                'ar' => 'عرعر',
                'en' => 'Arar'
            ],
            [
                'ar' => 'سكاكا',
                'en' => 'Sakaka'
            ],
            [
                'ar' => 'جازان',
                'en' => 'Jizan'
            ]
        ];

        foreach ($cities as $cityTranslations) {
            // Check if city already exists by English name (for backward compatibility)
            $existingCity = City::whereTranslation('name', $cityTranslations['en'], 'en')->first();

            if (!$existingCity) {
                // Create new city with translations
                $city = City::create();

                // Add translations
                foreach ($cityTranslations as $locale => $name) {
                    $city->translateOrNew($locale)->name = $name;
                }

                $city->save();
            }
        }
    }
}
