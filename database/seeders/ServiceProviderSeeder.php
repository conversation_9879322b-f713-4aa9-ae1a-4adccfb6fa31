<?php

namespace Database\Seeders;

use App\Models\ServiceProvider;
use App\Models\City;
use App\Models\Area;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ServiceProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some cities and areas for testing
        $riyadh = City::whereTranslation('name', 'Riyadh', 'en')->first();
        $jeddah = City::whereTranslation('name', 'Jeddah', 'en')->first();

        $riyadhAreas = $riyadh ? $riyadh->areas()->take(2)->get() : collect();
        $jeddahAreas = $jeddah ? $jeddah->areas()->take(2)->get() : collect();

        $providers = [
            [
                'translations' => [
                    'ar' => [
                        'name' => 'شركة الخدمات المتميزة',
                        'description' => 'نقدم أفضل الخدمات المنزلية والتجارية بجودة عالية ومواعيد دقيقة'
                    ],
                    'en' => [
                        'name' => 'Premium Services Company',
                        'description' => 'We provide the best home and commercial services with high quality and precise timing'
                    ]
                ],
                'logo' => 'https://via.placeholder.com/150x150/4F46E5/FFFFFF?text=PSC',
                'rating' => 4.8,
                'city_id' => $riyadh?->id,
                'area_id' => $riyadhAreas->first()?->id,
                'latitude' => 24.7136,
                'longitude' => 46.6753,
                'is_active' => true,
            ],
            [
                'translations' => [
                    'ar' => [
                        'name' => 'مؤسسة الصيانة الشاملة',
                        'description' => 'متخصصون في صيانة وإصلاح جميع الأجهزة المنزلية والمكتبية'
                    ],
                    'en' => [
                        'name' => 'Comprehensive Maintenance Foundation',
                        'description' => 'Specialists in maintenance and repair of all home and office appliances'
                    ]
                ],
                'logo' => 'https://via.placeholder.com/150x150/059669/FFFFFF?text=CMF',
                'rating' => 4.5,
                'city_id' => $jeddah?->id,
                'area_id' => $jeddahAreas->first()?->id,
                'latitude' => 21.4858,
                'longitude' => 39.1925,
                'is_active' => true,
            ],
            [
                'translations' => [
                    'ar' => [
                        'name' => 'خدمات التنظيف الاحترافية',
                        'description' => 'فريق محترف لتنظيف المنازل والمكاتب والمباني التجارية'
                    ],
                    'en' => [
                        'name' => 'Professional Cleaning Services',
                        'description' => 'Professional team for cleaning homes, offices and commercial buildings'
                    ]
                ],
                'logo' => 'https://via.placeholder.com/150x150/DC2626/FFFFFF?text=PCS',
                'rating' => 4.2,
                'city_id' => $riyadh?->id,
                'area_id' => $riyadhAreas->last()?->id,
                'latitude' => 24.6877,
                'longitude' => 46.7219,
                'is_active' => false,
            ],
            [
                'translations' => [
                    'ar' => [
                        'name' => 'مركز الخدمات السريعة',
                        'description' => 'خدمات سريعة ومتنوعة لجميع احتياجاتكم اليومية'
                    ],
                    'en' => [
                        'name' => 'Quick Services Center',
                        'description' => 'Fast and diverse services for all your daily needs'
                    ]
                ],
                'logo' => 'https://via.placeholder.com/150x150/7C3AED/FFFFFF?text=QSC',
                'rating' => 3.9,
                'city_id' => $jeddah?->id,
                'area_id' => $jeddahAreas->last()?->id,
                'latitude' => 21.5169,
                'longitude' => 39.2192,
                'is_active' => true,
            ],
        ];

        foreach ($providers as $providerData) {
            // Extract translations
            $translations = $providerData['translations'];
            unset($providerData['translations']);

            // Create the provider
            $provider = ServiceProvider::create($providerData);

            // Add translations
            foreach ($translations as $locale => $translation) {
                $provider->translateOrNew($locale)->name = $translation['name'];
                $provider->translateOrNew($locale)->description = $translation['description'];
            }

            $provider->save();
        }
    }
}
