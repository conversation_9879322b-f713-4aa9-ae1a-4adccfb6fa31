<?php

namespace Database\Factories;

use App\Models\ServiceProviderGallery;
use App\Models\ServiceProvider;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ServiceProviderGallery>
 */
class ServiceProviderGalleryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ServiceProviderGallery::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $imageTypes = ['interior', 'exterior', 'equipment', 'team', 'workspace', 'facility'];
        $imageType = $this->faker->randomElement($imageTypes);

        return [
            'service_provider_id' => ServiceProvider::factory(),
            'image' => $this->faker->imageUrl(800, 600, 'business', true, $imageType),
            'alt_text' => ucfirst($imageType) . ' view of our facility',
            'sort_order' => $this->faker->numberBetween(1, 10),
        ];
    }
}
