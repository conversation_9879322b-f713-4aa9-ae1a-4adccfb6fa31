<?php

namespace Database\Factories;

use App\Models\WorkingHour;
use App\Models\ServiceProvider;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WorkingHour>
 */
class WorkingHourFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WorkingHour::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        $day = $this->faker->randomElement($days);
        
        // Sunday is more likely to be off day
        $isOffDay = $day === 'sunday' ? $this->faker->boolean(70) : $this->faker->boolean(10);

        return [
            'service_provider_id' => ServiceProvider::factory(),
            'day' => $day,
            'open_time' => $isOffDay ? null : $this->faker->time('H:i', '10:00'),
            'close_time' => $isOffDay ? null : $this->faker->time('H:i', '20:00'),
            'is_off_day' => $isOffDay,
        ];
    }

    /**
     * Indicate that this is an off day.
     */
    public function offDay(): static
    {
        return $this->state(fn (array $attributes) => [
            'open_time' => null,
            'close_time' => null,
            'is_off_day' => true,
        ]);
    }

    /**
     * Indicate that this is a working day.
     */
    public function workingDay(): static
    {
        return $this->state(fn (array $attributes) => [
            'open_time' => '09:00',
            'close_time' => '18:00',
            'is_off_day' => false,
        ]);
    }

    /**
     * Indicate extended hours.
     */
    public function extendedHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'open_time' => '08:00',
            'close_time' => '22:00',
            'is_off_day' => false,
        ]);
    }
}
