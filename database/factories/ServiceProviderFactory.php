<?php

namespace Database\Factories;

use App\Models\ServiceProvider;
use App\Models\City;
use App\Models\Area;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ServiceProvider>
 */
class ServiceProviderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ServiceProvider::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $businessTypes = ['Salon', 'Spa', 'Clinic', 'Studio', 'Center', 'Services', 'Solutions', 'Company', 'Group', 'Associates'];
        $businessNames = ['Elite', 'Premium', 'Professional', 'Expert', 'Quality', 'Reliable', 'Trusted', 'Modern', 'Advanced', 'Superior'];

        // Get random city and area from database
        $city = City::inRandomOrder()->first();
        $area = null;

        if ($city) {
            $area = Area::where('city_id', $city->id)->inRandomOrder()->first();
        }

        $coordinates = $this->getSaudiCityCoordinates($city?->name ?? 'Riyadh');
        $businessName = $this->faker->randomElement($businessNames) . ' ' . $this->faker->randomElement($businessTypes);

        return [
            'name' => $businessName,
            'description' => $this->faker->paragraph(3),
            'logo' => $this->faker->imageUrl(200, 200, 'business', true, $businessName),
            'rating' => $this->faker->randomFloat(2, 3.0, 5.0),
            'city_id' => $city?->id,
            'area_id' => $area?->id,
            'longitude' => $coordinates['longitude'] + $this->faker->randomFloat(6, -0.05, 0.05),
            'latitude' => $coordinates['latitude'] + $this->faker->randomFloat(6, -0.05, 0.05),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Indicate that the service provider is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the service provider has high rating.
     */
    public function highRated(): static
    {
        return $this->state(fn(array $attributes) => [
            'rating' => $this->faker->randomFloat(2, 4.5, 5.0),
        ]);
    }

    /**
     * Get simplified coordinates for Saudi cities.
     */
    private function getSaudiCityCoordinates(string $city): array
    {
        $coordinates = [
            'Riyadh' => ['latitude' => 24.7136, 'longitude' => 46.6753],
            'Jeddah' => ['latitude' => 21.4858, 'longitude' => 39.1925],
            'Mecca' => ['latitude' => 21.3891, 'longitude' => 39.8579],
            'Medina' => ['latitude' => 24.5247, 'longitude' => 39.5692],
            'Dammam' => ['latitude' => 26.3927, 'longitude' => 49.9777],
            'Khobar' => ['latitude' => 26.2172, 'longitude' => 50.1971],
            'Dhahran' => ['latitude' => 26.2361, 'longitude' => 50.1328],
            'Taif' => ['latitude' => 21.2703, 'longitude' => 40.4158],
            'Buraidah' => ['latitude' => 26.3260, 'longitude' => 43.9750],
            'Tabuk' => ['latitude' => 28.3998, 'longitude' => 36.5700],
            'Hail' => ['latitude' => 27.5114, 'longitude' => 41.7208],
            'Khamis Mushait' => ['latitude' => 18.3059, 'longitude' => 42.7348],
            'Hofuf' => ['latitude' => 25.3647, 'longitude' => 49.5747],
            'Mubarraz' => ['latitude' => 25.4077, 'longitude' => 49.5906],
            'Najran' => ['latitude' => 17.4924, 'longitude' => 44.1277],
            'Yanbu' => ['latitude' => 24.0896, 'longitude' => 38.0618],
            'Abha' => ['latitude' => 18.2164, 'longitude' => 42.5053],
            'Arar' => ['latitude' => 30.9753, 'longitude' => 41.0381],
            'Sakaka' => ['latitude' => 29.9697, 'longitude' => 40.2064],
            'Jizan' => ['latitude' => 16.8892, 'longitude' => 42.5511],
        ];

        return $coordinates[$city] ?? ['latitude' => 24.7136, 'longitude' => 46.6753]; // Default to Riyadh
    }
}
