<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\City;
use App\Models\Area;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $genders = ['male', 'female'];

        // Get random city and area from database
        $city = City::inRandomOrder()->first();
        $area = null;

        if ($city) {
            $area = Area::where('city_id', $city->id)->inRandomOrder()->first();
        }

        // Generate coordinates based on Saudi cities (simplified)
        $coordinates = $this->getSaudiCityCoordinates($city?->name ?? 'Riyadh');

        return [
            'name' => $this->faker->name(),
            'gender' => $this->faker->randomElement($genders),
            'city_id' => $city?->id,
            'area_id' => $area?->id,
            'longitude' => $coordinates['longitude'] + $this->faker->randomFloat(6, -0.1, 0.1),
            'latitude' => $coordinates['latitude'] + $this->faker->randomFloat(6, -0.1, 0.1),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'mobile' => $this->generateSaudiMobile(),
            'remember_token' => Str::random(10),
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn(array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Generate a valid Saudi mobile number.
     */
    private function generateSaudiMobile(): string
    {
        // Generate 8 random digits after '05'
        $randomDigits = '';
        for ($i = 0; $i < 8; $i++) {
            $randomDigits .= $this->faker->numberBetween(0, 9);
        }

        return '05' . $randomDigits;
    }

    /**
     * Get simplified coordinates for Saudi cities.
     */
    private function getSaudiCityCoordinates(string $city): array
    {
        $coordinates = [
            'Riyadh' => ['latitude' => 24.7136, 'longitude' => 46.6753],
            'Jeddah' => ['latitude' => 21.4858, 'longitude' => 39.1925],
            'Mecca' => ['latitude' => 21.3891, 'longitude' => 39.8579],
            'Medina' => ['latitude' => 24.5247, 'longitude' => 39.5692],
            'Dammam' => ['latitude' => 26.3927, 'longitude' => 49.9777],
            'Khobar' => ['latitude' => 26.2172, 'longitude' => 50.1971],
            'Dhahran' => ['latitude' => 26.2361, 'longitude' => 50.1328],
            'Taif' => ['latitude' => 21.2703, 'longitude' => 40.4158],
            'Buraidah' => ['latitude' => 26.3260, 'longitude' => 43.9750],
            'Tabuk' => ['latitude' => 28.3998, 'longitude' => 36.5700],
            'Hail' => ['latitude' => 27.5114, 'longitude' => 41.7208],
            'Khamis Mushait' => ['latitude' => 18.3059, 'longitude' => 42.7348],
            'Hofuf' => ['latitude' => 25.3647, 'longitude' => 49.5747],
            'Mubarraz' => ['latitude' => 25.4077, 'longitude' => 49.5906],
            'Najran' => ['latitude' => 17.4924, 'longitude' => 44.1277],
            'Yanbu' => ['latitude' => 24.0896, 'longitude' => 38.0618],
            'Abha' => ['latitude' => 18.2164, 'longitude' => 42.5053],
            'Arar' => ['latitude' => 30.9753, 'longitude' => 41.0381],
            'Sakaka' => ['latitude' => 29.9697, 'longitude' => 40.2064],
            'Jizan' => ['latitude' => 16.8892, 'longitude' => 42.5511],
        ];

        return $coordinates[$city] ?? ['latitude' => 24.7136, 'longitude' => 46.6753]; // Default to Riyadh
    }
}
