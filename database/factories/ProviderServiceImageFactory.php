<?php

namespace Database\Factories;

use App\Models\ProviderServiceImage;
use App\Models\ProviderService;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProviderServiceImage>
 */
class ProviderServiceImageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProviderServiceImage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $imageTypes = ['before', 'after', 'process', 'result', 'equipment', 'detail'];
        $imageType = $this->faker->randomElement($imageTypes);

        return [
            'provider_service_id' => ProviderService::factory(),
            'image' => $this->faker->imageUrl(600, 400, 'business', true, $imageType),
            'alt_text' => ucfirst($imageType) . ' image of the service',
            'sort_order' => $this->faker->numberBetween(1, 5),
        ];
    }
}
