<?php

namespace Database\Factories;

use App\Models\ProviderService;
use App\Models\ServiceProvider;
use App\Models\ServiceCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProviderService>
 */
class ProviderServiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProviderService::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $serviceTypes = [
            'Consultation',
            'Treatment',
            'Therapy',
            'Maintenance',
            'Installation',
            'Repair',
            'Training',
            'Assessment',
            'Planning',
            'Design'
        ];

        $serviceAdjectives = [
            'Professional',
            'Premium',
            'Basic',
            'Advanced',
            'Express',
            'Comprehensive',
            'Standard',
            'Deluxe',
            'Quick',
            'Detailed'
        ];

        $title = $this->faker->randomElement($serviceAdjectives) . ' ' . $this->faker->randomElement($serviceTypes);

        return [
            'service_provider_id' => ServiceProvider::factory(),
            'service_category_id' => ServiceCategory::factory(),
            'image' => $this->faker->imageUrl(400, 300, 'business', true, $title),
            'title' => $title,
            'price' => $this->faker->randomFloat(2, 25.00, 500.00),
            'rating' => $this->faker->randomFloat(2, 3.0, 5.0),
            'duration' => $this->faker->randomElement([30, 45, 60, 90, 120, 180, 240]), // Duration in minutes
            'description' => $this->faker->paragraph(2),
            'is_active' => $this->faker->boolean(85), // 85% chance of being active
        ];
    }

    /**
     * Indicate that the service is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the service has high rating.
     */
    public function highRated(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->randomFloat(2, 4.5, 5.0),
        ]);
    }

    /**
     * Indicate that the service is expensive.
     */
    public function expensive(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $this->faker->randomFloat(2, 200.00, 1000.00),
        ]);
    }

    /**
     * Indicate that the service is budget-friendly.
     */
    public function budget(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $this->faker->randomFloat(2, 10.00, 50.00),
        ]);
    }

    /**
     * Indicate that the service is quick.
     */
    public function quick(): static
    {
        return $this->state(fn (array $attributes) => [
            'duration' => $this->faker->randomElement([15, 30, 45]),
        ]);
    }
}
