<?php

namespace Database\Factories;

use App\Models\MainImageSlider;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MainImageSlider>
 */
class MainImageSliderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MainImageSlider::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $titles = [
            'Welcome to Our Service Marketplace',
            'Find the Best Service Providers',
            'Quality Services at Your Fingertips',
            'Book Professional Services Today',
            'Trusted Service Providers Near You',
            'Experience Premium Services',
            'Your One-Stop Service Solution',
            'Connect with Expert Professionals',
        ];

        $descriptions = [
            'Discover amazing services from verified professionals in your area.',
            'Book appointments with top-rated service providers instantly.',
            'Get quality services delivered right to your doorstep.',
            'Join thousands of satisfied customers who trust our platform.',
            'Experience the convenience of professional services on demand.',
            'Find, compare, and book the best services in your city.',
        ];

        return [
            'image' => $this->faker->imageUrl(1200, 600, 'business', true, 'slider'),
            'title' => $this->faker->randomElement($titles),
            'description' => $this->faker->randomElement($descriptions),
            'link_url' => $this->faker->optional(0.7)->url(),
            'sort_order' => $this->faker->numberBetween(1, 10),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Indicate that the slider is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the slider has no link.
     */
    public function withoutLink(): static
    {
        return $this->state(fn (array $attributes) => [
            'link_url' => null,
        ]);
    }
}
