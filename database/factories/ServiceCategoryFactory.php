<?php

namespace Database\Factories;

use App\Models\ServiceCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ServiceCategory>
 */
class ServiceCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ServiceCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'Beauty & Wellness' => 'Professional beauty and wellness services',
            'Home Services' => 'Home maintenance and improvement services',
            'Health & Fitness' => 'Health, fitness and medical services',
            'Automotive' => 'Car maintenance and repair services',
            'Education & Training' => 'Educational and skill development services',
            'Technology' => 'IT and technology related services',
            'Event Planning' => 'Event organization and planning services',
            'Legal Services' => 'Legal consultation and services',
            'Financial Services' => 'Financial planning and advisory services',
            'Pet Services' => 'Pet care and veterinary services',
        ];

        $categoryName = $this->faker->randomElement(array_keys($categories));

        return [
            'name' => $categoryName,
            'description' => $categories[$categoryName],
            'icon' => $this->faker->imageUrl(100, 100, 'business', true, $categoryName),
        ];
    }
}
