Update **all form views** (e.g., create, update, edit forms) and **data display views** (e.g., show, detail pages) across the project to match the layout structure and CSS utility classes defined in the provided **layout snippet file** and associated **style definitions**.

---

**Instructions:**

1. **Match Layout Structure:**

    - Use the layout from the snippet file as the base structure.
    - Maintain the two-column responsive grid using:

        ```html
        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">...</div>
        ```

2. **Form Input Fields:**

    - Wrap each label + input in a `div` with:

        ```html
        class="flex items-center flex-wrap lg:flex-nowrap gap-2.5"
        ```

    - Use `label` with `class="form-label max-w-56"` and `input` with `class="input"` (or matching types).
    - Ensure fields use the placeholder, name, and value bindings as in the snippet.

3. **Full-Width Rows (if needed):**

    - Use `md:col-span-2` on grid children that should span both columns, like:

        - Status radio buttons
        - Section headers like "Service Fees"
        - Roles dropdown and selected roles

4. **Start New Sections (e.g., Service Fees):**

    - Place section dividers using:

        ```html
        <div class="border-t border-gray-200 pt-4 mt-2 mb-4">
            <h4 class="text-lg font-medium mb-3">Section Title</h4>
        </div>
        ```

5. **Match Custom Classes:**

    - Apply and preserve all custom CSS classes as used in the snippet:

        - `.input`, `.form-label`, `.radio`, `.radio-label`, etc.
        - Alert boxes must use `.alert` class per the included `<style>` block.

6. **Dropdowns and Selects:**

    - Use Tailwind utility classes for selects, matching this structure:

        ```html
        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm
        rounded-lg ..."
        ```

7. **Ensure Consistent Behavior:**

    - Make sure hidden inputs, radio groups, and interactive UI elements retain their original behavior and logic.

---

**Input Files:**

-   ✅ Layout Snippet: `snippet.html`
-   ✅ Stylesheet: `styles.css` (or embedded `<style>` block)

---

**Goal:**
Ensure all views follow a **consistent, responsive, and clean layout**, with matching class names, spacing, and accessibility standards based on the provided layout and style.
