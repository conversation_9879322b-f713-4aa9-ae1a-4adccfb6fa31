<?php

namespace Tests\Feature\Api;

use App\Models\ServiceProvider;
use App\Models\City;
use App\Models\Area;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProviderProfileTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected ServiceProvider $provider;
    protected City $city;
    protected Area $area;
    protected string $token;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test city and area
        $this->city = City::factory()->create();
        $this->area = Area::factory()->create(['city_id' => $this->city->id]);
        
        // Create authenticated provider
        $this->provider = ServiceProvider::factory()->create([
            'is_active' => true,
            'city_id' => $this->city->id,
            'area_id' => $this->area->id,
        ]);
        
        $this->token = $this->provider->createToken('test-token')->plainTextToken;
    }

    /** @test */
    public function authenticated_provider_can_get_detailed_profile()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/provider/profile/details');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'profile' => [
                        'id',
                        'name' => ['ar', 'en'],
                        'description' => ['ar', 'en'],
                        'email',
                        'logo',
                        'rating',
                        'city' => ['id', 'name'],
                        'area' => ['id', 'name'],
                        'latitude',
                        'longitude',
                        'is_active'
                    ],
                    'statistics' => [
                        'total_services',
                        'active_services',
                        'total_reviews',
                        'average_rating',
                        'profile_completion'
                    ]
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Profile retrieved successfully',
            ]);
    }

    /** @test */
    public function provider_can_update_basic_info_with_all_required_fields()
    {
        $updateData = [
            'translations' => [
                'ar' => [
                    'name' => 'شركة الخدمات المحدثة',
                    'description' => 'وصف محدث للشركة باللغة العربية مع تفاصيل أكثر'
                ],
                'en' => [
                    'name' => 'Updated Services Company',
                    'description' => 'Updated company description in English with more details'
                ]
            ],
            'email' => '<EMAIL>',
            'city_id' => $this->city->id,
            'area_id' => $this->area->id,
            'latitude' => 24.7136,
            'longitude' => 46.6753,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson('/api/provider/profile/basic-info', $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Profile updated successfully',
            ])
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name' => ['ar', 'en'],
                    'description' => ['ar', 'en'],
                    'email',
                    'city' => ['id', 'name'],
                    'area' => ['id', 'name'],
                    'latitude',
                    'longitude'
                ]
            ]);

        // Verify database was updated
        $this->assertDatabaseHas('service_providers', [
            'id' => $this->provider->id,
            'email' => '<EMAIL>',
            'latitude' => 24.7136,
            'longitude' => 46.6753,
        ]);
    }

    /** @test */
    public function provider_can_update_basic_info_with_logo_upload()
    {
        Storage::fake('public');

        $logoFile = UploadedFile::fake()->image('logo.jpg', 300, 300)->size(1024);

        $updateData = [
            'translations' => [
                'ar' => [
                    'name' => 'شركة الخدمات',
                    'description' => 'وصف الشركة باللغة العربية'
                ],
                'en' => [
                    'name' => 'Services Company',
                    'description' => 'Company description in English'
                ]
            ],
            'email' => '<EMAIL>',
            'city_id' => $this->city->id,
            'area_id' => $this->area->id,
            'latitude' => 24.7136,
            'longitude' => 46.6753,
            'logo' => $logoFile,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson('/api/provider/profile/basic-info', $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Profile updated successfully',
            ]);

        // Verify logo was uploaded
        $this->provider->refresh();
        $this->assertNotNull($this->provider->logo);
        Storage::disk('public')->assertExists($this->provider->logo);
    }

    /** @test */
    public function update_basic_info_requires_all_fields()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson('/api/provider/profile/basic-info', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'translations.ar.name',
                'translations.en.name',
                'translations.ar.description',
                'translations.en.description',
                'email',
                'city_id',
                'area_id',
                'latitude',
                'longitude'
            ]);
    }

    /** @test */
    public function update_basic_info_validates_area_belongs_to_city()
    {
        $anotherCity = City::factory()->create();
        $anotherArea = Area::factory()->create(['city_id' => $anotherCity->id]);

        $updateData = [
            'translations' => [
                'ar' => [
                    'name' => 'شركة الخدمات',
                    'description' => 'وصف الشركة باللغة العربية'
                ],
                'en' => [
                    'name' => 'Services Company',
                    'description' => 'Company description in English'
                ]
            ],
            'email' => '<EMAIL>',
            'city_id' => $this->city->id,
            'area_id' => $anotherArea->id, // Area from different city
            'latitude' => 24.7136,
            'longitude' => 46.6753,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson('/api/provider/profile/basic-info', $updateData);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Selected area does not belong to the selected city',
            ]);
    }

    /** @test */
    public function provider_can_get_cities_list()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/provider/cities');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => ['id', 'name']
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Cities retrieved successfully',
            ]);
    }

    /** @test */
    public function provider_can_get_areas_by_city()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/provider/areas-by-city?city_id=' . $this->city->id);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => ['id', 'name']
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Areas retrieved successfully',
            ]);
    }

    /** @test */
    public function get_areas_by_city_requires_city_id()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/provider/areas-by-city');

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'City ID is required',
            ]);
    }

    /** @test */
    public function unauthenticated_provider_cannot_access_profile_endpoints()
    {
        $endpoints = [
            'GET' => [
                '/api/provider/profile/details',
                '/api/provider/cities',
                '/api/provider/areas-by-city?city_id=1'
            ],
            'PUT' => [
                '/api/provider/profile/basic-info'
            ]
        ];

        foreach ($endpoints as $method => $urls) {
            foreach ($urls as $url) {
                $response = $this->json($method, $url);
                $response->assertStatus(401);
            }
        }
    }
}
