<?php

namespace Tests\Feature\Api;

use App\Models\ServiceProvider;
use App\Models\City;
use App\Models\Area;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class ProviderAuthTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test city and area
        $this->city = City::factory()->create();
        $this->area = Area::factory()->create(['city_id' => $this->city->id]);
    }

    /** @test */
    public function provider_can_login_with_valid_credentials()
    {
        $provider = ServiceProvider::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'is_active' => true,
        ]);

        $response = $this->postJson('/api/provider/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'provider' => [
                        'id',
                        'name',
                        'email',
                        'logo',
                        'rating',
                        'is_active'
                    ],
                    'token'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Login successful',
            ]);

        $this->assertDatabaseHas('personal_access_tokens', [
            'tokenable_type' => ServiceProvider::class,
            'tokenable_id' => $provider->id,
        ]);
    }

    /** @test */
    public function provider_cannot_login_with_invalid_credentials()
    {
        ServiceProvider::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->postJson('/api/provider/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Invalid credentials',
            ]);
    }

    /** @test */
    public function inactive_provider_cannot_login()
    {
        ServiceProvider::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'is_active' => false,
        ]);

        $response = $this->postJson('/api/provider/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Your account is inactive. Please contact support.',
            ]);
    }

    /** @test */
    public function provider_login_requires_email_and_password()
    {
        $response = $this->postJson('/api/provider/login', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email', 'password']);
    }

    /** @test */
    public function authenticated_provider_can_logout()
    {
        $provider = ServiceProvider::factory()->create([
            'is_active' => true,
        ]);

        $token = $provider->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/provider/logout');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Logout successful',
            ]);

        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_type' => ServiceProvider::class,
            'tokenable_id' => $provider->id,
        ]);
    }

    /** @test */
    public function unauthenticated_provider_cannot_logout()
    {
        $response = $this->postJson('/api/provider/logout');

        $response->assertStatus(401);
    }

    /** @test */
    public function authenticated_provider_can_get_profile()
    {
        $provider = ServiceProvider::factory()->create([
            'is_active' => true,
            'city_id' => $this->city->id,
            'area_id' => $this->area->id,
        ]);

        $token = $provider->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/provider/profile');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'logo',
                    'rating',
                    'city',
                    'area',
                    'latitude',
                    'longitude',
                    'is_active'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Profile retrieved successfully',
            ]);
    }

    /** @test */
    public function unauthenticated_provider_cannot_get_profile()
    {
        $response = $this->getJson('/api/provider/profile');

        $response->assertStatus(401);
    }
}
