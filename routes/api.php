<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\CustomerAuthController;
use App\Http\Controllers\Api\CustomerProfileController;
use App\Http\Controllers\Api\HomeScreenController;
use App\Http\Controllers\Api\PageController;
use App\Http\Controllers\Api\ContactMessageController;
use App\Http\Controllers\Api\FavouriteServiceController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\ServiceProviderController;
use App\Http\Controllers\Api\ProviderAuthController;
use App\Http\Controllers\Api\ProviderProfileController;

// Customer Authentication
Route::post('/customer/register', [CustomerAuthController::class, 'register']);
Route::post('/customer/login', [CustomerAuthController::class, 'login']);

// Provider Authentication
Route::post('/provider/login', [ProviderAuthController::class, 'login']);

// Provider Protected Routes
Route::middleware('auth:sanctum')->prefix('provider')->name('provider.')->group(function () {
    Route::post('/logout', [ProviderAuthController::class, 'logout']);
    Route::get('/profile', [ProviderAuthController::class, 'profile']);
    Route::get('/profile/details', [ProviderProfileController::class, 'show']);
    Route::put('/profile/basic-info', [ProviderProfileController::class, 'updateBasicInfo']);
    Route::get('/cities', [ProviderProfileController::class, 'getCities']);
    Route::get('/areas-by-city', [ProviderProfileController::class, 'getAreasByCity']);
});

// Protected Routes
Route::middleware('auth:sanctum')->group(function () {
    // Home Screen
    Route::get('/home', [HomeScreenController::class, 'index']);

    // Contact Messages
    Route::post('/contact', [ContactMessageController::class, 'store']);

    // Customer Profile
    Route::post('/customer/logout', [CustomerAuthController::class, 'logout']);
    Route::get('/customer/profile', [CustomerProfileController::class, 'show']);
    Route::put('/customer/profile', [CustomerProfileController::class, 'update']);
    Route::post('/customer/profile/image', [CustomerProfileController::class, 'updateProfileImage']);
    Route::delete('/customer/profile', [CustomerProfileController::class, 'destroy']);

    // Pages
    Route::get('/pages', [PageController::class, 'index']);
    Route::get('/pages/{id}', [PageController::class, 'show']);

    // Favourite Services
    Route::get('/favorites', [FavouriteServiceController::class, 'index']);
    Route::post('/favorites', [FavouriteServiceController::class, 'store']);
    Route::delete('/favorites/{id}', [FavouriteServiceController::class, 'destroy']);

    // Notifications
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    Route::delete('/notifications/clear-all', [NotificationController::class, 'clearAll']);

    // Service Providers
    Route::get('/service-providers', [ServiceProviderController::class, 'index']);
    Route::get('/service-providers/{id}', [ServiceProviderController::class, 'show']);
});
