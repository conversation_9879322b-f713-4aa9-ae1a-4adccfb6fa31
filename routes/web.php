<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\AuthController;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

// Localized Routes Group
Route::group([
    'prefix' => LaravelLocalization::setLocale(),
    'middleware' => ['localeSessionRedirect', 'localizationRedirect']
], function () {
    // Home Route
    Route::get('/', function () {
        return view('admin.auth.login');
    })->name('home');

    // Admin Authentication Routes (not protected)
    Route::prefix('admin')->name('admin.')->group(function () {
        Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [AuthController::class, 'login'])->name('login.submit');
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    });

    // Protected Admin Routes (localized)
    Route::prefix('admin')->name('admin.')->middleware(['admin.auth'])->group(function () {
        // Dashboard
        Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');

        // Dashboard API endpoints for charts
        Route::get('/api/user-growth', [DashboardController::class, 'getUserGrowthData'])->name('api.user-growth');
        Route::get('/api/service-categories', [DashboardController::class, 'getServiceCategoriesData'])->name('api.service-categories');

        // User Management Routes
        Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
        Route::patch('users/{user}/activate', [\App\Http\Controllers\Admin\UserController::class, 'activate'])->name('users.activate');
        Route::patch('users/{user}/deactivate', [\App\Http\Controllers\Admin\UserController::class, 'deactivate'])->name('users.deactivate');

        // Messages Management Routes (excluding create/store)
        Route::resource('messages', \App\Http\Controllers\Admin\MessageController::class)->except(['create', 'store']);
        Route::patch('messages/{message}/mark-read', [\App\Http\Controllers\Admin\MessageController::class, 'markAsRead'])->name('messages.mark-read');
        Route::patch('messages/{message}/mark-archived', [\App\Http\Controllers\Admin\MessageController::class, 'markAsArchived'])->name('messages.mark-archived');
        Route::post('messages/bulk-mark-read', [\App\Http\Controllers\Admin\MessageController::class, 'bulkMarkAsRead'])->name('messages.bulk-mark-read');
        Route::post('messages/bulk-archive', [\App\Http\Controllers\Admin\MessageController::class, 'bulkArchive'])->name('messages.bulk-archive');
        Route::delete('messages/bulk-delete', [\App\Http\Controllers\Admin\MessageController::class, 'bulkDelete'])->name('messages.bulk-delete');

        // Cities Management Routes (now including create/store)
        Route::resource('cities', \App\Http\Controllers\Admin\CityController::class);
        Route::patch('cities/{city}/restore', [\App\Http\Controllers\Admin\CityController::class, 'restore'])->name('cities.restore');
        Route::delete('cities/{city}/force-delete', [\App\Http\Controllers\Admin\CityController::class, 'forceDelete'])->name('cities.force-delete');
        Route::post('cities/bulk-delete', [\App\Http\Controllers\Admin\CityController::class, 'bulkDelete'])->name('cities.bulk-delete');
        Route::post('cities/bulk-restore', [\App\Http\Controllers\Admin\CityController::class, 'bulkRestore'])->name('cities.bulk-restore');

        // Areas Management Routes (now including create/store)
        Route::resource('areas', \App\Http\Controllers\Admin\AreaController::class);
        Route::patch('areas/{area}/restore', [\App\Http\Controllers\Admin\AreaController::class, 'restore'])->name('areas.restore');
        Route::delete('areas/{area}/force-delete', [\App\Http\Controllers\Admin\AreaController::class, 'forceDelete'])->name('areas.force-delete');
        Route::post('areas/bulk-delete', [\App\Http\Controllers\Admin\AreaController::class, 'bulkDelete'])->name('areas.bulk-delete');
        Route::post('areas/bulk-restore', [\App\Http\Controllers\Admin\AreaController::class, 'bulkRestore'])->name('areas.bulk-restore');

        // Service Providers Management Routes (excluding create/store)
        // Custom routes must be defined BEFORE resource routes to prevent conflicts
        Route::get('providers/areas-by-city', [\App\Http\Controllers\Admin\ServiceProviderController::class, 'getAreasByCity'])->name('providers.areas-by-city');
        Route::post('providers/bulk-delete', [\App\Http\Controllers\Admin\ServiceProviderController::class, 'bulkDelete'])->name('providers.bulk-delete');
        Route::post('providers/bulk-restore', [\App\Http\Controllers\Admin\ServiceProviderController::class, 'bulkRestore'])->name('providers.bulk-restore');
        Route::post('providers/bulk-update-status', [\App\Http\Controllers\Admin\ServiceProviderController::class, 'bulkUpdateStatus'])->name('providers.bulk-update-status');
        Route::patch('providers/{provider}/restore', [\App\Http\Controllers\Admin\ServiceProviderController::class, 'restore'])->name('providers.restore');
        Route::delete('providers/{provider}/force-delete', [\App\Http\Controllers\Admin\ServiceProviderController::class, 'forceDelete'])->name('providers.force-delete');
        Route::resource('providers', \App\Http\Controllers\Admin\ServiceProviderController::class)->except(['create', 'store']);
    });
});
