<?php

namespace App\Providers;

use App\Services\LanguageService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Blade;

class LanguageServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(LanguageService::class, function ($app) {
            return new LanguageService();
        });

        // Register alias for easier access
        $this->app->alias(LanguageService::class, 'language');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share language service with all views
        View::composer('*', function ($view) {
            $view->with('languageService', app(LanguageService::class));
        });

        // Register Blade directives for language functionality
        $this->registerBladeDirectives();
    }

    /**
     * Register custom Blade directives for language functionality
     */
    protected function registerBladeDirectives(): void
    {
        // @lang directive to get current language
        Blade::directive('lang', function () {
            return "<?php echo app('language')->getCurrentLanguage(); ?>";
        });

        // @direction directive to get current direction
        Blade::directive('direction', function () {
            return "<?php echo app('language')->getCurrentDirection(); ?>";
        });

        // @isRtl directive to check if current language is RTL
        Blade::directive('isRtl', function () {
            return "<?php if(app('language')->isRtl()): ?>";
        });

        // @endisRtl directive
        Blade::directive('endisRtl', function () {
            return "<?php endif; ?>";
        });

        // @isLtr directive to check if current language is LTR
        Blade::directive('isLtr', function () {
            return "<?php if(app('language')->isLtr()): ?>";
        });

        // @endisLtr directive
        Blade::directive('endisLtr', function () {
            return "<?php endif; ?>";
        });

        // @langSwitch directive to generate language switching links
        Blade::directive('langSwitch', function ($expression) {
            return "<?php echo app('language')->getLanguageSwitchingUrls({$expression}); ?>";
        });

        // @langFont directive to get language-specific font
        Blade::directive('langFont', function () {
            return "<?php echo app('language')->getLanguageFont(); ?>";
        });

        // @textAlign directive to get text alignment based on language direction
        Blade::directive('textAlign', function () {
            return "<?php echo app('language')->getTextAlignment(); ?>";
        });
    }
}
