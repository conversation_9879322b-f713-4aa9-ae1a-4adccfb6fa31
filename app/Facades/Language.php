<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static string getCurrentLanguage()
 * @method static string getCurrentLanguageName()
 * @method static string getCurrentLanguageNativeName()
 * @method static string getCurrentDirection()
 * @method static bool isRtl()
 * @method static bool isLtr()
 * @method static array getSupportedLanguages()
 * @method static array getLanguageSwitchingUrls(?string $route = null)
 * @method static string|null getLanguageUrl(string $locale, ?string $route = null)
 * @method static string getDefaultLanguage()
 * @method static bool isLocaleSupported(string $locale)
 * @method static string getDirectionClass()
 * @method static string getHtmlDirection()
 * @method static string getLanguageFont()
 * @method static string getLocalizedRouteName(string $routeName)
 * @method static string getNonLocalizedUrl(string $path)
 * @method static string getTextAlignment()
 * @method static string getOppositeTextAlignment()
 *
 * @see \App\Services\LanguageService
 */
class Language extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'language';
    }
}
