<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'unique:customers,email'],
            'mobile' => ['required', 'string', 'regex:/^05\d{8}$/', 'unique:customers,mobile'],
            'password' => [
                'required',
                'string',
                'min:8',
                'confirmed',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/'
            ],
            'password_confirmation' => ['required', 'string'],
            'approve_policy' => ['required', 'accepted'],
        ];
    }

    /**
     * Get the body parameters for Scribe documentation.
     *
     * @return array<string, array>
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'The customer\'s full name.',
                'example' => 'John Doe',
            ],
            'email' => [
                'description' => 'The customer\'s email address.',
                'example' => '<EMAIL>',
            ],
            'mobile' => [
                'description' => 'Saudi mobile number (starts with 05, 10 digits total).',
                'example' => '0512345678',
            ],
            'password' => [
                'description' => 'Password (min 8 chars, must contain uppercase, lowercase, and number).',
                'example' => 'Password123',
            ],
            'password_confirmation' => [
                'description' => 'Password confirmation (must match password).',
                'example' => 'Password123',
            ],
            'approve_policy' => [
                'description' => 'Must be true to accept policy terms.',
                'example' => true,
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Name is required.',
            'name.string' => 'Name must be a string.',
            'name.max' => 'Name may not be greater than 255 characters.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'mobile.required' => 'Saudi mobile number is required.',
            'mobile.regex' => 'Mobile number must be a valid Saudi number (starts with 05 and is 10 digits total).',
            'mobile.unique' => 'This mobile number is already registered.',
            'password.required' => 'Password is required.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, and one number.',
            'password_confirmation.required' => 'Password confirmation is required.',
            'approve_policy.required' => 'You must approve the policy to register.',
            'approve_policy.accepted' => 'You must approve the policy to register.',
        ];
    }
}
