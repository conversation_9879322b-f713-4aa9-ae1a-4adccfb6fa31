<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddToFavouritesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'provider_service_id' => [
                'required',
                'integer',
                'exists:provider_services,id,is_active,1,deleted_at,NULL'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'provider_service_id.required' => 'Service ID is required.',
            'provider_service_id.integer' => 'Service ID must be an integer.',
            'provider_service_id.exists' => 'The selected service does not exist or is not available.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'provider_service_id' => 'service',
        ];
    }
}
