<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

/**
 * Provider Basic Information Update Request
 * 
 * Validates provider basic information updates for API endpoints.
 * All fields are required to ensure complete profile information.
 */
class ProviderBasicInfoUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $providerId = $this->user()?->id;

        return [
            // Translatable name fields - REQUIRED
            'translations.ar.name' => [
                'required',
                'string',
                'max:255',
                'min:2'
            ],
            'translations.en.name' => [
                'required',
                'string',
                'max:255',
                'min:2'
            ],

            // Translatable description fields - REQUIRED
            'translations.ar.description' => [
                'required',
                'string',
                'max:1000',
                'min:10'
            ],
            'translations.en.description' => [
                'required',
                'string',
                'max:1000',
                'min:10'
            ],

            // Email field - REQUIRED
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                Rule::unique('service_providers', 'email')->ignore($providerId)
            ],

            // Location fields - REQUIRED
            'city_id' => [
                'required',
                'integer',
                'exists:cities,id'
            ],
            'area_id' => [
                'required',
                'integer',
                'exists:areas,id'
            ],

            // Coordinates - REQUIRED
            'latitude' => [
                'required',
                'numeric',
                'between:-90,90'
            ],
            'longitude' => [
                'required',
                'numeric',
                'between:-180,180'
            ],

            // Logo upload - OPTIONAL
            'logo' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048' // 2MB max
            ],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            // Arabic name messages
            'translations.ar.name.required' => 'Arabic name is required.',
            'translations.ar.name.string' => 'Arabic name must be a valid text.',
            'translations.ar.name.max' => 'Arabic name must not exceed 255 characters.',
            'translations.ar.name.min' => 'Arabic name must be at least 2 characters.',

            // English name messages
            'translations.en.name.required' => 'English name is required.',
            'translations.en.name.string' => 'English name must be a valid text.',
            'translations.en.name.max' => 'English name must not exceed 255 characters.',
            'translations.en.name.min' => 'English name must be at least 2 characters.',

            // Arabic description messages
            'translations.ar.description.required' => 'Arabic description is required.',
            'translations.ar.description.string' => 'Arabic description must be a valid text.',
            'translations.ar.description.max' => 'Arabic description must not exceed 1000 characters.',
            'translations.ar.description.min' => 'Arabic description must be at least 10 characters.',

            // English description messages
            'translations.en.description.required' => 'English description is required.',
            'translations.en.description.string' => 'English description must be a valid text.',
            'translations.en.description.max' => 'English description must not exceed 1000 characters.',
            'translations.en.description.min' => 'English description must be at least 10 characters.',

            // Email messages
            'email.required' => 'Email address is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.max' => 'Email address must not exceed 255 characters.',
            'email.unique' => 'This email address is already in use.',

            // Location messages
            'city_id.required' => 'City selection is required.',
            'city_id.integer' => 'City must be a valid selection.',
            'city_id.exists' => 'Selected city does not exist.',

            'area_id.required' => 'Area selection is required.',
            'area_id.integer' => 'Area must be a valid selection.',
            'area_id.exists' => 'Selected area does not exist.',

            // Coordinates messages
            'latitude.required' => 'Latitude is required.',
            'latitude.numeric' => 'Latitude must be a valid number.',
            'latitude.between' => 'Latitude must be between -90 and 90.',

            'longitude.required' => 'Longitude is required.',
            'longitude.numeric' => 'Longitude must be a valid number.',
            'longitude.between' => 'Longitude must be between -180 and 180.',

            // Logo messages
            'logo.image' => 'Logo must be a valid image file.',
            'logo.mimes' => 'Logo must be a JPEG, PNG, or JPG file.',
            'logo.max' => 'Logo file size must not exceed 2MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'translations.ar.name' => 'Arabic name',
            'translations.en.name' => 'English name',
            'translations.ar.description' => 'Arabic description',
            'translations.en.description' => 'English description',
            'email' => 'email address',
            'city_id' => 'city',
            'area_id' => 'area',
            'latitude' => 'latitude',
            'longitude' => 'longitude',
            'logo' => 'logo',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
