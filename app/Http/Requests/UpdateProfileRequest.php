<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $customerId = auth('sanctum')->id();

        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => [
                'sometimes',
                'string',
                'email',
                Rule::unique('customers', 'email')->ignore($customerId)
            ],
            'mobile' => [
                'sometimes',
                'string',
                'regex:/^(\+966|05)\d{8}$/',
                Rule::unique('customers', 'mobile')->ignore($customerId)
            ],
            'password' => [
                'sometimes',
                'string',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/'
            ],
            'city_id' => ['sometimes', 'integer', 'exists:cities,id'],
            'area_id' => ['sometimes', 'integer', 'exists:areas,id'],
            'gender' => ['sometimes', 'string', 'in:Male,Female,Other'],
        ];
    }

    /**
     * Get the body parameters for Scribe documentation.
     *
     * @return array<string, array>
     */
    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'The customer\'s full name (optional).',
                'example' => 'John Doe',
            ],
            'email' => [
                'description' => 'The customer\'s email address (optional).',
                'example' => '<EMAIL>',
            ],
            'mobile' => [
                'description' => 'Saudi mobile number (optional, starts with +966 or 05).',
                'example' => '0512345678',
            ],
            'password' => [
                'description' => 'New password (optional, min 8 chars with uppercase, lowercase, and number).',
                'example' => 'NewPassword123',
            ],
            'city_id' => [
                'description' => 'City ID (optional).',
                'example' => 1,
            ],
            'area_id' => [
                'description' => 'Area ID (optional).',
                'example' => 1,
            ],
            'gender' => [
                'description' => 'Gender (optional).',
                'example' => 'Male',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.string' => 'Name must be a string.',
            'name.max' => 'Name may not be greater than 255 characters.',
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'mobile.regex' => 'Mobile number must be a valid Saudi number (starts with +966 or 05 and is 10 digits total).',
            'mobile.unique' => 'This mobile number is already registered.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, and one number.',
            'city_id.integer' => 'City ID must be an integer.',
            'city_id.exists' => 'The selected city does not exist.',
            'area_id.integer' => 'Area ID must be an integer.',
            'area_id.exists' => 'The selected area does not exist.',
            'gender.in' => 'Gender must be one of: Male, Female, Other.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert mobile number format if needed
        if ($this->has('mobile')) {
            $mobile = $this->input('mobile');
            // Convert +966 format to 05 format for consistency
            if (str_starts_with($mobile, '+966')) {
                $mobile = '0' . substr($mobile, 4);
                $this->merge(['mobile' => $mobile]);
            }
        }
    }
}
