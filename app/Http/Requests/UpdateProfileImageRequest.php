<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProfileImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048', // 2MB max file size
                'dimensions:min_width=100,min_height=100,max_width=2000,max_height=2000'
            ],
        ];
    }

    /**
     * Get the body parameters for Scribe documentation.
     *
     * @return array<string, array>
     */
    public function bodyParameters(): array
    {
        return [
            'image' => [
                'description' => 'Profile image file (JPEG or PNG, max 2MB, 100x100 to 2000x2000 pixels).',
                'example' => 'No example available',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'image.required' => 'Profile image is required.',
            'image.image' => 'The file must be a valid image.',
            'image.mimes' => 'Profile image must be a JPEG or PNG file.',
            'image.max' => 'Profile image may not be larger than 2MB.',
            'image.dimensions' => 'Profile image must be between 100x100 and 2000x2000 pixels.',
        ];
    }
}
