<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CreateCityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Arabic translation
            'ar' => ['required', 'array'],
            'ar.name' => [
                'required',
                'string',
                'max:255',
                'unique:city_translations,name,NULL,id,locale,ar'
            ],

            // English translation
            'en' => ['required', 'array'],
            'en.name' => [
                'required',
                'string',
                'max:255',
                'unique:city_translations,name,NULL,id,locale,en'
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'ar.name' => __('admin/cities.fields.name_ar'),
            'en.name' => __('admin/cities.fields.name_en'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'ar.name.required' => __('admin/cities.validation.name_ar_required'),
            'ar.name.unique' => __('admin/cities.validation.name_ar_unique'),
            'en.name.required' => __('admin/cities.validation.name_en_required'),
            'en.name.unique' => __('admin/cities.validation.name_en_unique'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure we have the required structure
        if (!$this->has('ar')) {
            $this->merge(['ar' => []]);
        }
        if (!$this->has('en')) {
            $this->merge(['en' => []]);
        }
    }
}
