<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization should be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = $this->route('user');

        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                'unique:users,email,' . $userId
            ],
            'mobile' => [
                'nullable',
                'string',
                'regex:/^05[0-9]{8}$/',
                'unique:users,mobile,' . $userId
            ],
            'password' => [
                'nullable',
                'string',
                Password::min(8)
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
            ],
            'password_confirmation' => ['nullable', 'string', 'same:password'],
            'is_active' => ['boolean'],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => __('admin/users.validation.name_required'),
            'name.string' => __('admin/users.validation.name_string'),
            'name.max' => __('admin/users.validation.name_max'),
            'email.required' => __('admin/users.validation.email_required'),
            'email.email' => __('admin/users.validation.email_format'),
            'email.unique' => __('admin/users.validation.email_unique'),
            'mobile.regex' => __('admin/users.validation.mobile_format'),
            'mobile.unique' => __('admin/users.validation.mobile_unique'),
            'password_confirmation.same' => __('admin/users.validation.password_confirmation_same'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => __('admin/users.fields.name'),
            'email' => __('admin/users.fields.email'),
            'mobile' => __('admin/users.fields.mobile'),
            'password' => __('admin/users.fields.password'),
            'password_confirmation' => __('admin/users.fields.password_confirmation'),
            'is_active' => __('admin/users.fields.is_active'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Remove password fields if they are empty
        if (empty($this->password)) {
            $this->request->remove('password');
            $this->request->remove('password_confirmation');
        }
    }
}
