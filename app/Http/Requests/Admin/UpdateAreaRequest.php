<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $areaId = $this->route('area');

        return [
            // City relationship
            'city_id' => [
                'required',
                'integer',
                'exists:cities,id'
            ],

            // Translations
            'translations' => ['required', 'array'],

            // Arabic translation
            'translations.ar' => ['required', 'array'],
            'translations.ar.name' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) use ($areaId) {
                    $cityId = $this->input('city_id');
                    if ($cityId) {
                        $exists = \DB::table('area_translations')
                            ->join('areas', 'areas.id', '=', 'area_translations.area_id')
                            ->where('area_translations.name', $value)
                            ->where('area_translations.locale', 'ar')
                            ->where('areas.city_id', $cityId)
                            ->where('areas.id', '!=', $areaId)
                            ->exists();
                        
                        if ($exists) {
                            $fail(__('admin/areas.validation.name_ar_unique_in_city'));
                        }
                    }
                }
            ],

            // English translation
            'translations.en' => ['required', 'array'],
            'translations.en.name' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) use ($areaId) {
                    $cityId = $this->input('city_id');
                    if ($cityId) {
                        $exists = \DB::table('area_translations')
                            ->join('areas', 'areas.id', '=', 'area_translations.area_id')
                            ->where('area_translations.name', $value)
                            ->where('area_translations.locale', 'en')
                            ->where('areas.city_id', $cityId)
                            ->where('areas.id', '!=', $areaId)
                            ->exists();
                        
                        if ($exists) {
                            $fail(__('admin/areas.validation.name_en_unique_in_city'));
                        }
                    }
                }
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'city_id' => __('admin/areas.fields.city'),
            'translations.ar.name' => __('admin/areas.fields.name_ar'),
            'translations.en.name' => __('admin/areas.fields.name_en'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'city_id.required' => __('admin/areas.validation.city_required'),
            'city_id.exists' => __('admin/areas.validation.city_exists'),
            'translations.ar.name.required' => __('admin/areas.validation.name_ar_required'),
            'translations.en.name.required' => __('admin/areas.validation.name_en_required'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure we have the required structure
        if (!$this->has('translations')) {
            $this->merge(['translations' => []]);
        }
        if (!$this->has('translations.ar')) {
            $this->merge(['translations.ar' => []]);
        }
        if (!$this->has('translations.en')) {
            $this->merge(['translations.en' => []]);
        }
    }
}
