<?php

namespace App\Http\Requests\Admin;

use App\Models\ContactMessage;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],
            'message' => ['required', 'string', 'max:5000'],
            'status' => [
                'required',
                'string',
                Rule::in([
                    ContactMessage::STATUS_PENDING,
                    ContactMessage::STATUS_READ,
                    ContactMessage::STATUS_ARCHIVED,
                ])
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'title' => __('admin/messages.form.title'),
            'name' => __('admin/messages.form.name'),
            'email' => __('admin/messages.form.email'),
            'message' => __('admin/messages.form.message'),
            'status' => __('admin/messages.form.status'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.required' => __('admin/messages.validation.title_required'),
            'title.max' => __('admin/messages.validation.title_max'),
            'name.required' => __('admin/messages.validation.name_required'),
            'name.max' => __('admin/messages.validation.name_max'),
            'email.required' => __('admin/messages.validation.email_required'),
            'email.email' => __('admin/messages.validation.email_format'),
            'email.max' => __('admin/messages.validation.email_max'),
            'message.required' => __('admin/messages.validation.message_required'),
            'message.max' => __('admin/messages.validation.message_max'),
            'status.required' => __('admin/messages.validation.status_required'),
            'status.in' => __('admin/messages.validation.status_invalid'),
        ];
    }
}
