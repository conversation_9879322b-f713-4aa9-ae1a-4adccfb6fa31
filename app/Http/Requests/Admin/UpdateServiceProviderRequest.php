<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateServiceProviderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Translatable fields
            'translations' => ['required', 'array'],
            'translations.ar' => ['required', 'array'],
            'translations.ar.name' => ['required', 'string', 'max:255'],
            'translations.ar.description' => ['nullable', 'string', 'max:5000'],
            'translations.en' => ['required', 'array'],
            'translations.en.name' => ['required', 'string', 'max:255'],
            'translations.en.description' => ['nullable', 'string', 'max:5000'],

            // Non-translatable fields
            'logo' => ['nullable', 'string', 'max:255'],
            'rating' => ['nullable', 'numeric', 'min:0', 'max:5'],
            'city_id' => ['nullable', 'integer', 'exists:cities,id'],
            'area_id' => ['nullable', 'integer', 'exists:areas,id'],
            'longitude' => ['nullable', 'numeric', 'between:-180,180'],
            'latitude' => ['nullable', 'numeric', 'between:-90,90'],
            'is_active' => ['boolean'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'translations.ar.name' => __('admin/providers.fields.name_ar'),
            'translations.ar.description' => __('admin/providers.fields.description_ar'),
            'translations.en.name' => __('admin/providers.fields.name_en'),
            'translations.en.description' => __('admin/providers.fields.description_en'),
            'logo' => __('admin/providers.fields.logo'),
            'rating' => __('admin/providers.fields.rating'),
            'city_id' => __('admin/providers.fields.city'),
            'area_id' => __('admin/providers.fields.area'),
            'longitude' => __('admin/providers.fields.longitude'),
            'latitude' => __('admin/providers.fields.latitude'),
            'is_active' => __('admin/providers.fields.status'),
        ];
    }
}
