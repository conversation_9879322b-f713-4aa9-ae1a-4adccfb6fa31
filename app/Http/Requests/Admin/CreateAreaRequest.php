<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CreateAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // City relationship
            'city_id' => [
                'required',
                'integer',
                'exists:cities,id'
            ],

            // Arabic translation
            'ar' => ['required', 'array'],
            'ar.name' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    $cityId = $this->input('city_id');
                    if ($cityId) {
                        $exists = \DB::table('area_translations')
                            ->join('areas', 'areas.id', '=', 'area_translations.area_id')
                            ->where('area_translations.name', $value)
                            ->where('area_translations.locale', 'ar')
                            ->where('areas.city_id', $cityId)
                            ->exists();

                        if ($exists) {
                            $fail(__('admin/areas.validation.name_ar_unique_in_city'));
                        }
                    }
                }
            ],

            // English translation
            'en' => ['required', 'array'],
            'en.name' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    $cityId = $this->input('city_id');
                    if ($cityId) {
                        $exists = \DB::table('area_translations')
                            ->join('areas', 'areas.id', '=', 'area_translations.area_id')
                            ->where('area_translations.name', $value)
                            ->where('area_translations.locale', 'en')
                            ->where('areas.city_id', $cityId)
                            ->exists();

                        if ($exists) {
                            $fail(__('admin/areas.validation.name_en_unique_in_city'));
                        }
                    }
                }
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'city_id' => __('admin/areas.fields.city'),
            'ar.name' => __('admin/areas.fields.name_ar'),
            'en.name' => __('admin/areas.fields.name_en'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'city_id.required' => __('admin/areas.validation.city_required'),
            'city_id.exists' => __('admin/areas.validation.city_exists'),
            'ar.name.required' => __('admin/areas.validation.name_ar_required'),
            'ar.name.unique' => __('admin/areas.validation.name_ar_unique_in_city'),
            'en.name.required' => __('admin/areas.validation.name_en_required'),
            'en.name.unique' => __('admin/areas.validation.name_en_unique_in_city'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure we have the required structure
        if (!$this->has('ar')) {
            $this->merge(['ar' => []]);
        }
        if (!$this->has('en')) {
            $this->merge(['en' => []]);
        }
    }
}
