<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

/**
 * Provider Authentication Middleware
 * 
 * Handles authentication for provider dashboard routes.
 * Ensures only active service providers can access provider functionality.
 */
class ProviderAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if provider is authenticated with provider guard
        if (!Auth::guard('provider')->check()) {
            return $this->redirectToLogin($request);
        }

        // Get the authenticated provider
        $provider = Auth::guard('provider')->user();

        // Check if provider is active
        if (!$provider || !$provider->is_active) {
            // Log out the inactive provider
            Auth::guard('provider')->logout();

            // Invalidate the session
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            // Redirect to login with error message
            return $this->redirectToLogin($request, 'Your provider account is inactive. Please contact support.');
        }

        // Check if provider account is approved (if you have approval system)
        if (isset($provider->status) && $provider->status !== 'approved') {
            // Log out the unapproved provider
            Auth::guard('provider')->logout();

            // Invalidate the session
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            // Redirect to login with error message
            return $this->redirectToLogin($request, 'Your provider account is pending approval. Please wait for admin approval.');
        }

        return $next($request);
    }

    /**
     * Redirect to provider login page with localization support
     */
    private function redirectToLogin(Request $request, ?string $message = null): Response
    {
        // Get the current locale
        $locale = LaravelLocalization::getCurrentLocale();

        // Generate localized login URL for provider subdomain
        $loginUrl = LaravelLocalization::getLocalizedURL($locale, '/login');

        // If it's an AJAX request, return JSON response
        if ($request->expectsJson()) {
            return response()->json([
                'message' => $message ?? 'Unauthenticated.',
                'redirect' => $loginUrl
            ], 401);
        }

        // For regular requests, redirect with flash message
        if ($message) {
            return redirect($loginUrl)->with('error', $message);
        }

        return redirect($loginUrl);
    }
}
