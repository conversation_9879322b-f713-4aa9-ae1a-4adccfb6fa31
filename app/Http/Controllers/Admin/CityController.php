<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateCityRequest;
use App\Http\Requests\Admin\UpdateCityRequest;
use App\Services\CityService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;

/**
 * Admin City Controller
 * 
 * Handles CRUD operations for cities in the admin panel.
 */
class CityController extends Controller
{
    public function __construct(
        private CityService $cityService
    ) {
    }

    /**
     * Display a listing of cities.
     */
    public function index(Request $request): View
    {
        $filters = [
            'search' => $request->get('search'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to'),
            'sort_by' => $request->get('sort_by', 'created_at'),
            'sort_direction' => $request->get('sort_direction', 'desc'),
        ];

        // Remove empty filters
        $filters = array_filter($filters, function ($value) {
            return $value !== null && $value !== '';
        });

        $cities = $this->cityService->getCitiesWithFilters($filters, 15);
        $statistics = $this->cityService->getCitiesStatistics();

        return view('admin.cities.index', compact('cities', 'statistics', 'filters'));
    }

    /**
     * Show the form for creating a new city.
     */
    public function create(): View
    {
        return view('admin.cities.create');
    }

    /**
     * Store a newly created city in storage.
     */
    public function store(CreateCityRequest $request): RedirectResponse
    {
        try {
            $this->cityService->create($request->validated());

            return redirect()
                ->route('admin.cities.index')
                ->with('success', __('admin/cities.messages.created_successfully'));
        } catch (ValidationException $e) {
            return redirect()
                ->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/cities.messages.create_failed'))
                ->withInput();
        }
    }

    /**
     * Display the specified city.
     */
    public function show(int $id): View
    {
        $city = $this->cityService->getByIdOrFail($id);
        $city->load(['areas', 'customers', 'serviceProviders']);

        return view('admin.cities.show', compact('city'));
    }

    /**
     * Show the form for editing the specified city.
     */
    public function edit(int $id): View
    {
        $city = $this->cityService->getByIdOrFail($id);
        return view('admin.cities.edit', compact('city'));
    }

    /**
     * Update the specified city in storage.
     */
    public function update(UpdateCityRequest $request, int $id): RedirectResponse
    {
        try {
            $this->cityService->update($id, $request->validated());

            return redirect()
                ->route('admin.cities.index')
                ->with('success', __('admin/cities.messages.updated_successfully'));
        } catch (ValidationException $e) {
            return redirect()
                ->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/cities.messages.update_failed'))
                ->withInput();
        }
    }

    /**
     * Remove the specified city from storage (soft delete).
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $this->cityService->delete($id);

            return redirect()
                ->route('admin.cities.index')
                ->with('success', __('admin/cities.messages.deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/cities.messages.delete_failed'));
        }
    }

    /**
     * Restore the specified city from trash.
     */
    public function restore(int $id): RedirectResponse
    {
        try {
            $this->cityService->restore($id);

            return redirect()
                ->route('admin.cities.index')
                ->with('success', __('admin/cities.messages.restored_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/cities.messages.restore_failed'));
        }
    }

    /**
     * Force delete the specified city permanently.
     */
    public function forceDelete(int $id): RedirectResponse
    {
        try {
            $this->cityService->forceDelete($id);

            return redirect()
                ->route('admin.cities.index')
                ->with('success', __('admin/cities.messages.force_deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/cities.messages.force_delete_failed'));
        }
    }

    /**
     * Bulk delete cities.
     */
    public function bulkDelete(Request $request): RedirectResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:cities,id'
        ]);

        try {
            $this->cityService->bulkDelete($request->input('ids'));

            return redirect()
                ->route('admin.cities.index')
                ->with('success', __('admin/cities.messages.bulk_deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/cities.messages.bulk_delete_failed'));
        }
    }

    /**
     * Bulk restore cities.
     */
    public function bulkRestore(Request $request): RedirectResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer'
        ]);

        try {
            $this->cityService->bulkRestore($request->input('ids'));

            return redirect()
                ->route('admin.cities.index')
                ->with('success', __('admin/cities.messages.bulk_restored_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/cities.messages.bulk_restore_failed'));
        }
    }
}
