<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

/**
 * Admin Dashboard Controller
 * 
 * Handles the main admin dashboard functionality including
 * statistics, recent activity, and overview data.
 */
class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     * 
     * @return View
     */
    public function index(): View
    {
        // In a real application, you would fetch actual data from your models
        // For now, we'll use sample data that matches the dashboard view
        
        $statistics = [
            'total_users' => $this->getTotalUsers(),
            'service_providers' => $this->getServiceProviders(),
            'total_services' => $this->getTotalServices(),
            'active_bookings' => $this->getActiveBookings(),
        ];
        
        $recentActivity = $this->getRecentActivity();
        
        return view('admin.dashboard.index', compact('statistics', 'recentActivity'));
    }
    
    /**
     * Get total users count.
     * 
     * @return array
     */
    private function getTotalUsers(): array
    {
        // In a real application, you would query your User model
        // Example: User::count()
        return [
            'count' => 1247,
            'growth' => 12, // percentage
            'trend' => 'up'
        ];
    }
    
    /**
     * Get service providers count.
     * 
     * @return array
     */
    private function getServiceProviders(): array
    {
        // In a real application, you would query your ServiceProvider model
        // Example: ServiceProvider::count()
        return [
            'count' => 89,
            'growth' => 8, // percentage
            'trend' => 'up'
        ];
    }
    
    /**
     * Get total services count.
     * 
     * @return array
     */
    private function getTotalServices(): array
    {
        // In a real application, you would query your Service model
        // Example: Service::count()
        return [
            'count' => 456,
            'growth' => 15, // percentage
            'trend' => 'up'
        ];
    }
    
    /**
     * Get active bookings count.
     * 
     * @return array
     */
    private function getActiveBookings(): array
    {
        // In a real application, you would query your Booking model
        // Example: Booking::where('status', 'active')->count()
        return [
            'count' => 23,
            'growth' => -3, // percentage (negative for decrease)
            'trend' => 'down'
        ];
    }
    
    /**
     * Get recent activity data.
     * 
     * @return array
     */
    private function getRecentActivity(): array
    {
        // In a real application, you would query your activity logs or related models
        // This could be from an Activity model, User registrations, Service additions, etc.
        return [
            [
                'user' => [
                    'name' => 'John Doe',
                    'avatar' => 'assets/media/avatars/300-4.png'
                ],
                'action' => 'registered as a new user',
                'time' => '2 mins ago',
                'status' => 'success'
            ],
            [
                'user' => [
                    'name' => 'Sarah Wilson',
                    'avatar' => 'assets/media/avatars/300-1.png'
                ],
                'action' => 'added a new service',
                'time' => '5 mins ago',
                'status' => 'success'
            ],
            [
                'user' => [
                    'name' => 'Mike Johnson',
                    'avatar' => 'assets/media/avatars/300-2.png'
                ],
                'action' => 'updated profile information',
                'time' => '10 mins ago',
                'status' => 'warning'
            ]
        ];
    }
    
    /**
     * Get chart data for user growth.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserGrowthData(Request $request)
    {
        $period = $request->get('period', '7'); // days
        
        // In a real application, you would query your database for actual data
        // Example: User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
        //              ->where('created_at', '>=', now()->subDays($period))
        //              ->groupBy('date')
        //              ->get()
        
        $data = [
            'labels' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            'data' => [30, 40, 35, 50, 49, 60, 70]
        ];
        
        return response()->json($data);
    }
    
    /**
     * Get chart data for service categories.
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getServiceCategoriesData()
    {
        // In a real application, you would query your database for actual data
        // Example: ServiceCategory::withCount('services')->get()
        
        $data = [
            'labels' => ['Cleaning', 'Maintenance', 'Delivery', 'Beauty', 'Others'],
            'data' => [44, 55, 13, 43, 22]
        ];
        
        return response()->json($data);
    }
}
