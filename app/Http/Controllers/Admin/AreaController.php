<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateAreaRequest;
use App\Http\Requests\Admin\UpdateAreaRequest;
use App\Services\AreaService;
use App\Services\CityService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;

/**
 * Admin Area Controller
 * 
 * Handles CRUD operations for areas in the admin panel.
 */
class AreaController extends Controller
{
    public function __construct(
        private AreaService $areaService,
        private CityService $cityService
    ) {
    }

    /**
     * Display a listing of areas.
     */
    public function index(Request $request): View
    {
        $filters = [
            'search' => $request->get('search'),
            'city_id' => $request->get('city_id'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to'),
            'sort_by' => $request->get('sort_by', 'created_at'),
            'sort_direction' => $request->get('sort_direction', 'desc'),
        ];

        // Remove empty filters
        $filters = array_filter($filters, function ($value) {
            return $value !== null && $value !== '';
        });

        $areas = $this->areaService->getAreasWithFilters($filters, 15);
        $statistics = $this->areaService->getAreasStatistics();
        $cities = $this->cityService->getAllCities();

        return view('admin.areas.index', compact('areas', 'statistics', 'filters', 'cities'));
    }

    /**
     * Show the form for creating a new area.
     */
    public function create(): View
    {
        $cities = $this->cityService->getAllCities();
        return view('admin.areas.create', compact('cities'));
    }

    /**
     * Store a newly created area in storage.
     */
    public function store(CreateAreaRequest $request): RedirectResponse
    {
        try {
            $this->areaService->create($request->validated());

            return redirect()
                ->route('admin.areas.index')
                ->with('success', __('admin/areas.messages.created_successfully'));
        } catch (ValidationException $e) {
            return redirect()
                ->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/areas.messages.create_failed'))
                ->withInput();
        }
    }

    /**
     * Display the specified area.
     */
    public function show(int $id): View
    {
        $area = $this->areaService->getByIdOrFail($id);
        $area->load([
            'city',
            'customers' => function ($query) {
                $query->latest()->take(10);
            },
            'serviceProviders' => function ($query) {
                $query->latest()->take(10);
            }
        ]);
        $area->loadCount(['customers', 'serviceProviders']);

        return view('admin.areas.show', compact('area'));
    }

    /**
     * Show the form for editing the specified area.
     */
    public function edit(int $id): View
    {
        $area = $this->areaService->getByIdOrFail($id);
        $cities = $this->cityService->getAllCities();

        return view('admin.areas.edit', compact('area', 'cities'));
    }

    /**
     * Update the specified area in storage.
     */
    public function update(UpdateAreaRequest $request, int $id): RedirectResponse
    {
        try {
            $this->areaService->update($id, $request->validated());

            return redirect()
                ->route('admin.areas.index')
                ->with('success', __('admin/areas.messages.updated_successfully'));
        } catch (ValidationException $e) {
            return redirect()
                ->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/areas.messages.update_failed'))
                ->withInput();
        }
    }

    /**
     * Remove the specified area from storage (soft delete).
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $this->areaService->delete($id);

            return redirect()
                ->route('admin.areas.index')
                ->with('success', __('admin/areas.messages.deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/areas.messages.delete_failed'));
        }
    }

    /**
     * Restore the specified area from trash.
     */
    public function restore(int $id): RedirectResponse
    {
        try {
            $this->areaService->restore($id);

            return redirect()
                ->route('admin.areas.index')
                ->with('success', __('admin/areas.messages.restored_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/areas.messages.restore_failed'));
        }
    }

    /**
     * Force delete the specified area permanently.
     */
    public function forceDelete(int $id): RedirectResponse
    {
        try {
            $this->areaService->forceDelete($id);

            return redirect()
                ->route('admin.areas.index')
                ->with('success', __('admin/areas.messages.force_deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/areas.messages.force_delete_failed'));
        }
    }

    /**
     * Bulk delete areas.
     */
    public function bulkDelete(Request $request): RedirectResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:areas,id'
        ]);

        try {
            $this->areaService->bulkDelete($request->input('ids'));

            return redirect()
                ->route('admin.areas.index')
                ->with('success', __('admin/areas.messages.bulk_deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/areas.messages.bulk_delete_failed'));
        }
    }

    /**
     * Bulk restore areas.
     */
    public function bulkRestore(Request $request): RedirectResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer'
        ]);

        try {
            $this->areaService->bulkRestore($request->input('ids'));

            return redirect()
                ->route('admin.areas.index')
                ->with('success', __('admin/areas.messages.bulk_restored_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/areas.messages.bulk_restore_failed'));
        }
    }
}
