<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateUserRequest;
use App\Http\Requests\Admin\UpdateUserRequest;
use App\Services\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;

/**
 * Admin User Controller
 * 
 * Handles CRUD operations for users in the admin panel.
 */
class UserController extends Controller
{
    public function __construct(
        private UserService $userService
    ) {
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request): View
    {
        $filters = [
            'search' => $request->get('search'),
            'is_active' => $request->get('is_active'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to'),
        ];

        // Remove empty filters
        $filters = array_filter($filters, function ($value) {
            return $value !== null && $value !== '';
        });

        $users = $this->userService->getUsersWithFilters($filters, 15);
        $statistics = $this->userService->getUsersStatistics();

        return view('admin.users.index', compact('users', 'statistics', 'filters'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create(): View
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(CreateUserRequest $request): RedirectResponse
    {
        try {
            $this->userService->create($request->validated());

            return redirect()
                ->route('admin.users.index')
                ->with('success', __('admin/users.messages.created_successfully'));
        } catch (ValidationException $e) {
            return redirect()
                ->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'An error occurred while creating the user.')
                ->withInput();
        }
    }

    /**
     * Display the specified user.
     */
    public function show(int $id): View
    {
        try {
            $user = $this->userService->getByIdOrFail($id);
            return view('admin.users.show', compact('user'));
        } catch (\Exception $e) {
            abort(404, __('admin/users.messages.not_found'));
        }
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(int $id): View
    {
        try {
            $user = $this->userService->getByIdOrFail($id);
            return view('admin.users.edit', compact('user'));
        } catch (\Exception $e) {
            abort(404, __('admin/users.messages.not_found'));
        }
    }

    /**
     * Update the specified user in storage.
     */
    public function update(UpdateUserRequest $request, int $id): RedirectResponse
    {
        try {
            $this->userService->update($id, $request->validated());

            return redirect()
                ->route('admin.users.index')
                ->with('success', __('admin/users.messages.updated_successfully'));
        } catch (ValidationException $e) {
            return redirect()
                ->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'An error occurred while updating the user.')
                ->withInput();
        }
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $this->userService->delete($id);

            return redirect()
                ->route('admin.users.index')
                ->with('success', __('admin/users.messages.deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'An error occurred while deleting the user.');
        }
    }

    /**
     * Activate the specified user.
     */
    public function activate(int $id): RedirectResponse
    {
        try {
            $this->userService->activateUser($id);

            return redirect()
                ->back()
                ->with('success', __('admin/users.messages.activated_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'An error occurred while activating the user.');
        }
    }

    /**
     * Deactivate the specified user.
     */
    public function deactivate(int $id): RedirectResponse
    {
        try {
            $this->userService->deactivateUser($id);

            return redirect()
                ->back()
                ->with('success', __('admin/users.messages.deactivated_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'An error occurred while deactivating the user.');
        }
    }
}
