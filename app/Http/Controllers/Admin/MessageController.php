<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateMessageRequest;
use App\Services\ContactMessageService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

/**
 * Admin Message Controller
 *
 * Handles CRUD operations for contact messages in the admin panel.
 */
class MessageController extends Controller
{
    public function __construct(
        private ContactMessageService $contactMessageService
    ) {
    }

    /**
     * Display a listing of messages.
     */
    public function index(Request $request): View
    {
        $filters = [
            'search' => $request->get('search'),
            'status' => $request->get('status'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to'),
        ];

        // Remove empty filters
        $filters = array_filter($filters, function ($value) {
            return $value !== null && $value !== '';
        });

        $messages = $this->contactMessageService->getMessagesWithFilters($filters, 15);
        $statistics = $this->contactMessageService->getStatistics();

        return view('admin.messages.index', compact('messages', 'statistics', 'filters'));
    }

    /**
     * Display the specified message.
     */
    public function show(int $id): View
    {
        $message = $this->contactMessageService->getById($id);

        if (!$message) {
            abort(404);
        }

        // Mark as read if it's pending
        if ($message->isPending()) {
            $this->contactMessageService->markAsRead($id);
            $message->refresh();
        }

        return view('admin.messages.show', compact('message'));
    }

    /**
     * Show the form for editing the specified message.
     */
    public function edit(int $id): View
    {
        $message = $this->contactMessageService->getById($id);

        if (!$message) {
            abort(404);
        }

        return view('admin.messages.edit', compact('message'));
    }

    /**
     * Update the specified message in storage.
     */
    public function update(UpdateMessageRequest $request, int $id): RedirectResponse
    {
        try {
            $this->contactMessageService->update($id, $request->validated());

            return redirect()
                ->route('admin.messages.index')
                ->with('success', __('admin/messages.messages.updated_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/messages.messages.update_failed'))
                ->withInput();
        }
    }

    /**
     * Remove the specified message from storage.
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $this->contactMessageService->delete($id);

            return redirect()
                ->route('admin.messages.index')
                ->with('success', __('admin/messages.messages.deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/messages.messages.delete_failed'));
        }
    }

    /**
     * Mark the specified message as read.
     */
    public function markAsRead(int $id): RedirectResponse
    {
        try {
            $this->contactMessageService->markAsRead($id);

            return redirect()
                ->back()
                ->with('success', __('admin/messages.messages.marked_as_read'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/messages.messages.update_failed'));
        }
    }

    /**
     * Mark the specified message as archived.
     */
    public function markAsArchived(int $id): RedirectResponse
    {
        try {
            $this->contactMessageService->markAsArchived($id);

            return redirect()
                ->back()
                ->with('success', __('admin/messages.messages.marked_as_archived'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/messages.messages.update_failed'));
        }
    }

    /**
     * Bulk mark messages as read.
     */
    public function bulkMarkAsRead(Request $request): RedirectResponse
    {
        try {
            $ids = json_decode($request->input('ids'), true);

            if (empty($ids)) {
                return redirect()
                    ->back()
                    ->with('error', __('admin/messages.messages.no_items_selected'));
            }

            $this->contactMessageService->bulkUpdateStatus($ids, 'read');

            return redirect()
                ->route('admin.messages.index')
                ->with('success', __('admin/messages.messages.bulk_action_success'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/messages.messages.bulk_action_failed'));
        }
    }

    /**
     * Bulk archive messages.
     */
    public function bulkArchive(Request $request): RedirectResponse
    {
        try {
            $ids = json_decode($request->input('ids'), true);

            if (empty($ids)) {
                return redirect()
                    ->back()
                    ->with('error', __('admin/messages.messages.no_items_selected'));
            }

            $this->contactMessageService->bulkUpdateStatus($ids, 'archived');

            return redirect()
                ->route('admin.messages.index')
                ->with('success', __('admin/messages.messages.bulk_action_success'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/messages.messages.bulk_action_failed'));
        }
    }

    /**
     * Bulk delete messages.
     */
    public function bulkDelete(Request $request): RedirectResponse
    {
        try {
            $ids = json_decode($request->input('ids'), true);

            if (empty($ids)) {
                return redirect()
                    ->back()
                    ->with('error', __('admin/messages.messages.no_items_selected'));
            }

            $this->contactMessageService->bulkDelete($ids);

            return redirect()
                ->route('admin.messages.index')
                ->with('success', __('admin/messages.messages.bulk_action_success'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/messages.messages.bulk_action_failed'));
        }
    }
}
