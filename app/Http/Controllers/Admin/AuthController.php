<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\AdminAuthService;
use App\Services\LanguageService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

/**
 * Admin Authentication Controller
 * 
 * Handles admin panel authentication including login, logout,
 * and authentication views with localization support.
 */
class AuthController extends Controller
{
    public function __construct(
        private AdminAuthService $adminAuthService,
        private LanguageService $languageService
    ) {
    }

    /**
     * Show the admin login form
     *
     * @return View|RedirectResponse
     */
    public function showLoginForm(): View|RedirectResponse
    {
        // Redirect to dashboard if already authenticated
        if ($this->adminAuthService->isAuthenticated()) {
            return redirect()->route('admin.dashboard');
        }

        return view('admin.auth.login', [
            'languageService' => $this->languageService
        ]);
    }

    /**
     * Handle admin login request
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function login(Request $request): RedirectResponse
    {
        // Validate the request
        $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required', 'string'],
            'remember' => ['nullable', 'boolean'],
        ], [
            'email.required' => __('admin/auth.email_required'),
            'email.email' => __('admin/auth.email_invalid'),
            'password.required' => __('admin/auth.password_required'),
        ]);

        try {
            // Attempt login
            $result = $this->adminAuthService->login(
                $request->email,
                $request->password,
                $request->boolean('remember', false)
            );

            if ($result === false) {
                return back()
                    ->withInput($request->only('email', 'remember'))
                    ->withErrors(['email' => __('admin/auth.invalid_credentials')]);
            }

            // Login successful - redirect to intended page or dashboard
            $intendedUrl = $request->session()->pull('url.intended');

            if ($intendedUrl) {
                return redirect($intendedUrl)->with('success', __('admin/auth.login_success'));
            }

            // Generate localized dashboard URL
            $locale = LaravelLocalization::getCurrentLocale();
            $dashboardUrl = LaravelLocalization::getLocalizedURL($locale, '/admin/dashboard');

            return redirect($dashboardUrl)->with('success', __('admin/auth.login_success'));

        } catch (ValidationException $e) {
            return back()
                ->withInput($request->only('email', 'remember'))
                ->withErrors($e->errors());
        } catch (\Exception $e) {
            return back()
                ->withInput($request->only('email', 'remember'))
                ->withErrors(['email' => __('admin/auth.login_error')]);
        }
    }

    /**
     * Handle admin logout request
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function logout(Request $request): RedirectResponse
    {
        try {
            // Perform logout
            $this->adminAuthService->logout();

            // Generate localized login URL
            $locale = LaravelLocalization::getCurrentLocale();
            $loginUrl = LaravelLocalization::getLocalizedURL($locale, '/admin/login');

            return redirect($loginUrl)->with('success', __('admin/auth.logout_success'));

        } catch (\Exception $e) {
            // Even if logout fails, redirect to login page
            $locale = LaravelLocalization::getCurrentLocale();
            $loginUrl = LaravelLocalization::getLocalizedURL($locale, '/admin/login');

            return redirect($loginUrl)->with('error', __('admin/auth.logout_error'));
        }
    }
}
