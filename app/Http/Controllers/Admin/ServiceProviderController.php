<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateServiceProviderRequest;
use App\Services\ServiceProviderService;
use App\Services\CityService;
use App\Services\AreaService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;

class ServiceProviderController extends Controller
{
    public function __construct(
        private ServiceProviderService $serviceProviderService,
        private CityService $cityService,
        private AreaService $areaService
    ) {
    }

    /**
     * Display a listing of service providers.
     */
    public function index(Request $request): View
    {
        $filters = [
            'search' => $request->get('search'),
            'city_id' => $request->get('city_id'),
            'area_id' => $request->get('area_id'),
            'is_active' => $request->get('is_active'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to'),
            'sort_by' => $request->get('sort_by', 'created_at'),
            'sort_direction' => $request->get('sort_direction', 'desc'),
        ];

        // Remove empty filters
        $filters = array_filter($filters, function ($value) {
            return $value !== null && $value !== '';
        });

        $providers = $this->serviceProviderService->getProvidersWithFilters($filters, 15);
        $statistics = $this->serviceProviderService->getProvidersStatistics();
        $cities = $this->cityService->getAllCities();
        $areas = $this->areaService->getAllAreas();

        return view('admin.providers.index', compact('providers', 'statistics', 'filters', 'cities', 'areas'));
    }

    /**
     * Display the specified service provider.
     */
    public function show(int $id): View
    {
        $provider = $this->serviceProviderService->getWithDetails($id);

        if (!$provider) {
            abort(404, __('admin/providers.messages.not_found'));
        }

        return view('admin.providers.show', compact('provider'));
    }

    /**
     * Show the form for editing the specified service provider.
     */
    public function edit(int $id): View
    {
        $provider = $this->serviceProviderService->getWithDetails($id);

        if (!$provider) {
            abort(404, __('admin/providers.messages.not_found'));
        }

        $cities = $this->cityService->getAllCities();
        $areas = $this->areaService->getAllAreas();

        return view('admin.providers.edit', compact('provider', 'cities', 'areas'));
    }

    /**
     * Update the specified service provider in storage.
     */
    public function update(UpdateServiceProviderRequest $request, int $id): RedirectResponse
    {
        try {
            $this->serviceProviderService->updateWithTranslations($id, $request->validated());

            return redirect()
                ->route('admin.providers.index')
                ->with('success', __('admin/providers.messages.updated_successfully'));
        } catch (ValidationException $e) {
            return redirect()
                ->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.update_failed'))
                ->withInput();
        }
    }

    /**
     * Remove the specified service provider from storage.
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $this->serviceProviderService->delete($id);

            return redirect()
                ->route('admin.providers.index')
                ->with('success', __('admin/providers.messages.deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.delete_failed'));
        }
    }

    /**
     * Restore soft deleted service provider.
     */
    public function restore(int $id): RedirectResponse
    {
        try {
            $this->serviceProviderService->restore($id);

            return redirect()
                ->route('admin.providers.index')
                ->with('success', __('admin/providers.messages.restored_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.restore_failed'));
        }
    }

    /**
     * Force delete service provider.
     */
    public function forceDelete(int $id): RedirectResponse
    {
        try {
            $this->serviceProviderService->forceDelete($id);

            return redirect()
                ->route('admin.providers.index')
                ->with('success', __('admin/providers.messages.force_deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.force_delete_failed'));
        }
    }

    /**
     * Bulk delete service providers.
     */
    public function bulkDelete(Request $request): RedirectResponse
    {
        $ids = $request->input('ids', []);

        // If ids is a JSON string, decode it to array
        if (is_string($ids)) {
            $ids = json_decode($ids, true) ?? [];
        }

        if (empty($ids)) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.no_items_selected'));
        }

        try {
            $count = $this->serviceProviderService->bulkDelete($ids);

            return redirect()
                ->route('admin.providers.index')
                ->with('success', __('admin/providers.messages.bulk_deleted_successfully', ['count' => $count]));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.bulk_delete_failed'));
        }
    }

    /**
     * Bulk restore service providers.
     */
    public function bulkRestore(Request $request): RedirectResponse
    {
        $ids = $request->input('ids', []);

        // If ids is a JSON string, decode it to array
        if (is_string($ids)) {
            $ids = json_decode($ids, true) ?? [];
        }

        if (empty($ids)) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.no_items_selected'));
        }

        try {
            $count = $this->serviceProviderService->bulkRestore($ids);

            return redirect()
                ->route('admin.providers.index')
                ->with('success', __('admin/providers.messages.bulk_restored_successfully', ['count' => $count]));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.bulk_restore_failed'));
        }
    }

    /**
     * Get areas by city ID for AJAX requests.
     */
    public function getAreasByCity(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'city_id' => 'required|integer|exists:cities,id'
        ]);

        try {
            $areas = $this->areaService->getAreasByCity($request->input('city_id'));

            $areasData = $areas->map(function ($area) {
                return [
                    'id' => $area->id,
                    'name' => $area->name
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $areasData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch areas'
            ], 500);
        }
    }

    /**
     * Bulk update status of service providers.
     */
    public function bulkUpdateStatus(Request $request): RedirectResponse
    {
        $ids = $request->input('ids', []);

        // If ids is a JSON string, decode it to array
        if (is_string($ids)) {
            $ids = json_decode($ids, true) ?? [];
        }

        $status = $request->boolean('status');

        if (empty($ids)) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.no_items_selected'));
        }

        try {
            $count = $this->serviceProviderService->bulkUpdateStatus($ids, $status);

            $message = $status
                ? __('admin/providers.messages.bulk_activated_successfully', ['count' => $count])
                : __('admin/providers.messages.bulk_deactivated_successfully', ['count' => $count]);

            return redirect()
                ->route('admin.providers.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', __('admin/providers.messages.bulk_status_update_failed'));
        }
    }
}
