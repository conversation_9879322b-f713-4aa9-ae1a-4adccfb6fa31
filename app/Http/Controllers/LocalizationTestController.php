<?php

namespace App\Http\Controllers;

use App\Services\LanguageService;
use Illuminate\Http\Request;
use <PERSON><PERSON>ara\LaravelLocalization\Facades\LaravelLocalization;

class LocalizationTestController extends Controller
{
    protected $languageService;

    public function __construct(LanguageService $languageService)
    {
        $this->languageService = $languageService;
    }

    /**
     * Display localization test page
     */
    public function index()
    {
        $data = [
            'current_language' => $this->languageService->getCurrentLanguage(),
            'current_language_name' => $this->languageService->getCurrentLanguageName(),
            'current_language_native' => $this->languageService->getCurrentLanguageNativeName(),
            'current_direction' => $this->languageService->getCurrentDirection(),
            'is_rtl' => $this->languageService->isRtl(),
            'is_ltr' => $this->languageService->isLtr(),
            'supported_languages' => $this->languageService->getSupportedLanguages(),
            'language_switching_urls' => $this->languageService->getLanguageSwitchingUrls(),
            'default_language' => $this->languageService->getDefaultLanguage(),
            'direction_class' => $this->languageService->getDirectionClass(),
            'html_direction' => $this->languageService->getHtmlDirection(),
            'language_font' => $this->languageService->getLanguageFont(),
            'text_alignment' => $this->languageService->getTextAlignment(),
            'opposite_text_alignment' => $this->languageService->getOppositeTextAlignment(),
        ];

        return view('localization-test', $data);
    }

    /**
     * API endpoint to get current language information
     */
    public function apiInfo()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'current_language' => $this->languageService->getCurrentLanguage(),
                'current_language_name' => $this->languageService->getCurrentLanguageName(),
                'current_language_native' => $this->languageService->getCurrentLanguageNativeName(),
                'current_direction' => $this->languageService->getCurrentDirection(),
                'is_rtl' => $this->languageService->isRtl(),
                'is_ltr' => $this->languageService->isLtr(),
                'supported_languages' => $this->languageService->getSupportedLanguages(),
                'language_switching_urls' => $this->languageService->getLanguageSwitchingUrls(),
                'default_language' => $this->languageService->getDefaultLanguage(),
                'laravel_localization_info' => [
                    'current_locale' => LaravelLocalization::getCurrentLocale(),
                    'default_locale' => LaravelLocalization::getDefaultLocale(),
                    'supported_locales' => LaravelLocalization::getSupportedLocales(),
                    'localized_url_ar' => LaravelLocalization::getLocalizedURL('ar'),
                    'localized_url_en' => LaravelLocalization::getLocalizedURL('en'),
                ]
            ]
        ]);
    }

    /**
     * Test language switching functionality
     */
    public function switchLanguage(Request $request, $locale)
    {
        if (!$this->languageService->isLocaleSupported($locale)) {
            return response()->json([
                'success' => false,
                'message' => "Language '{$locale}' is not supported."
            ], 400);
        }

        $url = $this->languageService->getLanguageUrl($locale, $request->get('redirect_to'));
        
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => "Language switched to {$locale}",
                'redirect_url' => $url,
                'language_info' => [
                    'code' => $locale,
                    'name' => $this->languageService->getSupportedLanguages()[$locale]['name'],
                    'native' => $this->languageService->getSupportedLanguages()[$locale]['native'],
                    'direction' => $this->languageService->getSupportedLanguages()[$locale]['script'] === 'Arab' ? 'rtl' : 'ltr',
                ]
            ]);
        }

        return redirect($url);
    }
}
