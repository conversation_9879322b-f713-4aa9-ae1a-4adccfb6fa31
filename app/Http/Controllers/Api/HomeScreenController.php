<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\HomeScreen\HomeScreenResource;
use App\Services\HomeScreenService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Home Screen
 *
 * APIs for mobile app home screen data
 */
class HomeScreenController extends Controller
{
    public function __construct(
        private HomeScreenService $homeScreenService
    ) {
    }

    /**
     * Get Home Screen Data
     *
     * Get all data needed for the mobile app home screen including:
     * - Main slider slides
     * - 6 highest rating service providers (Id, Name, Image, Rate)
     * - 6 near service providers (Id, Name, Image, Rate) - based on location if provided
     * - Service categories
     * - Upcoming (always null for now)
     *
     * @authenticated
     *
     * @queryParam latitude number optional User's latitude for location-based near providers. Example: 24.7136
     * @queryParam longitude number optional User's longitude for location-based near providers. Example: 46.6753
     * @queryParam query string optional Search query to filter service providers and categories. Example: beauty
     *
     * @response 200 {
     *   "data": {
     *     "sliders": [
     *       {
     *         "id": 1,
     *         "image": "https://example.com/slider1.jpg",
     *         "title": "Welcome to Our Services",
     *         "description": "Find the best service providers near you",
     *         "link_url": "https://example.com/services"
     *       }
     *     ],
     *     "highest_rating_providers": [
     *       {
     *         "id": 1,
     *         "name": "Premium Beauty Salon",
     *         "logo": "https://example.com/logo1.jpg",
     *         "rating": 4.95
     *       }
     *     ],
     *     "near_providers": [
     *       {
     *         "id": 2,
     *         "name": "Quick Home Services",
     *         "logo": "https://example.com/logo2.jpg",
     *         "rating": 4.80
     *       }
     *     ],
     *     "service_categories": [
     *       {
     *         "id": 1,
     *         "name": "Beauty & Wellness",
     *         "description": "Professional beauty and wellness services",
     *         "icon": "https://example.com/icon1.png"
     *       }
     *     ],
     *     "upcoming": null
     *   }
     * }
     *
     * @response 500 {
     *   "message": "Failed to load home screen data",
     *   "error": "Error details here"
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $latitude = $request->query('latitude');
            $longitude = $request->query('longitude');
            $query = $request->query('query');

            // Validate coordinates if provided
            if (($latitude && !$longitude) || (!$latitude && $longitude)) {
                return response()->json([
                    'message' => 'Both latitude and longitude must be provided together'
                ], 422);
            }

            if ($latitude && ($latitude < -90 || $latitude > 90)) {
                return response()->json([
                    'message' => 'Latitude must be between -90 and 90'
                ], 422);
            }

            if ($longitude && ($longitude < -180 || $longitude > 180)) {
                return response()->json([
                    'message' => 'Longitude must be between -180 and 180'
                ], 422);
            }

            $homeScreenData = $this->homeScreenService->getHomeScreenData(
                $latitude ? (float) $latitude : null,
                $longitude ? (float) $longitude : null,
                $query
            );

            return response()->json([
                'data' => new HomeScreenResource($homeScreenData)
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load home screen data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
