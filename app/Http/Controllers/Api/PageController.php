<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Pages
 *
 * APIs for managing pages
 */
class PageController extends Controller
{
    public function __construct(
        private PageService $pageService
    ) {
    }

    /**
     * Get All Pages
     *
     * Get a list of all pages with their title and details.
     *
     * @authenticated
     *
     * @response 200 {
     *   "data": [
     *     {
     *       "id": 1,
     *       "title": "Privacy Policy",
     *       "details": "This is our privacy policy content...",
     *       "created_at": "2024-01-01T00:00:00.000000Z",
     *       "updated_at": "2024-01-01T00:00:00.000000Z"
     *     },
     *     {
     *       "id": 2,
     *       "title": "Terms of Service",
     *       "details": "These are our terms of service...",
     *       "created_at": "2024-01-01T00:00:00.000000Z",
     *       "updated_at": "2024-01-01T00:00:00.000000Z"
     *     }
     *   ]
     * }
     *
     * @response 500 {
     *   "message": "Failed to load pages",
     *   "error": "Error message"
     * }
     */
    public function index(): JsonResponse
    {
        try {
            $pages = $this->pageService->getAllPages();

            return response()->json([
                'data' => $pages->map(function ($page) {
                    return [
                        'id' => $page->id,
                        'title' => $page->title,
                        'details' => $page->details,
                        'created_at' => $page->created_at,
                        'updated_at' => $page->updated_at,
                    ];
                })
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load pages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Page Details
     *
     * Get details of a specific page by ID.
     *
     * @authenticated
     *
     * @urlParam id integer required The ID of the page. Example: 1
     *
     * @response 200 {
     *   "data": {
     *     "id": 1,
     *     "title": "Privacy Policy",
     *     "details": "This is our privacy policy content...",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     *
     * @response 404 {
     *   "message": "Page not found"
     * }
     *
     * @response 500 {
     *   "message": "Failed to load page details",
     *   "error": "Error message"
     * }
     */
    public function show(int $id): JsonResponse
    {
        try {
            $page = $this->pageService->getPageById($id);

            if (!$page) {
                return response()->json([
                    'message' => 'Page not found'
                ], 404);
            }

            return response()->json([
                'data' => [
                    'id' => $page->id,
                    'title' => $page->title,
                    'details' => $page->details,
                    'created_at' => $page->created_at,
                    'updated_at' => $page->updated_at,
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load page details',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
