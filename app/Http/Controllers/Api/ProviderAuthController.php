<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ServiceProvider;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

/**
 * @group Provider Authentication
 *
 * APIs for provider authentication and token management
 */
class ProviderAuthController extends Controller
{
    /**
     * Provider Login
     *
     * Authenticate a service provider and return an access token for API usage.
     * The provider must be active to successfully authenticate.
     *
     * @bodyParam email string required The provider's email address. Example: <EMAIL>
     * @bodyParam password string required The provider's password. Example: password123
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Login successful",
     *   "data": {
     *     "provider": {
     *       "id": 1,
     *       "name": "Beauty Salon Downtown",
     *       "email": "<EMAIL>",
     *       "logo": "https://example.com/logo.jpg",
     *       "rating": 4.5,
     *       "is_active": true
     *     },
     *     "token": "1|abc123def456ghi789jkl012mno345pqr678stu901vwx234yz"
     *   }
     * }
     *
     * @response 401 {
     *   "success": false,
     *   "message": "Invalid credentials"
     * }
     *
     * @response 403 {
     *   "success": false,
     *   "message": "Your account is inactive. Please contact support."
     * }
     *
     * @response 422 {
     *   "success": false,
     *   "message": "Validation failed",
     *   "errors": {
     *     "email": ["The email field is required."],
     *     "password": ["The password field is required."]
     *   }
     * }
     */
    public function login(Request $request): JsonResponse
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the provider by email
        $provider = ServiceProvider::where('email', $request->email)->first();

        // Check if provider exists and password is correct
        if (!$provider || !Hash::check($request->password, $provider->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Check if provider is active
        if (!$provider->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Your account is inactive. Please contact support.'
            ], 403);
        }

        // Create token for the provider
        $token = $provider->createToken('provider-api-token')->plainTextToken;

        // Return success response with provider data and token
        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'provider' => [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'email' => $provider->email,
                    'logo' => $provider->logo ? asset('storage/' . $provider->logo) : null,
                    'rating' => $provider->rating,
                    'is_active' => $provider->is_active,
                ],
                'token' => $token
            ]
        ]);
    }

    /**
     * Provider Logout
     *
     * Revoke the current access token and log out the provider.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Logout successful"
     * }
     *
     * @response 401 {
     *   "success": false,
     *   "message": "Unauthenticated"
     * }
     */
    public function logout(Request $request): JsonResponse
    {
        // Get the authenticated provider
        $provider = $request->user();

        if (!$provider) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        // Revoke the current token
        $provider->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Get Provider Profile
     *
     * Get the authenticated provider's profile information.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Profile retrieved successfully",
     *   "data": {
     *     "id": 1,
     *     "name": "Beauty Salon Downtown",
     *     "email": "<EMAIL>",
     *     "logo": "https://example.com/logo.jpg",
     *     "rating": 4.5,
     *     "city": "Riyadh",
     *     "area": "Downtown",
     *     "latitude": 24.7136,
     *     "longitude": 46.6753,
     *     "is_active": true
     *   }
     * }
     *
     * @response 401 {
     *   "success": false,
     *   "message": "Unauthenticated"
     * }
     */
    public function profile(Request $request): JsonResponse
    {
        $provider = $request->user();

        if (!$provider) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        // Load relationships
        $provider->load(['city', 'area']);

        return response()->json([
            'success' => true,
            'message' => 'Profile retrieved successfully',
            'data' => [
                'id' => $provider->id,
                'name' => $provider->name,
                'email' => $provider->email,
                'logo' => $provider->logo ? asset('storage/' . $provider->logo) : null,
                'rating' => $provider->rating,
                'city' => $provider->city?->name,
                'area' => $provider->area?->name,
                'latitude' => $provider->latitude,
                'longitude' => $provider->longitude,
                'is_active' => $provider->is_active,
            ]
        ]);
    }
}
