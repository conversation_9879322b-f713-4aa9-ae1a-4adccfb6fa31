<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\AddToFavouritesRequest;
use App\Http\Resources\FavouriteServiceResource;
use App\Services\CustomerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Favourite Services
 *
 * APIs for managing customer favourite services
 */
class FavouriteServiceController extends Controller
{
    public function __construct(
        private CustomerService $customerService
    ) {
    }

    /**
     * Get Customer's Favourite Services
     *
     * Retrieve the list of services that the authenticated customer has added to their favourites.
     * Returns detailed information about each service including provider details, category, images, and ratings.
     *
     * @authenticated
     *
     * @response 200 {
     *   "data": [
     *     {
     *       "id": 1,
     *       "title": "Hair Cut & Styling",
     *       "description": "Professional hair cutting and styling service",
     *       "price": "150.00",
     *       "rating": "4.50",
     *       "duration": 60,
     *       "formatted_duration": "1 hour",
     *       "image": "https://example.com/service-image.jpg",
     *       "service_provider": {
     *         "id": 1,
     *         "name": "Beauty Salon Pro",
     *         "image": "https://example.com/provider-image.jpg",
     *         "rating": "4.80",
     *         "city": "Riyadh",
     *         "area": "Al Olaya",
     *         "full_location": "Al Olaya, Riyadh"
     *       },
     *       "service_category": {
     *         "id": 1,
     *         "name": "Beauty & Hair",
     *         "image": "https://example.com/category-image.jpg"
     *       },
     *       "images": [
     *         {
     *           "id": 1,
     *           "image": "https://example.com/service-gallery-1.jpg",
     *           "alt_text": "Before image of the service"
     *         }
     *       ],
     *       "is_active": true,
     *       "created_at": "2024-01-01T00:00:00.000000Z",
     *       "updated_at": "2024-01-01T00:00:00.000000Z"
     *     }
     *   ]
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 500 {
     *   "message": "Failed to load favourite services",
     *   "error": "Error details here"
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $customer = $request->user();
            $favouriteServices = $this->customerService->getFavouriteServices($customer->id);

            return response()->json([
                'data' => FavouriteServiceResource::collection($favouriteServices)
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load favourite services',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add Service to Favourites
     *
     * Add a service to the authenticated customer's favourites list.
     * If the service is already in favourites, this operation will be ignored (no duplicate entries).
     *
     * @authenticated
     *
     * @bodyParam provider_service_id integer required The ID of the service to add to favourites. Example: 1
     *
     * @response 200 {
     *   "message": "Service added to favourites successfully"
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "provider_service_id": ["Service ID is required.", "The selected service does not exist or is not available."]
     *   }
     * }
     *
     * @response 500 {
     *   "message": "Failed to add service to favourites",
     *   "error": "Error details here"
     * }
     */
    public function store(AddToFavouritesRequest $request): JsonResponse
    {
        try {
            $customer = $request->user();
            $validatedData = $request->validated();

            $success = $this->customerService->addToFavourites(
                $customer->id,
                $validatedData['provider_service_id']
            );

            if ($success) {
                return response()->json([
                    'message' => 'Service added to favourites successfully'
                ], 200);
            } else {
                return response()->json([
                    'message' => 'Failed to add service to favourites'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to add service to favourites',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove Service from Favourites
     *
     * Remove a service from the authenticated customer's favourites list.
     *
     * @authenticated
     *
     * @urlParam id integer required The ID of the service to remove from favourites. Example: 1
     *
     * @response 200 {
     *   "message": "Service removed from favourites successfully"
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 404 {
     *   "message": "Service not found in favourites"
     * }
     *
     * @response 500 {
     *   "message": "Failed to remove service from favourites",
     *   "error": "Error details here"
     * }
     */
    public function destroy(Request $request, int $id): JsonResponse
    {
        try {
            $customer = $request->user();

            $success = $this->customerService->removeFromFavourites($customer->id, $id);

            if ($success) {
                return response()->json([
                    'message' => 'Service removed from favourites successfully'
                ], 200);
            } else {
                return response()->json([
                    'message' => 'Service not found in favourites'
                ], 404);
            }

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to remove service from favourites',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
