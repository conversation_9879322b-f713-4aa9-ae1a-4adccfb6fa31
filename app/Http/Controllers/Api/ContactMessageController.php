<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContactMessageRequest;
use App\Services\ContactMessageService;
use Illuminate\Http\JsonResponse;

/**
 * @group Contact Messages
 *
 * APIs for contact message management
 */
class ContactMessageController extends Controller
{
    public function __construct(
        private ContactMessageService $contactMessageService
    ) {
    }

    /**
     * Create Contact Message
     *
     * Submit a new contact message from an authenticated user.
     *
     * @authenticated
     *
     * @bodyParam name string required The sender's full name. Example: <PERSON>
     * @bodyParam email string required The sender's email address. Example: <EMAIL>
     * @bodyParam message string required The contact message content. Example: Hello, I would like to inquire about your services.
     *
     * @response 201 {
     *   "message": "Contact message sent successfully",
     *   "contact_message": {
     *     "id": 1,
     *     "name": "<PERSON>",
     *     "email": "<EMAIL>",
     *     "message": "Hello, I would like to inquire about your services.",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "name": ["Name is required."],
     *     "email": ["Email address is required.", "Please provide a valid email address."],
     *     "message": ["Message is required."]
     *   }
     * }
     *
     * @response 500 {
     *   "message": "Failed to send contact message",
     *   "error": "Error details here"
     * }
     */
    public function store(ContactMessageRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();

            $contactMessage = $this->contactMessageService->create($validatedData);

            return response()->json([
                'message' => 'Contact message sent successfully',
                'contact_message' => $contactMessage
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send contact message',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
