<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ServiceProviderListRequest;
use App\Http\Resources\ServiceProviderDetailsResource;
use App\Services\ServiceProviderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Service Providers
 *
 * APIs for managing and browsing service providers
 */
class ServiceProviderController extends Controller
{
    public function __construct(
        private ServiceProviderService $serviceProviderService
    ) {
    }

    /**
     * List Active Service Providers
     *
     * Get a list of all active service providers with optional search functionality.
     * Returns provider ID, name, image (logo), and rating.
     *
     * @authenticated
     *
     * @queryParam search string optional Search query to filter providers by name. Example: 
     *
     * @response 200 {
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "Beauty Salon Downtown",
     *       "image": "https://example.com/logo1.jpg",
     *       "rating": 4.5
     *     },
     *     {
     *       "id": 2,
     *       "name": "Tech Repair Center",
     *       "image": "https://example.com/logo2.jpg",
     *       "rating": 4.8
     *     }
     *   ],
     *   "message": "Service providers retrieved successfully"
     * }
     *
     * @response 200 scenario="No providers found" {
     *   "data": [],
     *   "message": "No service providers found"
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $searchQuery = $request->input('search');

        // Get active service providers with optional search
        if ($searchQuery) {
            $providers = $this->serviceProviderService->searchActiveProviders($searchQuery);
        } else {
            $providers = $this->serviceProviderService->getActiveProviders();
        }

        // Transform the data to return only required fields
        $transformedProviders = $providers->map(function ($provider) {
            return [
                'id' => $provider->id,
                'name' => $provider->name,
                'image' => $provider->logo,
                'rating' => $provider->rating,
            ];
        });

        $message = $transformedProviders->isEmpty()
            ? 'No service providers found'
            : 'Service providers retrieved successfully';

        return response()->json([
            'data' => $transformedProviders,
            'message' => $message
        ]);
    }

    /**
     * Get Service Provider Details
     *
     * Retrieve comprehensive details for a specific service provider including
     * location, description, Google Maps link, rating with review count,
     * photo gallery, working hours status, and list of services.
     *
     * @authenticated
     *
     * @urlParam id integer required The ID of the service provider. Example: 1
     *
     * @response 200 {
     *   "data": {
     *     "id": 1,
     *     "name": "Beauty Salon Downtown",
     *     "logo": "https://example.com/logo.jpg",
     *     "city": "Riyadh",
     *     "area": "Downtown",
     *     "location": "Downtown, Riyadh",
     *     "description": "Professional beauty services with experienced staff",
     *     "google_maps_url": "https://www.google.com/maps?q=24.7136,46.6753",
     *     "rating": 4.5,
     *     "review_count": 25,
     *     "gallery": [
     *       {
     *         "id": 1,
     *         "image": "https://example.com/gallery1.jpg",
     *         "alt_text": "Interior view",
     *         "sort_order": 1
     *       }
     *     ],
     *     "working_hours_status": "Open Now",
     *     "working_hours": [
     *       {
     *         "day": "Monday",
     *         "open": "09:00",
     *         "close": "18:00",
     *         "is_off_day": false,
     *         "formatted": "09:00 - 18:00"
     *       }
     *     ],
     *     "services": [
     *       {
     *         "id": 1,
     *         "image": "https://example.com/service1.jpg",
     *         "title": "Hair Cut",
     *         "price": 50.00,
     *         "rating": 4.8
     *       }
     *     ]
     *   },
     *   "message": "Service provider details retrieved successfully"
     * }
     *
     * @response 404 {
     *   "message": "Service provider not found"
     * }
     */
    public function show(int $id): JsonResponse
    {
        $provider = $this->serviceProviderService->getProviderWithFullDetails($id);

        if (!$provider) {
            return response()->json([
                'message' => 'Service provider not found'
            ], 404);
        }

        return response()->json([
            'data' => new ServiceProviderDetailsResource($provider),
            'message' => 'Service provider details retrieved successfully'
        ]);
    }
}
