<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RegisterRequest;
use App\Http\Resources\CustomerResource;
use App\Services\CustomerAuthService;
use Illuminate\Http\JsonResponse;

/**
 * @group Customer Authentication
 *
 * APIs for customer authentication and token management
 */
class CustomerAuthController extends Controller
{
    public function __construct(
        private CustomerAuthService $customerAuthService
    ) {
    }

    /**
     * Customer Login
     *
     * Authenticate a customer using email and password credentials.
     * Returns a Sanctum authentication token on successful login.
     *
     * @bodyParam email string required The customer's email address. Example: <EMAIL>
     * @bodyParam password string required The customer's password. Example: password123
     *
     * @response 200 {
     *   "message": "Login successful",
     *   "token": "1|abcdefghijklmnopqrstuvwxyz**********",
     *   "customer": {
     *     "id": 1,
     *     "gender": "male",
     *     "city": "New York",
     *     "area": "Manhattan",
     *     "full_location": "Manhattan, New York",
     *     "longitude": -73.9857,
     *     "latitude": 40.7484,
     *     "email": "<EMAIL>",
     *     "email_verified_at": null,
     *     "mobile": "+**********",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     *
     * @response 401 {
     *   "message": "Invalid credentials"
     * }
     *
     * @response 403 {
     *   "message": "Your account is inactive. Please contact support."
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "email": ["The email field is required."],
     *     "password": ["The password field is required."]
     *   }
     * }
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->validated();

        $result = $this->customerAuthService->login(
            $credentials['email'],
            $credentials['password']
        );

        if ($result === false) {
            return response()->json([
                'message' => 'Invalid credentials'
            ], 401);
        }

        if ($result === 'inactive') {
            return response()->json([
                'message' => 'Your account is inactive. Please contact support.'
            ], 403);
        }

        return response()->json([
            'message' => 'Login successful',
            'token' => $result['token'],
            'customer' => new CustomerResource($result['customer'])
        ], 200);
    }

    /**
     * Customer Logout
     *
     * Revoke the current authentication token and log out the customer.
     * This will invalidate the current token and require re-authentication for future requests.
     *
     * @authenticated
     *
     * @response 200 {
     *   "message": "Logout successful"
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function logout(): JsonResponse
    {
        /** @var \App\Models\Customer $customer */
        $customer = auth('sanctum')->user();

        $this->customerAuthService->logout($customer);

        return response()->json([
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Customer Registration
     *
     * Register a new customer with name, email, mobile number, and password.
     * Returns customer data and a Sanctum authentication token on successful registration.
     *
     * @bodyParam name string required The customer's full name. Example: John Doe
     * @bodyParam email string required The customer's email address. Example: <EMAIL>
     * @bodyParam mobile string required Saudi mobile number (starts with 05, 10 digits total). Example: 0512345678
     * @bodyParam password string required The customer's password (minimum 8 characters, must include uppercase, lowercase, and number). Example: Password123
     * @bodyParam password_confirmation string required Password confirmation. Example: Password123
     * @bodyParam approve_policy boolean required Must be true to accept policy terms. Example: true
     *
     * @response 201 {
     *   "message": "Registration successful",
     *   "token": "1|abcdefghijklmnopqrstuvwxyz**********",
     *   "customer": {
     *     "id": 1,
     *     "name": "John Doe",
     *     "gender": null,
     *     "city": null,
     *     "area": null,
     *     "full_location": "",
     *     "longitude": null,
     *     "latitude": null,
     *     "email": "<EMAIL>",
     *     "email_verified_at": null,
     *     "mobile": "0512345678",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "name": ["Name is required."],
     *     "email": ["Email address is required.", "This email address is already registered."],
     *     "mobile": ["Saudi mobile number is required.", "Mobile number must be a valid Saudi number (starts with 05 and is 10 digits total).", "This mobile number is already registered."],
     *     "password": ["Password is required.", "Password must be at least 8 characters.", "Password must contain at least one uppercase letter, one lowercase letter, and one number.", "Password confirmation does not match."],
     *     "password_confirmation": ["Password confirmation is required."],
     *     "approve_policy": ["You must approve the policy to register."]
     *   }
     * }
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        $validatedData = $request->validated();

        $result = $this->customerAuthService->register($validatedData);

        return response()->json([
            'message' => 'Registration successful',
            'token' => $result['token'],
            'customer' => new CustomerResource($result['customer'])
        ], 201);
    }
}
