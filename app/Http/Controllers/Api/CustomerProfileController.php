<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateProfileImageRequest;
use App\Http\Requests\UpdateProfileRequest;
use App\Http\Resources\CustomerResource;
use App\Services\CustomerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Customer Profile
 *
 * APIs for customer profile management
 */
class CustomerProfileController extends Controller
{
    public function __construct(
        private CustomerService $customerService
    ) {
    }

    /**
     * Get Customer Profile
     *
     * Get the authenticated customer's profile information.
     *
     * @authenticated
     *
     * @response 200 {
     *   "customer": {
     *     "id": 1,
     *     "name": "John Doe",
     *     "gender": "Male",
     *     "city": "Riyadh",
     *     "area": "Al Olaya",
     *     "full_location": "Al Olaya, Riyadh",
     *     "longitude": 46.6753,
     *     "latitude": 24.7136,
     *     "email": "<EMAIL>",
     *     "email_verified_at": null,
     *     "mobile": "0512345678",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function show(Request $request): JsonResponse
    {
        $customer = $request->user();

        return response()->json([
            'customer' => new CustomerResource($customer->load(['city', 'area']))
        ], 200);
    }

    /**
     * Update Customer Profile
     *
     * Update the authenticated customer's profile information.
     * You can update one or more fields: name, email, mobile, password, city_id, area_id, gender.
     *
     * @authenticated
     *
     * @bodyParam name string The customer's full name. Example: John Doe
     * @bodyParam email string The customer's email address. Example: <EMAIL>
     * @bodyParam mobile string Saudi mobile number (starts with +966 or 05). Example: +966501234567
     * @bodyParam password string The customer's new password (minimum 8 characters, must include uppercase, lowercase, and number). Example: NewPassword123
     * @bodyParam city_id integer The ID of the city. Example: 1
     * @bodyParam area_id integer The ID of the area. Example: 1
     * @bodyParam gender string The customer's gender. Example: Male
     *
     * @response 200 {
     *   "message": "Profile updated successfully",
     *   "customer": {
     *     "id": 1,
     *     "name": "John Doe",
     *     "gender": "Male",
     *     "city": "Riyadh",
     *     "area": "Al Olaya",
     *     "full_location": "Al Olaya, Riyadh",
     *     "longitude": 46.6753,
     *     "latitude": 24.7136,
     *     "email": "<EMAIL>",
     *     "email_verified_at": null,
     *     "mobile": "+966501234567",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "email": ["This email address is already registered."],
     *     "mobile": ["Mobile number must be a valid Saudi number (starts with +966 or 05 and is 10 digits total)."],
     *     "password": ["Password must contain at least one uppercase letter, one lowercase letter, and one number."],
     *     "city_id": ["The selected city does not exist."],
     *     "area_id": ["The selected area does not exist."],
     *     "gender": ["Gender must be one of: Male, Female, Other."]
     *   }
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function update(UpdateProfileRequest $request): JsonResponse
    {
        $customer = $request->user();
        $validatedData = $request->validated();

        try {
            $updatedCustomer = $this->customerService->updateProfile(
                $customer->id,
                $validatedData
            );

            return response()->json([
                'message' => 'Profile updated successfully',
                'customer' => new CustomerResource($updatedCustomer->load(['city', 'area']))
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update Customer Profile Image
     *
     * Update the authenticated customer's profile image.
     *
     * @authenticated
     *
     * @bodyParam image file required The profile image file (JPEG or PNG, max 2MB).
     *
     * @response 200 {
     *   "message": "Profile image updated successfully",
     *   "imageUrl": "https://example.com/storage/profile-images/abc123.jpg",
     *   "customer": {
     *     "id": 1,
     *     "name": "John Doe",
     *     "gender": "Male",
     *     "city": "Riyadh",
     *     "area": "Al Olaya",
     *     "full_location": "Al Olaya, Riyadh",
     *     "longitude": 46.6753,
     *     "latitude": 24.7136,
     *     "email": "<EMAIL>",
     *     "email_verified_at": null,
     *     "mobile": "0512345678",
     *     "profile_image": "profile-images/abc123.jpg",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "image": ["Profile image is required."],
     *     "image": ["Profile image must be a JPEG or PNG file."],
     *     "image": ["Profile image may not be larger than 2MB."]
     *   }
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function updateProfileImage(UpdateProfileImageRequest $request): JsonResponse
    {
        $customer = $request->user();

        try {
            $updatedCustomer = $this->customerService->updateProfileImage(
                $customer->id,
                $request->file('image')
            );

            $imageUrl = $updatedCustomer->profile_image
                ? asset('storage/' . $updatedCustomer->profile_image)
                : null;

            return response()->json([
                'message' => 'Profile image updated successfully',
                'imageUrl' => $imageUrl,
                'customer' => new CustomerResource($updatedCustomer->load(['city', 'area']))
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update profile image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete Customer Profile
     *
     * Delete the authenticated customer's profile. This will soft delete the customer account,
     * remove the profile image, and revoke all authentication tokens.
     *
     * @authenticated
     *
     * @response 200 {
     *   "message": "Profile deleted successfully"
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 500 {
     *   "message": "Failed to delete profile",
     *   "error": "Error details here"
     * }
     */
    public function destroy(Request $request): JsonResponse
    {
        try {
            $customer = $request->user();

            $this->customerService->deleteProfile($customer->id);

            return response()->json([
                'message' => 'Profile deleted successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
