<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\NotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Notifications
 *
 * APIs for managing customer notifications
 */
class NotificationController extends Controller
{
    public function __construct(
        private NotificationService $notificationService
    ) {
    }

    /**
     * Get Notifications List
     *
     * Retrieve all notifications for the authenticated customer.
     * Returns notifications with id, title, description, type, and is_read status.
     *
     * @authenticated
     *
     * @response 200 {
     *   "message": "Notifications retrieved successfully",
     *   "data": [
     *     {
     *       "id": 1,
     *       "title": "Welcome to our service",
     *       "description": "Thank you for joining our platform. We're excited to have you!",
     *       "type": "general",
     *       "is_read": false,
     *       "created_at": "2024-01-01T00:00:00.000000Z",
     *       "updated_at": "2024-01-01T00:00:00.000000Z"
     *     },
     *     {
     *       "id": 2,
     *       "title": "New service available",
     *       "description": "Check out our new cleaning service in your area.",
     *       "type": "promotion",
     *       "is_read": true,
     *       "created_at": "2024-01-02T00:00:00.000000Z",
     *       "updated_at": "2024-01-02T00:00:00.000000Z"
     *     }
     *   ],
     *   "unread_count": 1
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $customer = $request->user();
        
        $notifications = $this->notificationService->getNotificationsForCustomer($customer->id);
        $unreadCount = $this->notificationService->countUnreadForCustomer($customer->id);

        return response()->json([
            'message' => 'Notifications retrieved successfully',
            'data' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'description' => $notification->description,
                    'type' => $notification->type,
                    'is_read' => $notification->is_read,
                    'created_at' => $notification->created_at,
                    'updated_at' => $notification->updated_at,
                ];
            }),
            'unread_count' => $unreadCount,
        ]);
    }

    /**
     * Mark All Notifications as Read
     *
     * Mark all notifications as read for the authenticated customer.
     *
     * @authenticated
     *
     * @response 200 {
     *   "message": "All notifications marked as read successfully"
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $customer = $request->user();
        
        $this->notificationService->markAllAsReadForCustomer($customer->id);

        return response()->json([
            'message' => 'All notifications marked as read successfully',
        ]);
    }

    /**
     * Clear All Notifications
     *
     * Clear (soft delete) all notifications for the authenticated customer.
     *
     * @authenticated
     *
     * @response 200 {
     *   "message": "All notifications cleared successfully"
     * }
     *
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function clearAll(Request $request): JsonResponse
    {
        $customer = $request->user();
        
        $this->notificationService->clearAllNotificationsForCustomer($customer->id);

        return response()->json([
            'message' => 'All notifications cleared successfully',
        ]);
    }
}
