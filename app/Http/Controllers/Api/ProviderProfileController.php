<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ProviderBasicInfoUpdateRequest;
use App\Services\Api\ProviderProfileApiService;
use App\Services\CityService;
use App\Services\AreaService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;

/**
 * @group Provider Profile
 *
 * APIs for provider profile management and updates
 */
class ProviderProfileController extends Controller
{
    public function __construct(
        private ProviderProfileApiService $providerProfileService,
        private CityService $cityService,
        private AreaService $areaService
    ) {
    }

    /**
     * Get Provider Profile
     *
     * Get the authenticated provider's complete profile information including
     * translations, location data, and profile statistics.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Profile retrieved successfully",
     *   "data": {
     *     "profile": {
     *       "id": 1,
     *       "name": {
     *         "ar": "شركة الخدمات المتميزة",
     *         "en": "Premium Services Company"
     *       },
     *       "description": {
     *         "ar": "نقدم أفضل الخدمات المنزلية",
     *         "en": "We provide the best home services"
     *       },
     *       "email": "<EMAIL>",
     *       "logo": "https://example.com/logo.jpg",
     *       "rating": 4.5,
     *       "city": {
     *         "id": 1,
     *         "name": "Riyadh"
     *       },
     *       "area": {
     *         "id": 1,
     *         "name": "Downtown"
     *       },
     *       "latitude": 24.7136,
     *       "longitude": 46.6753,
     *       "is_active": true
     *     },
     *     "statistics": {
     *       "total_services": 12,
     *       "active_services": 10,
     *       "total_reviews": 48,
     *       "average_rating": 4.5,
     *       "profile_completion": 85
     *     }
     *   }
     * }
     *
     * @response 401 {
     *   "success": false,
     *   "message": "Unauthenticated"
     * }
     */
    public function show(Request $request): JsonResponse
    {
        $provider = $request->user();

        if (!$provider) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        try {
            $profileData = $this->providerProfileService->getProviderProfileData($provider);
            $statistics = $this->providerProfileService->getProviderStatistics($provider);

            return response()->json([
                'success' => true,
                'message' => 'Profile retrieved successfully',
                'data' => [
                    'profile' => $profileData,
                    'statistics' => $statistics
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve profile data'
            ], 500);
        }
    }

    /**
     * Update Provider Basic Information
     *
     * Update the authenticated provider's basic information including name translations,
     * description translations, email, location, coordinates, and logo.
     * All fields are required except logo which is optional.
     *
     * @authenticated
     *
     * @bodyParam translations.ar.name string required Arabic name of the provider. Example: شركة الخدمات المتميزة
     * @bodyParam translations.en.name string required English name of the provider. Example: Premium Services Company
     * @bodyParam translations.ar.description string required Arabic description of the provider. Example: نقدم أفضل الخدمات المنزلية والتجارية
     * @bodyParam translations.en.description string required English description of the provider. Example: We provide the best home and commercial services
     * @bodyParam email string required Provider's email address. Example: <EMAIL>
     * @bodyParam city_id integer required ID of the city where provider is located. Example: 1
     * @bodyParam area_id integer required ID of the area where provider is located. Example: 1
     * @bodyParam latitude number required Provider's latitude coordinate. Example: 24.7136
     * @bodyParam longitude number required Provider's longitude coordinate. Example: 46.6753
     * @bodyParam logo file optional Provider's logo image (JPEG, PNG, JPG, max 2MB).
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Profile updated successfully",
     *   "data": {
     *     "id": 1,
     *     "name": {
     *       "ar": "شركة الخدمات المتميزة",
     *       "en": "Premium Services Company"
     *     },
     *     "description": {
     *       "ar": "نقدم أفضل الخدمات المنزلية والتجارية",
     *       "en": "We provide the best home and commercial services"
     *     },
     *     "email": "<EMAIL>",
     *     "logo": "https://example.com/new-logo.jpg",
     *     "rating": 4.5,
     *     "city": {
     *       "id": 1,
     *       "name": "Riyadh"
     *     },
     *     "area": {
     *       "id": 1,
     *       "name": "Downtown"
     *     },
     *     "latitude": 24.7136,
     *     "longitude": 46.6753,
     *     "is_active": true
     *   }
     * }
     *
     * @response 422 {
     *   "success": false,
     *   "message": "Validation failed",
     *   "errors": {
     *     "translations.ar.name": ["Arabic name is required."],
     *     "email": ["The email field is required."]
     *   }
     * }
     *
     * @response 400 {
     *   "success": false,
     *   "message": "Selected area does not belong to the selected city"
     * }
     *
     * @response 500 {
     *   "success": false,
     *   "message": "Failed to update profile"
     * }
     */
    public function updateBasicInfo(ProviderBasicInfoUpdateRequest $request): JsonResponse
    {
        $provider = $request->user();
        $validatedData = $request->validated();

        try {
            // Additional validation: Check if area belongs to city
            if (
                !$this->providerProfileService->validateAreaBelongsToCity(
                    $validatedData['city_id'],
                    $validatedData['area_id']
                )
            ) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected area does not belong to the selected city'
                ], 400);
            }

            // Update provider profile
            $updatedProvider = $this->providerProfileService->updateBasicInfo(
                $provider,
                $validatedData,
                $request->file('logo')
            );

            // Get updated profile data
            $profileData = $this->providerProfileService->getProviderProfileData($updatedProvider);

            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => $profileData
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update profile'
            ], 500);
        }
    }

    /**
     * Get Cities List
     *
     * Get all available cities for provider location selection.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Cities retrieved successfully",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "Riyadh"
     *     },
     *     {
     *       "id": 2,
     *       "name": "Jeddah"
     *     }
     *   ]
     * }
     */
    public function getCities(): JsonResponse
    {
        try {
            $cities = $this->cityService->getAllCities();

            $citiesData = $cities->map(function ($city) {
                return [
                    'id' => $city->id,
                    'name' => $city->name
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Cities retrieved successfully',
                'data' => $citiesData
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve cities'
            ], 500);
        }
    }

    /**
     * Get Areas by City
     *
     * Get all areas for a specific city.
     *
     * @authenticated
     *
     * @queryParam city_id integer required The ID of the city. Example: 1
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Areas retrieved successfully",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "Downtown"
     *     },
     *     {
     *       "id": 2,
     *       "name": "North District"
     *     }
     *   ]
     * }
     *
     * @response 422 {
     *   "success": false,
     *   "message": "City ID is required"
     * }
     */
    public function getAreasByCity(Request $request): JsonResponse
    {
        $cityId = $request->query('city_id');

        if (!$cityId) {
            return response()->json([
                'success' => false,
                'message' => 'City ID is required'
            ], 422);
        }

        try {
            $areas = $this->areaService->getAreasByCity($cityId);

            $areasData = $areas->map(function ($area) {
                return [
                    'id' => $area->id,
                    'name' => $area->name
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Areas retrieved successfully',
                'data' => $areasData
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve areas'
            ], 500);
        }
    }
}
