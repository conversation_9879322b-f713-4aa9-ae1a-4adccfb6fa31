<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

/**
 * Provider Setting Controller
 * 
 * Handles provider account settings and preferences.
 */
class SettingController extends Controller
{
    /**
     * Display settings overview.
     */
    public function index(): View
    {
        return view('provider.settings.index');
    }

    /**
     * Display account settings.
     */
    public function account(): View
    {
        $provider = Auth::guard('provider')->user();

        return view('provider.settings.account', compact('provider'));
    }

    /**
     * Update account settings.
     */
    public function updateAccount(Request $request): RedirectResponse
    {
        // Update account settings logic would go here
        return back()->with('success', __('provider/settings.account_updated'));
    }

    /**
     * Update privacy settings.
     */
    public function updatePrivacy(Request $request): RedirectResponse
    {
        $request->validate([
            'profile_visibility' => 'required|in:public,private',
            'show_phone' => 'nullable|boolean',
            'show_email' => 'nullable|boolean',
        ]);

        $provider = Auth::guard('provider')->user();

        // Update privacy settings logic would go here
        // For example:
        // $provider->update([
        //     'profile_visibility' => $request->profile_visibility,
        //     'show_phone' => $request->boolean('show_phone'),
        //     'show_email' => $request->boolean('show_email'),
        // ]);

        return back()->with('success', __('provider/settings.privacy_updated'));
    }

    /**
     * Update preferences settings.
     */
    public function updatePreferences(Request $request): RedirectResponse
    {
        $request->validate([
            'language' => 'required|in:ar,en',
            'timezone' => 'required|string',
            'currency' => 'required|string',
        ]);

        $provider = Auth::guard('provider')->user();

        // Update preferences settings logic would go here
        // For example:
        // $provider->update([
        //     'language' => $request->language,
        //     'timezone' => $request->timezone,
        //     'currency' => $request->currency,
        // ]);

        return back()->with('success', __('provider/settings.preferences_updated'));
    }

    /**
     * Display notification settings.
     */
    public function notifications(): View
    {
        $provider = Auth::guard('provider')->user();

        return view('provider.settings.notifications', compact('provider'));
    }

    /**
     * Update notification settings.
     */
    public function updateNotifications(Request $request): RedirectResponse
    {
        // Update notification settings logic would go here
        return back()->with('success', __('provider/settings.notifications_updated'));
    }

    /**
     * Display security settings.
     */
    public function security(): View
    {
        return view('provider.settings.security');
    }

    /**
     * Update password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        // Update password logic would go here
        return back()->with('success', __('provider/settings.password_updated'));
    }

    /**
     * Enable two-factor authentication.
     */
    public function enableTwoFactor(Request $request): RedirectResponse
    {
        // Enable 2FA logic would go here
        return back()->with('success', __('provider/settings.two_factor_enabled'));
    }

    /**
     * Disable two-factor authentication.
     */
    public function disableTwoFactor(Request $request): RedirectResponse
    {
        // Disable 2FA logic would go here
        return back()->with('success', __('provider/settings.two_factor_disabled'));
    }
}
