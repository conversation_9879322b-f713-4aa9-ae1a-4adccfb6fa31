<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

/**
 * Provider Dashboard Controller
 * 
 * Handles the main provider dashboard functionality including
 * statistics, recent activity, and overview data.
 */
class DashboardController extends Controller
{
    /**
     * Display the provider dashboard.
     */
    public function index(): View
    {
        $provider = Auth::guard('provider')->user();
        
        // Get dashboard statistics
        $statistics = $this->getDashboardStatistics($provider);
        
        // Get recent activity
        $recentActivity = $this->getRecentActivity($provider);
        
        // Get upcoming bookings
        $upcomingBookings = $this->getUpcomingBookings($provider);
        
        return view('provider.dashboard.index', compact(
            'statistics', 
            'recentActivity', 
            'upcomingBookings'
        ));
    }

    /**
     * Get dashboard statistics for API endpoint.
     */
    public function getStatistics(): JsonResponse
    {
        $provider = Auth::guard('provider')->user();
        $statistics = $this->getDashboardStatistics($provider);
        
        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Get recent bookings for API endpoint.
     */
    public function getRecentBookings(): JsonResponse
    {
        $provider = Auth::guard('provider')->user();
        $recentBookings = $this->getRecentActivity($provider);
        
        return response()->json([
            'success' => true,
            'data' => $recentBookings
        ]);
    }

    /**
     * Get earnings chart data for API endpoint.
     */
    public function getEarningsChart(Request $request): JsonResponse
    {
        $provider = Auth::guard('provider')->user();
        $period = $request->input('period', 'month'); // week, month, year
        
        $earningsData = $this->getEarningsData($provider, $period);
        
        return response()->json([
            'success' => true,
            'data' => $earningsData
        ]);
    }

    /**
     * Get service performance data for API endpoint.
     */
    public function getServicePerformance(): JsonResponse
    {
        $provider = Auth::guard('provider')->user();
        $servicePerformance = $this->getServicePerformanceData($provider);
        
        return response()->json([
            'success' => true,
            'data' => $servicePerformance
        ]);
    }

    /**
     * Get dashboard statistics for the provider.
     */
    private function getDashboardStatistics($provider): array
    {
        // In a real application, you would query your models
        // For now, we'll use sample data
        return [
            'total_services' => [
                'count' => 12,
                'growth' => 15,
                'trend' => 'up'
            ],
            'active_bookings' => [
                'count' => 8,
                'growth' => 25,
                'trend' => 'up'
            ],
            'completed_bookings' => [
                'count' => 156,
                'growth' => 12,
                'trend' => 'up'
            ],
            'total_earnings' => [
                'amount' => 15420.50,
                'growth' => 18,
                'trend' => 'up',
                'currency' => 'SAR'
            ],
            'average_rating' => [
                'rating' => 4.8,
                'total_reviews' => 89,
                'growth' => 5,
                'trend' => 'up'
            ],
            'response_time' => [
                'hours' => 2.5,
                'improvement' => 15,
                'trend' => 'down' // down is good for response time
            ]
        ];
    }

    /**
     * Get recent activity for the provider.
     */
    private function getRecentActivity($provider): array
    {
        // In a real application, you would query your booking/activity models
        return [
            [
                'id' => 1,
                'type' => 'booking',
                'title' => 'New booking received',
                'description' => 'Home cleaning service for Ahmed Ali',
                'time' => '2 hours ago',
                'status' => 'pending'
            ],
            [
                'id' => 2,
                'type' => 'review',
                'title' => 'New review received',
                'description' => '5-star review from Sara Mohammed',
                'time' => '4 hours ago',
                'status' => 'positive'
            ],
            [
                'id' => 3,
                'type' => 'booking',
                'title' => 'Booking completed',
                'description' => 'Plumbing service for Omar Hassan',
                'time' => '1 day ago',
                'status' => 'completed'
            ]
        ];
    }

    /**
     * Get upcoming bookings for the provider.
     */
    private function getUpcomingBookings($provider): array
    {
        // In a real application, you would query your booking models
        return [
            [
                'id' => 1,
                'service' => 'Home Cleaning',
                'customer' => 'Ahmed Ali',
                'date' => '2024-01-20',
                'time' => '10:00 AM',
                'status' => 'confirmed',
                'amount' => 150.00
            ],
            [
                'id' => 2,
                'service' => 'Plumbing Repair',
                'customer' => 'Sara Mohammed',
                'date' => '2024-01-21',
                'time' => '2:00 PM',
                'status' => 'pending',
                'amount' => 200.00
            ]
        ];
    }

    /**
     * Get earnings data for charts.
     */
    private function getEarningsData($provider, string $period): array
    {
        // Sample data for earnings chart
        $data = [
            'week' => [
                'labels' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                'earnings' => [120, 190, 300, 500, 200, 300, 450]
            ],
            'month' => [
                'labels' => ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                'earnings' => [1200, 1900, 3000, 2200]
            ],
            'year' => [
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                'earnings' => [5000, 6200, 7800, 8500, 9200, 10100, 11500, 12200, 10800, 9500, 8700, 9800]
            ]
        ];

        return $data[$period] ?? $data['month'];
    }

    /**
     * Get service performance data.
     */
    private function getServicePerformanceData($provider): array
    {
        // Sample data for service performance
        return [
            [
                'service' => 'Home Cleaning',
                'bookings' => 45,
                'rating' => 4.8,
                'earnings' => 6750.00
            ],
            [
                'service' => 'Plumbing Repair',
                'bookings' => 32,
                'rating' => 4.6,
                'earnings' => 4800.00
            ],
            [
                'service' => 'Electrical Work',
                'bookings' => 28,
                'rating' => 4.9,
                'earnings' => 4200.00
            ]
        ];
    }
}
