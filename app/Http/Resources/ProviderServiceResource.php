<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProviderServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'image' => $this->image,
            'main_image' => $this->main_image,
            'price' => $this->price,
            'rating' => $this->rating,
            'duration' => $this->duration,
            'formatted_duration' => $this->formatted_duration,
            'is_active' => $this->is_active,
            'service_provider' => new ServiceProviderResource($this->whenLoaded('serviceProvider')),
            'service_category' => new ServiceCategoryResource($this->whenLoaded('serviceCategory')),
            'images' => ProviderServiceImageResource::collection($this->whenLoaded('images')),
            'favourites_count' => $this->when(isset($this->favourites_count), $this->favourites_count),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
