<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceProviderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'logo' => $this->logo,
            'rating' => $this->rating,
            'city' => $this->city,
            'area' => $this->area,
            'full_location' => $this->full_location,
            'longitude' => $this->longitude,
            'latitude' => $this->latitude,
            'is_active' => $this->is_active,
            'average_price' => $this->when($this->relationLoaded('services'), $this->average_price),
            'services' => ProviderServiceResource::collection($this->whenLoaded('services')),
            'gallery' => ServiceProviderGalleryResource::collection($this->whenLoaded('gallery')),
            'working_hours' => WorkingHourResource::collection($this->whenLoaded('workingHours')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
