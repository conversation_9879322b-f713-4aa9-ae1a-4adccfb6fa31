<?php

namespace App\Http\Resources\HomeScreen;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HomeScreenResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'sliders' => SliderResource::collection($this->resource['sliders']),
            'highest_rating_providers' => ServiceProviderSummaryResource::collection($this->resource['highest_rating_providers']),
            'near_providers' => ServiceProviderSummaryResource::collection($this->resource['near_providers']),
            'service_categories' => ServiceCategorySummaryResource::collection($this->resource['service_categories']),
            'upcoming' => $this->resource['upcoming'],
        ];
    }
}
