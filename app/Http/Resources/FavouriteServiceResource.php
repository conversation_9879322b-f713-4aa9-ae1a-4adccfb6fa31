<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FavouriteServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'rating' => $this->rating,
            'duration' => $this->duration,
            'formatted_duration' => $this->formatted_duration,
            'image' => $this->main_image,
            'service_provider' => [
                'id' => $this->serviceProvider->id,
                'name' => $this->serviceProvider->name,
                'image' => $this->serviceProvider->image,
                'rating' => $this->serviceProvider->rating,
                'city' => $this->serviceProvider->city?->name,
                'area' => $this->serviceProvider->area?->name,
                'full_location' => $this->serviceProvider->full_location,
            ],
            'service_category' => [
                'id' => $this->serviceCategory->id,
                'name' => $this->serviceCategory->name,
                'image' => $this->serviceCategory->image,
            ],
            'images' => $this->images->map(function ($image) {
                return [
                    'id' => $image->id,
                    'image' => $image->image,
                    'alt_text' => $image->alt_text,
                ];
            }),
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
