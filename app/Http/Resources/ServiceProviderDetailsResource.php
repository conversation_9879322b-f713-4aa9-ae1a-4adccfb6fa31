<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceProviderDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'logo' => $this->logo,
            'city' => $this->city?->name,
            'area' => $this->area?->name,
            'location' => $this->full_location,
            'description' => $this->description,
            'google_maps_url' => $this->google_maps_url,
            'rating' => $this->rating,
            'review_count' => $this->review_count,
            'gallery' => ServiceProviderGalleryResource::collection($this->whenLoaded('gallery')),
            'working_hours_status' => $this->working_hours_status,
            'working_hours' => $this->formatted_working_hours,
            'services' => ServiceProviderServiceResource::collection($this->whenLoaded('activeServices')),
        ];
    }
}
