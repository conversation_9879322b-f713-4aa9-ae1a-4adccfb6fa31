<?php

namespace App\Repositories;

use App\Models\ProviderService;
use App\Repositories\Contracts\ProviderServiceRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class ProviderServiceRepository extends BaseRepository implements ProviderServiceRepositoryInterface
{
    public function __construct(ProviderService $model)
    {
        parent::__construct($model);
    }

    /**
     * Get active services.
     */
    public function getActive(): Collection
    {
        return $this->model->active()->with(['serviceProvider', 'serviceCategory'])->get();
    }

    /**
     * Get services by category.
     */
    public function getByCategory(int $categoryId): Collection
    {
        return $this->model->inCategory($categoryId)
            ->active()
            ->with(['serviceProvider', 'images'])
            ->get();
    }

    /**
     * Get services by provider.
     */
    public function getByProvider(int $providerId): Collection
    {
        return $this->model->where('service_provider_id', $providerId)
            ->active()
            ->with(['serviceCategory', 'images'])
            ->get();
    }

    /**
     * Get services within price range.
     */
    public function getByPriceRange(float $minPrice, float $maxPrice): Collection
    {
        return $this->model->priceBetween($minPrice, $maxPrice)
            ->active()
            ->with(['serviceProvider', 'serviceCategory'])
            ->get();
    }

    /**
     * Get services with minimum rating.
     */
    public function getWithMinRating(float $rating): Collection
    {
        return $this->model->withMinRating($rating)
            ->active()
            ->with(['serviceProvider', 'serviceCategory'])
            ->get();
    }

    /**
     * Get services with maximum duration.
     */
    public function getWithMaxDuration(int $duration): Collection
    {
        return $this->model->withMaxDuration($duration)
            ->active()
            ->with(['serviceProvider', 'serviceCategory'])
            ->get();
    }

    /**
     * Search services by title or description.
     */
    public function search(string $query): Collection
    {
        return $this->model->where(function ($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('description', 'LIKE', "%{$query}%");
        })
        ->active()
        ->with(['serviceProvider', 'serviceCategory'])
        ->get();
    }

    /**
     * Get service with full details.
     */
    public function getWithDetails(int $id): ?ProviderService
    {
        return $this->model->with([
            'serviceProvider.gallery',
            'serviceProvider.workingHours',
            'serviceCategory',
            'images'
        ])->find($id);
    }

    /**
     * Get popular services (most favourited).
     */
    public function getPopular(int $limit = 10): Collection
    {
        return $this->model->withCount('favourites')
            ->active()
            ->orderBy('favourites_count', 'desc')
            ->limit($limit)
            ->with(['serviceProvider', 'serviceCategory'])
            ->get();
    }

    /**
     * Get featured services.
     */
    public function getFeatured(): Collection
    {
        return $this->model->active()
            ->where('rating', '>=', 4.0)
            ->orderBy('rating', 'desc')
            ->limit(6)
            ->with(['serviceProvider', 'serviceCategory'])
            ->get();
    }

    /**
     * Update service rating.
     */
    public function updateRating(int $id, float $rating): bool
    {
        return $this->model->where('id', $id)->update(['rating' => $rating]) > 0;
    }
}
