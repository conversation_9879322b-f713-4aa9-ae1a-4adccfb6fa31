<?php

namespace App\Repositories\Contracts;

use Illuminate\Database\Eloquent\Collection;

interface MainImageSliderRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get active sliders ordered by sort order.
     */
    public function getActiveOrdered(): Collection;

    /**
     * Get all sliders ordered by sort order.
     */
    public function getAllOrdered(): Collection;

    /**
     * Update sort orders.
     */
    public function updateSortOrders(array $sortOrders): bool;

    /**
     * Toggle slider status.
     */
    public function toggleStatus(int $id): bool;
}
