<?php

namespace App\Repositories\Contracts;

use App\Models\City;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface CityRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Find city by name.
     */
    public function findByName(string $name): ?City;

    /**
     * Search cities by name.
     */
    public function searchByName(string $name): Collection;

    /**
     * Get cities with their areas.
     */
    public function getWithAreas(): Collection;

    /**
     * Get cities with customers count.
     */
    public function getWithCustomersCount(): Collection;

    /**
     * Get cities with service providers count.
     */
    public function getWithServiceProvidersCount(): Collection;

    /**
     * Get cities with areas count.
     */
    public function getWithAreasCount(): Collection;

    /**
     * Get cities that have customers.
     */
    public function getWithCustomers(): Collection;

    /**
     * Get cities that have service providers.
     */
    public function getWithServiceProviders(): Collection;

    /**
     * Get cities with pagination and filters for admin.
     */
    public function getCitiesWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator;

    /**
     * Get cities statistics for admin dashboard.
     */
    public function getStatistics(): array;

    /**
     * Restore soft deleted city.
     */
    public function restore(int $id): bool;

    /**
     * Force delete city permanently.
     */
    public function forceDelete(int $id): bool;

    /**
     * Get trashed cities.
     */
    public function getTrashed(): Collection;

    /**
     * Bulk delete cities.
     */
    public function bulkDelete(array $ids): bool;

    /**
     * Bulk restore cities.
     */
    public function bulkRestore(array $ids): bool;
}
