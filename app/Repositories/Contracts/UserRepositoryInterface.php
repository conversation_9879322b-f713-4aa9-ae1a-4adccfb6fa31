<?php

namespace App\Repositories\Contracts;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface UserRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Find user by email.
     */
    public function findByEmail(string $email): ?User;

    /**
     * Find user by mobile number.
     */
    public function findByMobile(string $mobile): ?User;

    /**
     * Get active users.
     */
    public function getActiveUsers(): Collection;

    /**
     * Get inactive users.
     */
    public function getInactiveUsers(): Collection;

    /**
     * Search users by name or email.
     */
    public function searchUsers(string $query, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get users with pagination and filters.
     */
    public function getUsersWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator;

    /**
     * Activate user.
     */
    public function activate(int $userId): bool;

    /**
     * Deactivate user.
     */
    public function deactivate(int $userId): bool;

    /**
     * Get users count by status.
     */
    public function getCountByStatus(): array;
}
