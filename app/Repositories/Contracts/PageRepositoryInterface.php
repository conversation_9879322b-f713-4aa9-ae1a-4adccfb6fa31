<?php

namespace App\Repositories\Contracts;

use Illuminate\Database\Eloquent\Collection;

interface PageRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get all pages ordered by creation date.
     */
    public function getAllOrdered(): Collection;

    /**
     * Find page by title.
     */
    public function findByTitle(string $title): ?object;

    /**
     * Search pages by title.
     */
    public function searchByTitle(string $title): Collection;
}
