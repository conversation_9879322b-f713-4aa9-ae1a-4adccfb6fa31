<?php

namespace App\Repositories\Contracts;

use App\Models\ServiceCategory;
use Illuminate\Database\Eloquent\Collection;

interface ServiceCategoryRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get categories with active services.
     */
    public function getWithActiveServices(): Collection;

    /**
     * Get category with services count.
     */
    public function getWithServicesCount(): Collection;

    /**
     * Find category by name.
     */
    public function findByName(string $name): ?ServiceCategory;

    /**
     * Get category with its services.
     */
    public function getWithServices(int $id): ?ServiceCategory;

    /**
     * Get popular categories (with most services).
     */
    public function getPopular(int $limit = 10): Collection;
}
