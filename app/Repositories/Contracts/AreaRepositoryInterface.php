<?php

namespace App\Repositories\Contracts;

use App\Models\Area;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface AreaRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get areas by city ID.
     */
    public function getByCity(int $cityId): Collection;

    /**
     * Find area by name within a city.
     */
    public function findByCityAndName(int $cityId, string $name): ?Area;

    /**
     * Search areas by name.
     */
    public function searchByName(string $name): Collection;

    /**
     * Get areas with their city.
     */
    public function getWithCity(): Collection;

    /**
     * Get areas with customers count.
     */
    public function getWithCustomersCount(): Collection;

    /**
     * Get areas with service providers count.
     */
    public function getWithServiceProvidersCount(): Collection;

    /**
     * Get areas that have customers.
     */
    public function getWithCustomers(): Collection;

    /**
     * Get areas that have service providers.
     */
    public function getWithServiceProviders(): Collection;

    /**
     * Get areas by city with counts.
     */
    public function getByCityWithCounts(int $cityId): Collection;

    /**
     * Get areas with pagination and filters for admin.
     */
    public function getAreasWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator;

    /**
     * Get areas statistics for admin dashboard.
     */
    public function getStatistics(): array;

    /**
     * Restore soft deleted area.
     */
    public function restore(int $id): bool;

    /**
     * Force delete area permanently.
     */
    public function forceDelete(int $id): bool;

    /**
     * Get trashed areas.
     */
    public function getTrashed(): Collection;

    /**
     * Bulk delete areas.
     */
    public function bulkDelete(array $ids): bool;

    /**
     * Bulk restore areas.
     */
    public function bulkRestore(array $ids): bool;
}
