<?php

namespace App\Repositories\Contracts;

use App\Models\ContactMessage;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface ContactMessageRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get all messages ordered by latest.
     */
    public function getAllLatest(): Collection;

    /**
     * Search messages by name.
     */
    public function searchByName(string $name): Collection;

    /**
     * Search messages by email.
     */
    public function searchByEmail(string $email): Collection;

    /**
     * Get messages by date range.
     */
    public function getByDateRange(string $startDate, string $endDate): Collection;

    /**
     * Search messages by title.
     */
    public function searchByTitle(string $title): Collection;

    /**
     * Search messages by content (title or message).
     */
    public function searchByContent(string $content): Collection;

    /**
     * Get messages by status.
     */
    public function getByStatus(string $status): Collection;

    /**
     * Get messages with filters and pagination.
     */
    public function getWithFilters(array $filters, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get messages statistics.
     */
    public function getStatistics(): array;

    /**
     * Get pending messages count.
     */
    public function getPendingCount(): int;

    /**
     * Get read messages count.
     */
    public function getReadCount(): int;

    /**
     * Get archived messages count.
     */
    public function getArchivedCount(): int;

    /**
     * Bulk update status for multiple messages.
     */
    public function bulkUpdateStatus(array $ids, string $status): bool;

    /**
     * Bulk delete multiple messages.
     */
    public function bulkDelete(array $ids): bool;
}
