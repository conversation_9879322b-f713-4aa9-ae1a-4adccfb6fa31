<?php

namespace App\Repositories\Contracts;

use App\Models\Notification;
use Illuminate\Database\Eloquent\Collection;

interface NotificationRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get all notifications for a specific customer ordered by latest.
     */
    public function getForCustomer(int $customerId): Collection;

    /**
     * Get unread notifications for a specific customer.
     */
    public function getUnreadForCustomer(int $customerId): Collection;

    /**
     * Get notifications by type for a specific customer.
     */
    public function getByTypeForCustomer(int $customerId, string $type): Collection;

    /**
     * Mark all notifications as read for a specific customer.
     */
    public function markAllAsReadForCustomer(int $customerId): bool;

    /**
     * Clear all notifications for a specific customer (soft delete).
     */
    public function clearAllForCustomer(int $customerId): bool;

    /**
     * Count unread notifications for a specific customer.
     */
    public function countUnreadForCustomer(int $customerId): int;

    /**
     * Mark specific notification as read.
     */
    public function markAsRead(int $id): bool;

    /**
     * Create notification for a customer.
     */
    public function createForCustomer(int $customerId, array $data): Notification;
}
