<?php

namespace App\Repositories\Contracts;

use App\Models\ServiceProvider;
use Illuminate\Database\Eloquent\Collection;

interface ServiceProviderRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get active service providers.
     */
    public function getActive(): Collection;

    /**
     * Get providers in a specific city.
     */
    public function getByCity(int $cityId): Collection;

    /**
     * Get providers in a specific area.
     */
    public function getByArea(int $areaId): Collection;

    /**
     * Get providers by city name.
     */
    public function getByCityName(string $cityName): Collection;

    /**
     * Get providers by area name.
     */
    public function getByAreaName(string $areaName): Collection;

    /**
     * Get providers with their location details.
     */
    public function getWithLocation(): Collection;

    /**
     * Get providers with minimum rating.
     */
    public function getWithMinRating(float $rating): Collection;

    /**
     * Get providers within radius of coordinates.
     */
    public function getWithinRadius(float $latitude, float $longitude, float $radiusKm): Collection;

    /**
     * Search providers by name or description.
     */
    public function search(string $query): Collection;

    /**
     * Get provider with services and gallery.
     */
    public function getWithDetails(int $id): ?ServiceProvider;

    /**
     * Get provider with full details for API response.
     */
    public function getWithFullDetails(int $id): ?ServiceProvider;

    /**
     * Get providers offering specific service category.
     */
    public function getByServiceCategory(int $categoryId): Collection;

    /**
     * Update provider rating.
     */
    public function updateRating(int $id, float $rating): bool;

    /**
     * Search active providers by name or description.
     */
    public function searchActiveProviders(string $query): Collection;

    /**
     * Get providers with filters for admin panel.
     */
    public function getWithFilters(array $filters, int $perPage = 15);

    /**
     * Get providers statistics for admin dashboard.
     */
    public function getStatistics(): array;

    /**
     * Bulk update status.
     */
    public function bulkUpdateStatus(array $ids, bool $status): int;

    /**
     * Restore soft deleted provider.
     */
    public function restore(int $id): bool;

    /**
     * Force delete provider.
     */
    public function forceDelete(int $id): bool;

    /**
     * Bulk delete providers.
     */
    public function bulkDelete(array $ids): int;

    /**
     * Bulk restore providers.
     */
    public function bulkRestore(array $ids): int;

    /**
     * Find provider by email.
     */
    public function findByEmail(string $email): ?ServiceProvider;

    /**
     * Check if email exists.
     */
    public function existsByEmail(string $email): bool;
}
