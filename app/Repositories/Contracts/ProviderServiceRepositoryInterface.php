<?php

namespace App\Repositories\Contracts;

use App\Models\ProviderService;
use Illuminate\Database\Eloquent\Collection;

interface ProviderServiceRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get active services.
     */
    public function getActive(): Collection;

    /**
     * Get services by category.
     */
    public function getByCategory(int $categoryId): Collection;

    /**
     * Get services by provider.
     */
    public function getByProvider(int $providerId): Collection;

    /**
     * Get services within price range.
     */
    public function getByPriceRange(float $minPrice, float $maxPrice): Collection;

    /**
     * Get services with minimum rating.
     */
    public function getWithMinRating(float $rating): Collection;

    /**
     * Get services with maximum duration.
     */
    public function getWithMaxDuration(int $duration): Collection;

    /**
     * Search services by title or description.
     */
    public function search(string $query): Collection;

    /**
     * Get service with full details.
     */
    public function getWithDetails(int $id): ?ProviderService;

    /**
     * Get popular services (most favourited).
     */
    public function getPopular(int $limit = 10): Collection;

    /**
     * Get featured services.
     */
    public function getFeatured(): Collection;

    /**
     * Update service rating.
     */
    public function updateRating(int $id, float $rating): bool;
}
