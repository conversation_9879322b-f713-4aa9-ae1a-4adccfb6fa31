<?php

namespace App\Repositories\Contracts;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Collection;

interface CustomerRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Find customer by email.
     */
    public function findByEmail(string $email): ?Customer;

    /**
     * Find customer by mobile.
     */
    public function findByMobile(string $mobile): ?Customer;

    /**
     * Get customers in a specific city.
     */
    public function getByCity(int $cityId): Collection;

    /**
     * Get customers in a specific area.
     */
    public function getByArea(int $areaId): Collection;

    /**
     * Get customers by city name.
     */
    public function getByCityName(string $cityName): Collection;

    /**
     * Get customers by area name.
     */
    public function getByAreaName(string $areaName): Collection;

    /**
     * Get customers with their location details.
     */
    public function getWithLocation(): Collection;

    /**
     * Get customers within radius of coordinates.
     */
    public function getWithinRadius(float $latitude, float $longitude, float $radiusKm): Collection;

    /**
     * Get customer's favourite services.
     */
    public function getFavouriteServices(int $customerId): Collection;

    /**
     * Add service to customer's favourites.
     */
    public function addToFavourites(int $customerId, int $serviceId): bool;

    /**
     * Remove service from customer's favourites.
     */
    public function removeFromFavourites(int $customerId, int $serviceId): bool;

    /**
     * Check if service is in customer's favourites.
     */
    public function isFavourite(int $customerId, int $serviceId): bool;
}
