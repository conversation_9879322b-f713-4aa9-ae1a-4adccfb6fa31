<?php

namespace App\Repositories;

use App\Models\ServiceCategory;
use App\Repositories\Contracts\ServiceCategoryRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class ServiceCategoryRepository extends BaseRepository implements ServiceCategoryRepositoryInterface
{
    public function __construct(ServiceCategory $model)
    {
        parent::__construct($model);
    }

    /**
     * Get categories with active services.
     */
    public function getWithActiveServices(): Collection
    {
        return $this->model->withActiveServices()->get();
    }

    /**
     * Get category with services count.
     */
    public function getWithServicesCount(): Collection
    {
        return $this->model->withCount(['providerServices as active_services_count' => function ($query) {
            $query->where('is_active', true);
        }])->get();
    }

    /**
     * Find category by name.
     */
    public function findByName(string $name): ?ServiceCategory
    {
        return $this->model->where('name', $name)->first();
    }

    /**
     * Get category with its services.
     */
    public function getWithServices(int $id): ?ServiceCategory
    {
        return $this->model->with(['activeProviderServices.serviceProvider'])->find($id);
    }

    /**
     * Get popular categories (with most services).
     */
    public function getPopular(int $limit = 10): Collection
    {
        return $this->model->withCount(['providerServices as active_services_count' => function ($query) {
            $query->where('is_active', true);
        }])
        ->having('active_services_count', '>', 0)
        ->orderBy('active_services_count', 'desc')
        ->limit($limit)
        ->get();
    }
}
