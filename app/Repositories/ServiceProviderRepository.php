<?php

namespace App\Repositories;

use App\Models\ServiceProvider;
use App\Repositories\Contracts\ServiceProviderRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class ServiceProviderRepository extends BaseRepository implements ServiceProviderRepositoryInterface
{
    public function __construct(ServiceProvider $model)
    {
        parent::__construct($model);
    }

    /**
     * Get active service providers.
     */
    public function getActive(): Collection
    {
        return $this->model->active()->get();
    }

    /**
     * Get providers in a specific city.
     */
    public function getByCity(int $cityId): Collection
    {
        return $this->model->inCity($cityId)->get();
    }

    /**
     * Get providers in a specific area.
     */
    public function getByArea(int $areaId): Collection
    {
        return $this->model->inArea($areaId)->get();
    }

    /**
     * Get providers by city name.
     */
    public function getByCityName(string $cityName): Collection
    {
        return $this->model->whereHas('city', function ($query) use ($cityName) {
            $query->where('name', $cityName);
        })->get();
    }

    /**
     * Get providers by area name.
     */
    public function getByAreaName(string $areaName): Collection
    {
        return $this->model->whereHas('area', function ($query) use ($areaName) {
            $query->where('name', $areaName);
        })->get();
    }

    /**
     * Get providers with their location details.
     */
    public function getWithLocation(): Collection
    {
        return $this->model->withLocation()->get();
    }

    /**
     * Get providers with minimum rating.
     */
    public function getWithMinRating(float $rating): Collection
    {
        return $this->model->withMinRating($rating)->get();
    }

    /**
     * Get providers within radius of coordinates.
     */
    public function getWithinRadius(float $latitude, float $longitude, float $radiusKm): Collection
    {
        return $this->model->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->whereRaw(
                '(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?',
                [$latitude, $longitude, $latitude, $radiusKm]
            )
            ->get();
    }

    /**
     * Search providers by name or description.
     */
    public function search(string $query): Collection
    {
        return $this->model->whereTranslationLike('name', "%{$query}%")
            ->orWhereTranslationLike('description', "%{$query}%")
            ->get();
    }

    /**
     * Get provider with services and gallery.
     */
    public function getWithDetails(int $id): ?ServiceProvider
    {
        return $this->model->with([
            'services' => function ($query) {
                $query->active()->with(['serviceCategory', 'images']);
            },
            'gallery',
            'workingHours'
        ])->find($id);
    }

    /**
     * Get provider with full details for API response.
     */
    public function getWithFullDetails(int $id): ?ServiceProvider
    {
        return $this->model->with([
            'city',
            'area',
            'gallery' => function ($query) {
                $query->orderBy('sort_order');
            },
            'workingHours' => function ($query) {
                $query->orderByRaw("FIELD(day, 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')");
            },
            'activeServices' => function ($query) {
                $query->with(['serviceCategory'])
                    ->orderBy('rating', 'desc')
                    ->orderBy('title');
            }
        ])->active()->find($id);
    }

    /**
     * Get providers offering specific service category.
     */
    public function getByServiceCategory(int $categoryId): Collection
    {
        return $this->model->whereHas('services', function ($query) use ($categoryId) {
            $query->where('service_category_id', $categoryId)
                ->where('is_active', true);
        })->get();
    }

    /**
     * Update provider rating.
     */
    public function updateRating(int $id, float $rating): bool
    {
        return $this->model->where('id', $id)->update(['rating' => $rating]) > 0;
    }

    /**
     * Search active providers by name or description.
     */
    public function searchActiveProviders(string $query): Collection
    {
        return $this->model->active()
            ->whereTranslationLike('name', "%{$query}%")
            ->orWhereTranslationLike('description', "%{$query}%")
            ->get();
    }

    /**
     * Get providers with filters for admin panel.
     */
    public function getWithFilters(array $filters, int $perPage = 15)
    {
        $query = $this->model->with(['city', 'area']);

        // Search filter
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->whereTranslationLike('name', "%{$filters['search']}%")
                    ->orWhereTranslationLike('description', "%{$filters['search']}%");
            });
        }

        // City filter
        if (!empty($filters['city_id'])) {
            $query->where('city_id', $filters['city_id']);
        }

        // Area filter
        if (!empty($filters['area_id'])) {
            $query->where('area_id', $filters['area_id']);
        }

        // Status filter
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        // Date range filters
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get providers statistics for admin dashboard.
     */
    public function getStatistics(): array
    {
        return [
            'total' => $this->model->count(),
            'active' => $this->model->where('is_active', true)->count(),
            'inactive' => $this->model->where('is_active', false)->count(),
            'with_services' => $this->model->whereHas('services')->count(),
        ];
    }

    /**
     * Bulk update status.
     */
    public function bulkUpdateStatus(array $ids, bool $status): int
    {
        return $this->model->whereIn('id', $ids)->update(['is_active' => $status]);
    }

    /**
     * Restore soft deleted provider.
     */
    public function restore(int $id): bool
    {
        $provider = $this->model->withTrashed()->find($id);
        return $provider ? $provider->restore() : false;
    }

    /**
     * Force delete provider.
     */
    public function forceDelete(int $id): bool
    {
        $provider = $this->model->withTrashed()->find($id);
        return $provider ? $provider->forceDelete() : false;
    }

    /**
     * Bulk delete providers.
     */
    public function bulkDelete(array $ids): int
    {
        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * Bulk restore providers.
     */
    public function bulkRestore(array $ids): int
    {
        return $this->model->withTrashed()->whereIn('id', $ids)->restore();
    }

    /**
     * Find provider by email.
     */
    public function findByEmail(string $email): ?ServiceProvider
    {
        return $this->model->where('email', $email)->first();
    }

    /**
     * Check if email exists.
     */
    public function existsByEmail(string $email): bool
    {
        return $this->model->where('email', $email)->exists();
    }
}
