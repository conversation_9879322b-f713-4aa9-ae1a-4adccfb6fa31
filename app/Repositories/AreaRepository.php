<?php

namespace App\Repositories;

use App\Models\Area;
use App\Repositories\Contracts\AreaRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class AreaRepository extends BaseRepository implements AreaRepositoryInterface
{
    public function __construct(Area $model)
    {
        parent::__construct($model);
    }

    /**
     * Get areas by city ID.
     */
    public function getByCity(int $cityId): Collection
    {
        return $this->model->inCity($cityId)->get();
    }

    /**
     * Find area by name within a city.
     */
    public function findByCityAndName(int $cityId, string $name): ?Area
    {
        return $this->model->where('city_id', $cityId)
            ->where('name', $name)
            ->first();
    }

    /**
     * Search areas by name.
     */
    public function searchByName(string $name): Collection
    {
        return $this->model->searchByName($name)->get();
    }

    /**
     * Get areas with their city.
     */
    public function getWithCity(): Collection
    {
        return $this->model->withCity()->get();
    }

    /**
     * Get areas with customers count.
     */
    public function getWithCustomersCount(): Collection
    {
        return $this->model->withCustomersCount()->get();
    }

    /**
     * Get areas with service providers count.
     */
    public function getWithServiceProvidersCount(): Collection
    {
        return $this->model->withServiceProvidersCount()->get();
    }

    /**
     * Get areas that have customers.
     */
    public function getWithCustomers(): Collection
    {
        return $this->model->whereHas('customers')->get();
    }

    /**
     * Get areas that have service providers.
     */
    public function getWithServiceProviders(): Collection
    {
        return $this->model->whereHas('serviceProviders')->get();
    }

    /**
     * Get areas by city with counts.
     */
    public function getByCityWithCounts(int $cityId): Collection
    {
        return $this->model->inCity($cityId)
            ->withCustomersCount()
            ->withServiceProvidersCount()
            ->get();
    }

    /**
     * Get areas with pagination and filters for admin.
     */
    public function getAreasWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->with('city')
            ->withCount(['customers', 'serviceProviders']);

        // Apply search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->whereTranslationLike('name', "%{$search}%")
                ->orWhereHas('city', function ($q) use ($search) {
                    $q->whereTranslationLike('name', "%{$search}%");
                });
        }

        // Apply city filter
        if (!empty($filters['city_id'])) {
            $query->where('city_id', $filters['city_id']);
        }

        // Apply date range filters
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get areas statistics for admin dashboard.
     */
    public function getStatistics(): array
    {
        $total = $this->model->count();
        $withCustomers = $this->model->whereHas('customers')->count();
        $withServiceProviders = $this->model->whereHas('serviceProviders')->count();
        $trashed = $this->model->onlyTrashed()->count();

        return [
            'total' => $total,
            'with_customers' => $withCustomers,
            'with_service_providers' => $withServiceProviders,
            'trashed' => $trashed,
        ];
    }

    /**
     * Restore soft deleted area.
     */
    public function restore(int $id): bool
    {
        $area = $this->model->onlyTrashed()->findOrFail($id);
        return $area->restore();
    }

    /**
     * Force delete area permanently.
     */
    public function forceDelete(int $id): bool
    {
        $area = $this->model->withTrashed()->findOrFail($id);
        return $area->forceDelete();
    }

    /**
     * Get trashed areas.
     */
    public function getTrashed(): Collection
    {
        return $this->model->onlyTrashed()->with('city')->get();
    }

    /**
     * Bulk delete areas.
     */
    public function bulkDelete(array $ids): bool
    {
        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * Bulk restore areas.
     */
    public function bulkRestore(array $ids): bool
    {
        return $this->model->onlyTrashed()->whereIn('id', $ids)->restore();
    }
}
