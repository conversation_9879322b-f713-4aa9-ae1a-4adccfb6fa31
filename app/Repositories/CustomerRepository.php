<?php

namespace App\Repositories;

use App\Models\Customer;
use App\Models\FavouriteService;
use App\Repositories\Contracts\CustomerRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CustomerRepository extends BaseRepository implements CustomerRepositoryInterface
{
    public function __construct(Customer $model)
    {
        parent::__construct($model);
    }

    /**
     * Find customer by email.
     */
    public function findByEmail(string $email): ?Customer
    {
        return $this->model->where('email', $email)->first();
    }

    /**
     * Find customer by mobile.
     */
    public function findByMobile(string $mobile): ?Customer
    {
        return $this->model->where('mobile', $mobile)->first();
    }

    /**
     * Get customers in a specific city.
     */
    public function getByCity(int $cityId): Collection
    {
        return $this->model->inCity($cityId)->get();
    }

    /**
     * Get customers in a specific area.
     */
    public function getByArea(int $areaId): Collection
    {
        return $this->model->inArea($areaId)->get();
    }

    /**
     * Get customers by city name.
     */
    public function getByCityName(string $cityName): Collection
    {
        return $this->model->whereHas('city', function ($query) use ($cityName) {
            $query->where('name', $cityName);
        })->get();
    }

    /**
     * Get customers by area name.
     */
    public function getByAreaName(string $areaName): Collection
    {
        return $this->model->whereHas('area', function ($query) use ($areaName) {
            $query->where('name', $areaName);
        })->get();
    }

    /**
     * Get customers with their location details.
     */
    public function getWithLocation(): Collection
    {
        return $this->model->withLocation()->get();
    }

    /**
     * Get customers within radius of coordinates.
     */
    public function getWithinRadius(float $latitude, float $longitude, float $radiusKm): Collection
    {
        return $this->model->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->whereRaw(
                '(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?',
                [$latitude, $longitude, $latitude, $radiusKm]
            )
            ->get();
    }

    /**
     * Get customer's favourite services.
     */
    public function getFavouriteServices(int $customerId): Collection
    {
        $customer = $this->findOrFail($customerId);
        return $customer->favouriteServices()->with(['serviceProvider', 'serviceCategory'])->get();
    }

    /**
     * Add service to customer's favourites.
     */
    public function addToFavourites(int $customerId, int $serviceId): bool
    {
        try {
            FavouriteService::firstOrCreate([
                'customer_id' => $customerId,
                'provider_service_id' => $serviceId,
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Remove service from customer's favourites.
     */
    public function removeFromFavourites(int $customerId, int $serviceId): bool
    {
        return FavouriteService::where('customer_id', $customerId)
            ->where('provider_service_id', $serviceId)
            ->delete() > 0;
    }

    /**
     * Check if service is in customer's favourites.
     */
    public function isFavourite(int $customerId, int $serviceId): bool
    {
        return FavouriteService::where('customer_id', $customerId)
            ->where('provider_service_id', $serviceId)
            ->exists();
    }
}
