<?php

namespace App\Repositories;

use App\Models\City;
use App\Repositories\Contracts\CityRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CityRepository extends BaseRepository implements CityRepositoryInterface
{
    public function __construct(City $model)
    {
        parent::__construct($model);
    }

    /**
     * Find city by name.
     */
    public function findByName(string $name): ?City
    {
        return $this->model->where('name', $name)->first();
    }

    /**
     * Search cities by name.
     */
    public function searchByName(string $name): Collection
    {
        return $this->model->searchByName($name)->get();
    }

    /**
     * Get cities with their areas.
     */
    public function getWithAreas(): Collection
    {
        return $this->model->withAreas()->get();
    }

    /**
     * Get cities with customers count.
     */
    public function getWithCustomersCount(): Collection
    {
        return $this->model->withCustomersCount()->get();
    }

    /**
     * Get cities with service providers count.
     */
    public function getWithServiceProvidersCount(): Collection
    {
        return $this->model->withServiceProvidersCount()->get();
    }

    /**
     * Get cities with areas count.
     */
    public function getWithAreasCount(): Collection
    {
        return $this->model->withCount('areas')->get();
    }

    /**
     * Get cities that have customers.
     */
    public function getWithCustomers(): Collection
    {
        return $this->model->whereHas('customers')->get();
    }

    /**
     * Get cities that have service providers.
     */
    public function getWithServiceProviders(): Collection
    {
        return $this->model->whereHas('serviceProviders')->get();
    }

    /**
     * Get cities with pagination and filters for admin.
     */
    public function getCitiesWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->withCount(['areas', 'customers', 'serviceProviders']);

        // Apply search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->whereTranslationLike('name', "%{$search}%");
        }

        // Apply date range filters
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get cities statistics for admin dashboard.
     */
    public function getStatistics(): array
    {
        $total = $this->model->count();
        $withAreas = $this->model->whereHas('areas')->count();
        $withCustomers = $this->model->whereHas('customers')->count();
        $withServiceProviders = $this->model->whereHas('serviceProviders')->count();
        $trashed = $this->model->onlyTrashed()->count();

        return [
            'total' => $total,
            'with_areas' => $withAreas,
            'with_customers' => $withCustomers,
            'with_service_providers' => $withServiceProviders,
            'trashed' => $trashed,
        ];
    }

    /**
     * Restore soft deleted city.
     */
    public function restore(int $id): bool
    {
        $city = $this->model->onlyTrashed()->findOrFail($id);
        return $city->restore();
    }

    /**
     * Force delete city permanently.
     */
    public function forceDelete(int $id): bool
    {
        $city = $this->model->withTrashed()->findOrFail($id);
        return $city->forceDelete();
    }

    /**
     * Get trashed cities.
     */
    public function getTrashed(): Collection
    {
        return $this->model->onlyTrashed()->get();
    }

    /**
     * Bulk delete cities.
     */
    public function bulkDelete(array $ids): bool
    {
        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * Bulk restore cities.
     */
    public function bulkRestore(array $ids): bool
    {
        return $this->model->onlyTrashed()->whereIn('id', $ids)->restore();
    }
}
