<?php

namespace App\Repositories;

use App\Models\ContactMessage;
use App\Repositories\Contracts\ContactMessageRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ContactMessageRepository extends BaseRepository implements ContactMessageRepositoryInterface
{
    public function __construct(ContactMessage $model)
    {
        parent::__construct($model);
    }

    /**
     * Get all messages ordered by latest.
     */
    public function getAllLatest(): Collection
    {
        return $this->model->latest()->get();
    }

    /**
     * Search messages by name.
     */
    public function searchByName(string $name): Collection
    {
        return $this->model->searchByName($name)->latest()->get();
    }

    /**
     * Search messages by email.
     */
    public function searchByEmail(string $email): Collection
    {
        return $this->model->searchByEmail($email)->latest()->get();
    }

    /**
     * Get messages by date range.
     */
    public function getByDateRange(string $startDate, string $endDate): Collection
    {
        return $this->model->whereBetween('created_at', [$startDate, $endDate])
            ->latest()
            ->get();
    }

    /**
     * Search messages by title.
     */
    public function searchByTitle(string $title): Collection
    {
        return $this->model->searchByTitle($title)->latest()->get();
    }

    /**
     * Search messages by content (title or message).
     */
    public function searchByContent(string $content): Collection
    {
        return $this->model->searchByContent($content)->latest()->get();
    }

    /**
     * Get messages by status.
     */
    public function getByStatus(string $status): Collection
    {
        return $this->model->byStatus($status)->latest()->get();
    }

    /**
     * Get messages with filters and pagination.
     */
    public function getWithFilters(array $filters, int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        // Apply search filter
        if (!empty($filters['search'])) {
            $query->searchByContent($filters['search']);
        }

        // Apply status filter
        if (!empty($filters['status'])) {
            $query->byStatus($filters['status']);
        }

        // Apply date range filter
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->latest()->paginate($perPage);
    }

    /**
     * Get messages statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total' => $this->model->count(),
            'pending' => $this->getPendingCount(),
            'read' => $this->getReadCount(),
            'archived' => $this->getArchivedCount(),
        ];
    }

    /**
     * Get pending messages count.
     */
    public function getPendingCount(): int
    {
        return $this->model->pending()->count();
    }

    /**
     * Get read messages count.
     */
    public function getReadCount(): int
    {
        return $this->model->read()->count();
    }

    /**
     * Get archived messages count.
     */
    public function getArchivedCount(): int
    {
        return $this->model->archived()->count();
    }

    /**
     * Bulk update status for multiple messages.
     */
    public function bulkUpdateStatus(array $ids, string $status): bool
    {
        return $this->model->whereIn('id', $ids)->update(['status' => $status]) > 0;
    }

    /**
     * Bulk delete multiple messages.
     */
    public function bulkDelete(array $ids): bool
    {
        return $this->model->whereIn('id', $ids)->delete() > 0;
    }
}
