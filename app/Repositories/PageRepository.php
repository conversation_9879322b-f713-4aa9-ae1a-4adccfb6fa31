<?php

namespace App\Repositories;

use App\Models\Page;
use App\Repositories\Contracts\PageRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class PageRepository extends BaseRepository implements PageRepositoryInterface
{
    public function __construct(Page $model)
    {
        parent::__construct($model);
    }

    /**
     * Get all pages ordered by creation date.
     */
    public function getAllOrdered(): Collection
    {
        return $this->model->orderBy('created_at', 'desc')->get();
    }

    /**
     * Find page by title.
     */
    public function findByTitle(string $title): ?Page
    {
        return $this->model->where('title', $title)->first();
    }

    /**
     * Search pages by title.
     */
    public function searchByTitle(string $title): Collection
    {
        return $this->model->where('title', 'like', '%' . $title . '%')
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
