<?php

namespace App\Repositories;

use App\Models\Notification;
use App\Repositories\Contracts\NotificationRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class NotificationRepository extends BaseRepository implements NotificationRepositoryInterface
{
    public function __construct(Notification $model)
    {
        parent::__construct($model);
    }

    /**
     * Get all notifications for a specific customer ordered by latest.
     */
    public function getForCustomer(int $customerId): Collection
    {
        return $this->model->forCustomer($customerId)
            ->latest()
            ->get();
    }

    /**
     * Get unread notifications for a specific customer.
     */
    public function getUnreadForCustomer(int $customerId): Collection
    {
        return $this->model->forCustomer($customerId)
            ->unread()
            ->latest()
            ->get();
    }

    /**
     * Get notifications by type for a specific customer.
     */
    public function getByTypeForCustomer(int $customerId, string $type): Collection
    {
        return $this->model->forCustomer($customerId)
            ->ofType($type)
            ->latest()
            ->get();
    }

    /**
     * Mark all notifications as read for a specific customer.
     */
    public function markAllAsReadForCustomer(int $customerId): bool
    {
        return $this->model->forCustomer($customerId)
            ->unread()
            ->update(['is_read' => true]) !== false;
    }

    /**
     * Clear all notifications for a specific customer (soft delete).
     */
    public function clearAllForCustomer(int $customerId): bool
    {
        return $this->model->forCustomer($customerId)
            ->delete() !== false;
    }

    /**
     * Count unread notifications for a specific customer.
     */
    public function countUnreadForCustomer(int $customerId): int
    {
        return $this->model->forCustomer($customerId)
            ->unread()
            ->count();
    }

    /**
     * Mark specific notification as read.
     */
    public function markAsRead(int $id): bool
    {
        $notification = $this->find($id);
        if (!$notification) {
            return false;
        }

        return $notification->markAsRead();
    }

    /**
     * Create notification for a customer.
     */
    public function createForCustomer(int $customerId, array $data): Notification
    {
        $data['customer_id'] = $customerId;
        return $this->create($data);
    }
}
