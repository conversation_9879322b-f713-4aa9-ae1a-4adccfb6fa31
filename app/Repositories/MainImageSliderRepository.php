<?php

namespace App\Repositories;

use App\Models\MainImageSlider;
use App\Repositories\Contracts\MainImageSliderRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class MainImageSliderRepository extends BaseRepository implements MainImageSliderRepositoryInterface
{
    public function __construct(MainImageSlider $model)
    {
        parent::__construct($model);
    }

    /**
     * Get active sliders ordered by sort order.
     */
    public function getActiveOrdered(): Collection
    {
        return $this->model->activeOrdered()->get();
    }

    /**
     * Get all sliders ordered by sort order.
     */
    public function getAllOrdered(): Collection
    {
        return $this->model->ordered()->get();
    }

    /**
     * Update sort orders.
     */
    public function updateSortOrders(array $sortOrders): bool
    {
        try {
            foreach ($sortOrders as $id => $sortOrder) {
                $this->model->where('id', $id)->update(['sort_order' => $sortOrder]);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Toggle slider status.
     */
    public function toggleStatus(int $id): bool
    {
        $slider = $this->find($id);
        if (!$slider) {
            return false;
        }

        return $slider->update(['is_active' => !$slider->is_active]);
    }
}
