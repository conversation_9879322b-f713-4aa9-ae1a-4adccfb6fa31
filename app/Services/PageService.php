<?php

namespace App\Services;

use App\Models\Page;
use App\Repositories\Contracts\PageRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Validation\ValidationException;

class PageService
{
    public function __construct(
        private PageRepositoryInterface $pageRepository
    ) {
    }

    /**
     * Get all pages.
     */
    public function getAllPages(): Collection
    {
        return $this->pageRepository->getAllOrdered();
    }

    /**
     * Get page by ID.
     */
    public function getPageById(int $id): ?Page
    {
        return $this->pageRepository->find($id);
    }

    /**
     * Get page by ID or fail.
     */
    public function getPageByIdOrFail(int $id): Page
    {
        return $this->pageRepository->findOrFail($id);
    }

    /**
     * Create a new page.
     */
    public function createPage(array $data): Page
    {
        // Validate unique title
        if ($this->pageRepository->findByTitle($data['title'])) {
            throw ValidationException::withMessages([
                'title' => ['The page title has already been taken.']
            ]);
        }

        return $this->pageRepository->create($data);
    }

    /**
     * Update a page.
     */
    public function updatePage(int $id, array $data): Page
    {
        // Check if title is unique (excluding current page)
        if (isset($data['title'])) {
            $existingPage = $this->pageRepository->findByTitle($data['title']);
            if ($existingPage && $existingPage->id !== $id) {
                throw ValidationException::withMessages([
                    'title' => ['The page title has already been taken.']
                ]);
            }
        }

        return $this->pageRepository->update($id, $data);
    }

    /**
     * Delete a page.
     */
    public function deletePage(int $id): bool
    {
        return $this->pageRepository->delete($id);
    }

    /**
     * Search pages by title.
     */
    public function searchPages(string $query): Collection
    {
        return $this->pageRepository->searchByTitle($query);
    }
}
