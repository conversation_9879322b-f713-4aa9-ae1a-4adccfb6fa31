<?php

namespace App\Services;

use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

class LanguageService
{
    /**
     * Get the current active language
     *
     * @return string
     */
    public function getCurrentLanguage(): string
    {
        return LaravelLocalization::getCurrentLocale();
    }

    /**
     * Get the current language name
     *
     * @return string
     */
    public function getCurrentLanguageName(): string
    {
        $locale = $this->getCurrentLanguage();
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        return $supportedLocales[$locale]['name'] ?? $locale;
    }

    /**
     * Get the current language native name
     *
     * @return string
     */
    public function getCurrentLanguageNativeName(): string
    {
        $locale = $this->getCurrentLanguage();
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        return $supportedLocales[$locale]['native'] ?? $locale;
    }

    /**
     * Determine the current layout direction (RTL for Arabic, LTR for others)
     *
     * @return string
     */
    public function getCurrentDirection(): string
    {
        $locale = $this->getCurrentLanguage();
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        // Check if the current locale uses Arabic script (RTL)
        if (isset($supportedLocales[$locale]['script']) && $supportedLocales[$locale]['script'] === 'Arab') {
            return 'rtl';
        }
        
        // Default to LTR for all other languages
        return 'ltr';
    }

    /**
     * Check if the current language is RTL
     *
     * @return bool
     */
    public function isRtl(): bool
    {
        return $this->getCurrentDirection() === 'rtl';
    }

    /**
     * Check if the current language is LTR
     *
     * @return bool
     */
    public function isLtr(): bool
    {
        return $this->getCurrentDirection() === 'ltr';
    }

    /**
     * Get all supported languages
     *
     * @return array
     */
    public function getSupportedLanguages(): array
    {
        return LaravelLocalization::getSupportedLocales();
    }

    /**
     * Get language switching URLs for all supported languages
     *
     * @param string|null $route
     * @return array
     */
    public function getLanguageSwitchingUrls(?string $route = null): array
    {
        $urls = [];
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        foreach ($supportedLocales as $localeCode => $properties) {
            $urls[$localeCode] = [
                'code' => $localeCode,
                'name' => $properties['name'],
                'native' => $properties['native'],
                'url' => LaravelLocalization::getLocalizedURL($localeCode, $route),
                'direction' => $properties['script'] === 'Arab' ? 'rtl' : 'ltr',
                'is_current' => $localeCode === $this->getCurrentLanguage(),
            ];
        }
        
        return $urls;
    }

    /**
     * Get URL for a specific language
     *
     * @param string $locale
     * @param string|null $route
     * @return string|null
     */
    public function getLanguageUrl(string $locale, ?string $route = null): ?string
    {
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        if (!isset($supportedLocales[$locale])) {
            return null;
        }
        
        return LaravelLocalization::getLocalizedURL($locale, $route);
    }

    /**
     * Get the default language
     *
     * @return string
     */
    public function getDefaultLanguage(): string
    {
        return config('app.locale', 'ar');
    }

    /**
     * Check if the given locale is supported
     *
     * @param string $locale
     * @return bool
     */
    public function isLocaleSupported(string $locale): bool
    {
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        return isset($supportedLocales[$locale]);
    }

    /**
     * Get the appropriate CSS class for the current language direction
     *
     * @return string
     */
    public function getDirectionClass(): string
    {
        return $this->isRtl() ? 'rtl' : 'ltr';
    }

    /**
     * Get the appropriate HTML dir attribute value
     *
     * @return string
     */
    public function getHtmlDirection(): string
    {
        return $this->getCurrentDirection();
    }

    /**
     * Get language-specific font family
     *
     * @return string
     */
    public function getLanguageFont(): string
    {
        $locale = $this->getCurrentLanguage();
        
        switch ($locale) {
            case 'ar':
                return "'Tajawal', 'Cairo', 'Amiri', sans-serif";
            case 'en':
            default:
                return "'Inter', 'Helvetica Neue', Arial, sans-serif";
        }
    }

    /**
     * Get localized route name
     *
     * @param string $routeName
     * @return string
     */
    public function getLocalizedRouteName(string $routeName): string
    {
        $locale = $this->getCurrentLanguage();
        return "{$locale}.{$routeName}";
    }

    /**
     * Get non-localized URL (useful for API endpoints)
     *
     * @param string $path
     * @return string
     */
    public function getNonLocalizedUrl(string $path): string
    {
        return LaravelLocalization::getNonLocalizedURL($path);
    }

    /**
     * Get language-specific text alignment
     *
     * @return string
     */
    public function getTextAlignment(): string
    {
        return $this->isRtl() ? 'text-right' : 'text-left';
    }

    /**
     * Get opposite text alignment (useful for certain UI elements)
     *
     * @return string
     */
    public function getOppositeTextAlignment(): string
    {
        return $this->isRtl() ? 'text-left' : 'text-right';
    }
}
