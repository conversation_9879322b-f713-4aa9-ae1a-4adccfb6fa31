<?php

namespace App\Services;

use App\Models\ContactMessage;
use App\Repositories\Contracts\ContactMessageRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ContactMessageService
{
    public function __construct(
        private ContactMessageRepositoryInterface $contactMessageRepository
    ) {
    }

    /**
     * Create a new contact message.
     */
    public function create(array $data): ContactMessage
    {
        return $this->contactMessageRepository->create($data);
    }

    /**
     * Get all contact messages ordered by latest.
     */
    public function getAllLatest(): Collection
    {
        return $this->contactMessageRepository->getAllLatest();
    }

    /**
     * Get contact message by ID.
     */
    public function getById(int $id): ?ContactMessage
    {
        return $this->contactMessageRepository->find($id);
    }

    /**
     * Search messages by name.
     */
    public function searchByName(string $name): Collection
    {
        return $this->contactMessageRepository->searchByName($name);
    }

    /**
     * Search messages by email.
     */
    public function searchByEmail(string $email): Collection
    {
        return $this->contactMessageRepository->searchByEmail($email);
    }

    /**
     * Get messages by date range.
     */
    public function getByDateRange(string $startDate, string $endDate): Collection
    {
        return $this->contactMessageRepository->getByDateRange($startDate, $endDate);
    }

    /**
     * Update a contact message.
     */
    public function update(int $id, array $data): ContactMessage
    {
        return $this->contactMessageRepository->update($id, $data);
    }

    /**
     * Delete a contact message.
     */
    public function delete(int $id): bool
    {
        return $this->contactMessageRepository->delete($id);
    }

    /**
     * Get messages with filters and pagination.
     */
    public function getMessagesWithFilters(array $filters, int $perPage = 15): LengthAwarePaginator
    {
        return $this->contactMessageRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Get messages statistics.
     */
    public function getStatistics(): array
    {
        return $this->contactMessageRepository->getStatistics();
    }

    /**
     * Search messages by title.
     */
    public function searchByTitle(string $title): Collection
    {
        return $this->contactMessageRepository->searchByTitle($title);
    }

    /**
     * Search messages by content.
     */
    public function searchByContent(string $content): Collection
    {
        return $this->contactMessageRepository->searchByContent($content);
    }

    /**
     * Get messages by status.
     */
    public function getByStatus(string $status): Collection
    {
        return $this->contactMessageRepository->getByStatus($status);
    }

    /**
     * Mark message as read.
     */
    public function markAsRead(int $id): bool
    {
        $message = $this->contactMessageRepository->findOrFail($id);
        return $message->markAsRead();
    }

    /**
     * Mark message as archived.
     */
    public function markAsArchived(int $id): bool
    {
        $message = $this->contactMessageRepository->findOrFail($id);
        return $message->markAsArchived();
    }

    /**
     * Update message status.
     */
    public function updateStatus(int $id, string $status): ContactMessage
    {
        return $this->contactMessageRepository->update($id, ['status' => $status]);
    }

    /**
     * Bulk update status for multiple messages.
     */
    public function bulkUpdateStatus(array $ids, string $status): bool
    {
        return $this->contactMessageRepository->bulkUpdateStatus($ids, $status);
    }

    /**
     * Bulk delete multiple messages.
     */
    public function bulkDelete(array $ids): bool
    {
        return $this->contactMessageRepository->bulkDelete($ids);
    }

    /**
     * Get pending messages count.
     */
    public function getPendingCount(): int
    {
        return $this->contactMessageRepository->getPendingCount();
    }

    /**
     * Get read messages count.
     */
    public function getReadCount(): int
    {
        return $this->contactMessageRepository->getReadCount();
    }

    /**
     * Get archived messages count.
     */
    public function getArchivedCount(): int
    {
        return $this->contactMessageRepository->getArchivedCount();
    }

    /**
     * Get total messages count.
     */
    public function getTotalCount(): int
    {
        return $this->contactMessageRepository->count();
    }
}
