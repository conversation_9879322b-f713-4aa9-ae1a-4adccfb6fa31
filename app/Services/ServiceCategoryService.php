<?php

namespace App\Services;

use App\Models\ServiceCategory;
use App\Repositories\Contracts\ServiceCategoryRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Validation\ValidationException;

class ServiceCategoryService
{
    public function __construct(
        private ServiceCategoryRepositoryInterface $serviceCategoryRepository
    ) {}

    /**
     * Create a new service category.
     */
    public function create(array $data): ServiceCategory
    {
        // Validate unique name
        if ($this->serviceCategoryRepository->findByName($data['name'])) {
            throw ValidationException::withMessages([
                'name' => ['The category name has already been taken.']
            ]);
        }

        return $this->serviceCategoryRepository->create($data);
    }

    /**
     * Update service category.
     */
    public function update(int $categoryId, array $data): ServiceCategory
    {
        // Validate unique name if changed
        $category = $this->serviceCategoryRepository->findOrFail($categoryId);
        
        if (isset($data['name']) && $data['name'] !== $category->name) {
            if ($this->serviceCategoryRepository->findByName($data['name'])) {
                throw ValidationException::withMessages([
                    'name' => ['The category name has already been taken.']
                ]);
            }
        }

        return $this->serviceCategoryRepository->update($categoryId, $data);
    }

    /**
     * Get all categories with services count.
     */
    public function getAllWithServicesCount(): Collection
    {
        return $this->serviceCategoryRepository->getWithServicesCount();
    }

    /**
     * Get categories with active services only.
     */
    public function getWithActiveServices(): Collection
    {
        return $this->serviceCategoryRepository->getWithActiveServices();
    }

    /**
     * Get category with its services.
     */
    public function getWithServices(int $categoryId): ?ServiceCategory
    {
        return $this->serviceCategoryRepository->getWithServices($categoryId);
    }

    /**
     * Get popular categories.
     */
    public function getPopular(int $limit = 10): Collection
    {
        return $this->serviceCategoryRepository->getPopular($limit);
    }

    /**
     * Delete category if it has no services.
     */
    public function delete(int $categoryId): bool
    {
        $category = $this->serviceCategoryRepository->getWithServices($categoryId);
        
        if (!$category) {
            return false;
        }

        // Check if category has any services
        if ($category->providerServices->isNotEmpty()) {
            throw ValidationException::withMessages([
                'category' => ['Cannot delete category that has services associated with it.']
            ]);
        }

        return $this->serviceCategoryRepository->delete($categoryId);
    }

    /**
     * Get category statistics.
     */
    public function getStatistics(int $categoryId): array
    {
        $category = $this->serviceCategoryRepository->getWithServices($categoryId);
        
        if (!$category) {
            return [];
        }

        $totalServices = $category->providerServices->count();
        $activeServices = $category->providerServices->where('is_active', true)->count();
        $averagePrice = $category->providerServices->where('is_active', true)->avg('price');
        $averageRating = $category->providerServices->where('is_active', true)->avg('rating');
        $totalProviders = $category->providerServices->pluck('service_provider_id')->unique()->count();

        return [
            'total_services' => $totalServices,
            'active_services' => $activeServices,
            'average_price' => round($averagePrice, 2),
            'average_rating' => round($averageRating, 2),
            'total_providers' => $totalProviders,
        ];
    }

    /**
     * Search categories by name or description.
     */
    public function search(string $query): Collection
    {
        return $this->serviceCategoryRepository->findBy([
            'name' => $query
        ])->merge(
            $this->serviceCategoryRepository->all()->filter(function ($category) use ($query) {
                return stripos($category->description, $query) !== false;
            })
        )->unique('id');
    }
}
