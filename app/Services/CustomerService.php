<?php

namespace App\Services;

use App\Models\Customer;
use App\Repositories\Contracts\CustomerRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class CustomerService
{
    public function __construct(
        private CustomerRepositoryInterface $customerRepository
    ) {
    }

    /**
     * Create a new customer.
     */
    public function create(array $data): Customer
    {
        // Validate unique email and mobile
        if ($this->customerRepository->findByEmail($data['email'])) {
            throw ValidationException::withMessages([
                'email' => ['The email has already been taken.']
            ]);
        }

        if (isset($data['mobile']) && $this->customerRepository->findByMobile($data['mobile'])) {
            throw ValidationException::withMessages([
                'mobile' => ['The mobile number has already been taken.']
            ]);
        }

        // Hash password
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        return $this->customerRepository->create($data);
    }

    /**
     * Update customer profile.
     */
    public function updateProfile(int $customerId, array $data): Customer
    {
        // Remove password from update data if not provided
        if (empty($data['password'])) {
            unset($data['password']);
        } else {
            $data['password'] = Hash::make($data['password']);
        }

        // Validate unique email and mobile if changed
        $customer = $this->customerRepository->findOrFail($customerId);

        if (isset($data['email']) && $data['email'] !== $customer->email) {
            if ($this->customerRepository->findByEmail($data['email'])) {
                throw ValidationException::withMessages([
                    'email' => ['The email has already been taken.']
                ]);
            }
        }

        if (isset($data['mobile']) && $data['mobile'] !== $customer->mobile) {
            if ($this->customerRepository->findByMobile($data['mobile'])) {
                throw ValidationException::withMessages([
                    'mobile' => ['The mobile number has already been taken.']
                ]);
            }
        }

        // Validate area belongs to city if both are provided
        if (isset($data['city_id']) && isset($data['area_id'])) {
            $area = \App\Models\Area::find($data['area_id']);
            if ($area && $area->city_id !== (int) $data['city_id']) {
                throw ValidationException::withMessages([
                    'area_id' => ['The selected area does not belong to the selected city.']
                ]);
            }
        }

        return $this->customerRepository->update($customerId, $data);
    }

    /**
     * Get customers near a location.
     */
    public function getNearbyCustomers(float $latitude, float $longitude, float $radiusKm = 10): Collection
    {
        return $this->customerRepository->getWithinRadius($latitude, $longitude, $radiusKm);
    }

    /**
     * Add service to customer's favourites.
     */
    public function addToFavourites(int $customerId, int $serviceId): bool
    {
        return $this->customerRepository->addToFavourites($customerId, $serviceId);
    }

    /**
     * Remove service from customer's favourites.
     */
    public function removeFromFavourites(int $customerId, int $serviceId): bool
    {
        return $this->customerRepository->removeFromFavourites($customerId, $serviceId);
    }

    /**
     * Toggle service in customer's favourites.
     */
    public function toggleFavourite(int $customerId, int $serviceId): bool
    {
        if ($this->customerRepository->isFavourite($customerId, $serviceId)) {
            return $this->customerRepository->removeFromFavourites($customerId, $serviceId);
        }

        return $this->customerRepository->addToFavourites($customerId, $serviceId);
    }

    /**
     * Get customer's favourite services.
     */
    public function getFavouriteServices(int $customerId): Collection
    {
        return $this->customerRepository->getFavouriteServices($customerId);
    }

    /**
     * Authenticate customer.
     */
    public function authenticate(string $email, string $password): ?Customer
    {
        $customer = $this->customerRepository->findByEmail($email);

        if ($customer && Hash::check($password, $customer->password)) {
            return $customer;
        }

        return null;
    }

    /**
     * Update customer location.
     */
    public function updateLocation(int $customerId, float $latitude, float $longitude, ?string $city = null, ?string $area = null): Customer
    {
        $data = [
            'latitude' => $latitude,
            'longitude' => $longitude,
        ];

        if ($city) {
            $data['city'] = $city;
        }

        if ($area) {
            $data['area'] = $area;
        }

        return $this->customerRepository->update($customerId, $data);
    }

    /**
     * Update customer profile image.
     */
    public function updateProfileImage(int $customerId, UploadedFile $image): Customer
    {
        $customer = $this->customerRepository->findOrFail($customerId);

        // Delete old profile image if exists
        if ($customer->profile_image) {
            Storage::disk('public')->delete($customer->profile_image);
        }

        // Store new image
        $imagePath = $image->store('profile-images', 'public');

        // Update customer record
        return $this->customerRepository->update($customerId, [
            'profile_image' => $imagePath
        ]);
    }

    /**
     * Delete customer profile (soft delete).
     */
    public function deleteProfile(int $customerId): bool
    {
        $customer = $this->customerRepository->findOrFail($customerId);

        // Delete profile image if exists
        if ($customer->profile_image) {
            Storage::disk('public')->delete($customer->profile_image);
        }

        // Revoke all tokens for this customer
        $customer->tokens()->delete();

        // Soft delete the customer
        return $this->customerRepository->delete($customerId);
    }
}
