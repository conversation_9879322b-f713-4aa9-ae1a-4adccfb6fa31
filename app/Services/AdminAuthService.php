<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

/**
 * Admin Authentication Service
 * 
 * Handles business logic for admin authentication including
 * login, logout, and user validation.
 */
class AdminAuthService
{
    /**
     * Attempt to authenticate an admin user
     *
     * @param string $email
     * @param string $password
     * @param bool $remember
     * @return array|false Returns user data on success, false on failure
     * @throws ValidationException
     */
    public function login(string $email, string $password, bool $remember = false): array|false
    {
        // Find user by email
        $user = User::where('email', $email)->first();

        // Check if user exists
        if (!$user) {
            return false;
        }

        // Check if user is active
        if (!$user->is_active) {
            throw ValidationException::withMessages([
                'email' => ['Your account is inactive. Please contact support.']
            ]);
        }

        // Verify password
        if (!Hash::check($password, $user->password)) {
            return false;
        }

        // Attempt authentication with admin guard
        $credentials = [
            'email' => $email,
            'password' => $password,
            'is_active' => true, // Ensure only active users can login
        ];

        if (Auth::guard('admin')->attempt($credentials, $remember)) {
            // Regenerate session to prevent session fixation
            request()->session()->regenerate();

            return [
                'user' => $user->toArray(),
                'message' => 'Login successful'
            ];
        }

        return false;
    }

    /**
     * Log out the admin user
     *
     * @return bool
     */
    public function logout(): bool
    {
        try {
            // Log out from admin guard
            Auth::guard('admin')->logout();

            // Invalidate the session
            request()->session()->invalidate();

            // Regenerate CSRF token
            request()->session()->regenerateToken();

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get the currently authenticated admin user
     *
     * @return User|null
     */
    public function getAuthenticatedUser(): ?User
    {
        return Auth::guard('admin')->user();
    }

    /**
     * Check if admin user is authenticated
     *
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        return Auth::guard('admin')->check();
    }

    /**
     * Check if the authenticated user is active
     *
     * @return bool
     */
    public function isUserActive(): bool
    {
        $user = $this->getAuthenticatedUser();
        
        return $user && $user->is_active;
    }

    /**
     * Validate user credentials without logging in
     *
     * @param string $email
     * @param string $password
     * @return bool
     */
    public function validateCredentials(string $email, string $password): bool
    {
        $user = User::where('email', $email)->where('is_active', true)->first();

        if (!$user) {
            return false;
        }

        return Hash::check($password, $user->password);
    }

    /**
     * Force logout if user becomes inactive
     *
     * @return void
     */
    public function checkUserStatus(): void
    {
        if ($this->isAuthenticated() && !$this->isUserActive()) {
            $this->logout();
        }
    }
}
