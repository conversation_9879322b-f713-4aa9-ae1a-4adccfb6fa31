<?php

namespace App\Services;

use App\Models\ProviderService;
use App\Repositories\Contracts\ProviderServiceRepositoryInterface;
use App\Repositories\Contracts\ServiceProviderRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class ProviderServiceService
{
    public function __construct(
        private ProviderServiceRepositoryInterface $providerServiceRepository,
        private ServiceProviderRepositoryInterface $serviceProviderRepository,
        private ServiceProviderService $serviceProviderService
    ) {}

    /**
     * Create a new provider service.
     */
    public function create(array $data): ProviderService
    {
        $service = $this->providerServiceRepository->create($data);
        
        // Update provider rating after adding new service
        $this->serviceProviderService->updateProviderRating($service->service_provider_id);
        
        return $service;
    }

    /**
     * Update provider service.
     */
    public function update(int $serviceId, array $data): ProviderService
    {
        $service = $this->providerServiceRepository->update($serviceId, $data);
        
        // Update provider rating after updating service
        $this->serviceProviderService->updateProviderRating($service->service_provider_id);
        
        return $service;
    }

    /**
     * Delete provider service.
     */
    public function delete(int $serviceId): bool
    {
        $service = $this->providerServiceRepository->findOrFail($serviceId);
        $providerId = $service->service_provider_id;
        
        $deleted = $this->providerServiceRepository->delete($serviceId);
        
        if ($deleted) {
            // Update provider rating after deleting service
            $this->serviceProviderService->updateProviderRating($providerId);
        }
        
        return $deleted;
    }

    /**
     * Get service with full details.
     */
    public function getWithDetails(int $serviceId): ?ProviderService
    {
        return $this->providerServiceRepository->getWithDetails($serviceId);
    }

    /**
     * Search services with filters.
     */
    public function search(array $filters): Collection
    {
        $services = collect();

        // Apply filters
        if (!empty($filters['category_id'])) {
            $services = $this->providerServiceRepository->getByCategory($filters['category_id']);
        } else {
            $services = $this->providerServiceRepository->getActive();
        }

        if (!empty($filters['provider_id'])) {
            $services = $this->providerServiceRepository->getByProvider($filters['provider_id']);
        }

        if (!empty($filters['min_price']) && !empty($filters['max_price'])) {
            $services = $this->providerServiceRepository->getByPriceRange($filters['min_price'], $filters['max_price']);
        }

        if (!empty($filters['min_rating'])) {
            $services = $this->providerServiceRepository->getWithMinRating($filters['min_rating']);
        }

        if (!empty($filters['max_duration'])) {
            $services = $this->providerServiceRepository->getWithMaxDuration($filters['max_duration']);
        }

        if (!empty($filters['query'])) {
            $services = $this->providerServiceRepository->search($filters['query']);
        }

        return $services;
    }

    /**
     * Get popular services.
     */
    public function getPopular(int $limit = 10): Collection
    {
        return $this->providerServiceRepository->getPopular($limit);
    }

    /**
     * Get featured services.
     */
    public function getFeatured(): Collection
    {
        return $this->providerServiceRepository->getFeatured();
    }

    /**
     * Toggle service active status.
     */
    public function toggleStatus(int $serviceId): ProviderService
    {
        $service = $this->providerServiceRepository->findOrFail($serviceId);
        
        $updatedService = $this->providerServiceRepository->update($serviceId, [
            'is_active' => !$service->is_active
        ]);

        // Update provider rating after status change
        $this->serviceProviderService->updateProviderRating($service->service_provider_id);
        
        return $updatedService;
    }

    /**
     * Update service rating.
     */
    public function updateRating(int $serviceId, float $rating): bool
    {
        $service = $this->providerServiceRepository->findOrFail($serviceId);
        
        $updated = $this->providerServiceRepository->updateRating($serviceId, $rating);
        
        if ($updated) {
            // Update provider rating after service rating change
            $this->serviceProviderService->updateProviderRating($service->service_provider_id);
        }
        
        return $updated;
    }

    /**
     * Get services by location (through provider location).
     */
    public function getByLocation(string $city, ?string $area = null): Collection
    {
        if ($area) {
            $providers = $this->serviceProviderRepository->getByArea($area);
        } else {
            $providers = $this->serviceProviderRepository->getByCity($city);
        }

        $providerIds = $providers->pluck('id');
        
        return $this->providerServiceRepository->findBy([
            'service_provider_id' => $providerIds->toArray(),
            'is_active' => true
        ]);
    }

    /**
     * Get service recommendations for a customer.
     */
    public function getRecommendations(int $customerId, int $limit = 10): Collection
    {
        // This is a simple recommendation based on popular services
        // In a real application, you might implement more sophisticated algorithms
        return $this->getPopular($limit);
    }
}
