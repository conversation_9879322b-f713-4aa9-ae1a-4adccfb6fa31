<?php

namespace App\Services;

use App\Models\City;
use App\Repositories\Contracts\CityRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Validation\ValidationException;

class CityService
{
    public function __construct(
        private CityRepositoryInterface $cityRepository
    ) {
    }

    /**
     * Create a new city.
     */
    public function create(array $data): City
    {
        // Validate unique name for each locale
        foreach ($data as $locale => $translation) {
            if (isset($translation['name'])) {
                $existingCity = City::whereTranslation('name', $translation['name'], $locale)->first();
                if ($existingCity) {
                    throw ValidationException::withMessages([
                        "{$locale}.name" => ["The {$locale} name has already been taken."]
                    ]);
                }
            }
        }

        // Create city
        $city = $this->cityRepository->create([]);

        // Add translations
        foreach ($data as $locale => $translation) {
            if (isset($translation['name'])) {
                $city->translateOrNew($locale)->name = $translation['name'];
            }
        }

        $city->save();
        return $city->fresh();
    }

    /**
     * Update a city.
     */
    public function update(int $cityId, array $data): City
    {
        $city = $this->cityRepository->findOrFail($cityId);

        // Validate unique name for each locale (excluding current city)
        foreach ($data as $locale => $translation) {
            if (isset($translation['name'])) {
                $existingCity = City::whereTranslation('name', $translation['name'], $locale)
                    ->where('id', '!=', $cityId)
                    ->first();
                if ($existingCity) {
                    throw ValidationException::withMessages([
                        "{$locale}.name" => ["The {$locale} name has already been taken."]
                    ]);
                }
            }
        }

        // Update translations
        foreach ($data as $locale => $translation) {
            if (isset($translation['name'])) {
                $city->translateOrNew($locale)->name = $translation['name'];
            }
        }

        $city->save();
        return $city->fresh();
    }

    /**
     * Delete a city.
     */
    public function delete(int $cityId): bool
    {
        return $this->cityRepository->delete($cityId);
    }

    /**
     * Get city by ID.
     */
    public function getById(int $cityId): ?City
    {
        return $this->cityRepository->find($cityId);
    }

    /**
     * Get city by ID or fail.
     */
    public function getByIdOrFail(int $cityId): City
    {
        return $this->cityRepository->findOrFail($cityId);
    }

    /**
     * Get all cities.
     */
    public function getAllCities(): Collection
    {
        return $this->cityRepository->all();
    }

    /**
     * Get cities with pagination and filters.
     */
    public function getCitiesWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->cityRepository->getCitiesWithFilters($filters, $perPage);
    }

    /**
     * Get cities statistics.
     */
    public function getCitiesStatistics(): array
    {
        return $this->cityRepository->getStatistics();
    }

    /**
     * Activate city.
     */
    public function activate(int $cityId): bool
    {
        $city = $this->cityRepository->findOrFail($cityId);
        // Cities don't have active status, but we can restore if soft deleted
        if ($city->trashed()) {
            return $this->cityRepository->restore($cityId);
        }
        return true;
    }

    /**
     * Deactivate city (soft delete).
     */
    public function deactivate(int $cityId): bool
    {
        return $this->cityRepository->delete($cityId);
    }

    /**
     * Restore soft deleted city.
     */
    public function restore(int $cityId): bool
    {
        return $this->cityRepository->restore($cityId);
    }

    /**
     * Force delete city permanently.
     */
    public function forceDelete(int $cityId): bool
    {
        return $this->cityRepository->forceDelete($cityId);
    }

    /**
     * Get trashed cities.
     */
    public function getTrashedCities(): Collection
    {
        return $this->cityRepository->getTrashed();
    }

    /**
     * Bulk delete cities.
     */
    public function bulkDelete(array $cityIds): bool
    {
        return $this->cityRepository->bulkDelete($cityIds);
    }

    /**
     * Bulk restore cities.
     */
    public function bulkRestore(array $cityIds): bool
    {
        return $this->cityRepository->bulkRestore($cityIds);
    }

    /**
     * Search cities by name.
     */
    public function searchByName(string $name): Collection
    {
        return $this->cityRepository->searchByName($name);
    }

    /**
     * Get cities with areas.
     */
    public function getCitiesWithAreas(): Collection
    {
        return $this->cityRepository->getWithAreas();
    }

    /**
     * Get cities with areas count.
     */
    public function getCitiesWithAreasCount(): Collection
    {
        return $this->cityRepository->getWithAreasCount();
    }

    /**
     * Get cities with customers count.
     */
    public function getCitiesWithCustomersCount(): Collection
    {
        return $this->cityRepository->getWithCustomersCount();
    }

    /**
     * Get cities with service providers count.
     */
    public function getCitiesWithServiceProvidersCount(): Collection
    {
        return $this->cityRepository->getWithServiceProvidersCount();
    }
}
