<?php

namespace App\Services\Api;

use App\Models\ServiceProvider;
use App\Repositories\ServiceProviderRepositoryInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Exception;

/**
 * Provider Profile API Service
 * 
 * Handles business logic for provider profile management via API endpoints.
 * Includes translation handling, file uploads, and data validation.
 */
class ProviderProfileApiService
{
    public function __construct(
        private ServiceProviderRepositoryInterface $serviceProviderRepository
    ) {
    }

    /**
     * Update provider basic information with translations and file uploads.
     *
     * @param ServiceProvider $provider
     * @param array $data
     * @param UploadedFile|null $logoFile
     * @return ServiceProvider
     * @throws Exception
     */
    public function updateBasicInfo(ServiceProvider $provider, array $data, ?UploadedFile $logoFile = null): ServiceProvider
    {
        try {
            DB::beginTransaction();

            // Handle logo upload if provided
            if ($logoFile) {
                $data['logo'] = $this->handleLogoUpload($provider, $logoFile);
            }

            // Extract translations
            $translations = $data['translations'] ?? [];
            unset($data['translations']);

            // Update the provider basic information
            $provider = $this->serviceProviderRepository->update($provider->id, $data);

            // Update translations
            $this->updateTranslations($provider, $translations);

            // Reload the provider with fresh data
            $provider->refresh();
            $provider->load(['city', 'area']);

            DB::commit();

            return $provider;

        } catch (Exception $e) {
            DB::rollBack();
            
            // Clean up uploaded file if transaction failed
            if (isset($data['logo']) && $data['logo']) {
                $this->deleteLogoFile($data['logo']);
            }
            
            throw $e;
        }
    }

    /**
     * Handle logo file upload.
     *
     * @param ServiceProvider $provider
     * @param UploadedFile $logoFile
     * @return string
     * @throws Exception
     */
    private function handleLogoUpload(ServiceProvider $provider, UploadedFile $logoFile): string
    {
        try {
            // Delete old logo if exists
            if ($provider->logo && Storage::disk('public')->exists($provider->logo)) {
                Storage::disk('public')->delete($provider->logo);
            }

            // Generate unique filename
            $filename = 'provider_' . $provider->id . '_' . time() . '.' . $logoFile->getClientOriginalExtension();
            
            // Store new logo
            $logoPath = $logoFile->storeAs('providers/logos', $filename, 'public');

            if (!$logoPath) {
                throw new Exception('Failed to upload logo file');
            }

            return $logoPath;

        } catch (Exception $e) {
            throw new Exception('Logo upload failed: ' . $e->getMessage());
        }
    }

    /**
     * Update provider translations.
     *
     * @param ServiceProvider $provider
     * @param array $translations
     * @return void
     */
    private function updateTranslations(ServiceProvider $provider, array $translations): void
    {
        foreach ($translations as $locale => $translation) {
            $providerTranslation = $provider->translateOrNew($locale);
            $providerTranslation->name = $translation['name'];
            $providerTranslation->description = $translation['description'] ?? null;
        }

        $provider->save();
    }

    /**
     * Delete logo file from storage.
     *
     * @param string $logoPath
     * @return void
     */
    private function deleteLogoFile(string $logoPath): void
    {
        try {
            if (Storage::disk('public')->exists($logoPath)) {
                Storage::disk('public')->delete($logoPath);
            }
        } catch (Exception $e) {
            // Log error but don't throw exception for cleanup operations
            \Log::warning('Failed to delete logo file: ' . $e->getMessage());
        }
    }

    /**
     * Get provider profile data formatted for API response.
     *
     * @param ServiceProvider $provider
     * @return array
     */
    public function getProviderProfileData(ServiceProvider $provider): array
    {
        // Load relationships
        $provider->load(['city', 'area']);

        return [
            'id' => $provider->id,
            'name' => [
                'ar' => $provider->translate('ar')?->name,
                'en' => $provider->translate('en')?->name,
            ],
            'description' => [
                'ar' => $provider->translate('ar')?->description,
                'en' => $provider->translate('en')?->description,
            ],
            'email' => $provider->email,
            'logo' => $provider->logo ? asset('storage/' . $provider->logo) : null,
            'rating' => $provider->rating,
            'city' => [
                'id' => $provider->city?->id,
                'name' => $provider->city?->name,
            ],
            'area' => [
                'id' => $provider->area?->id,
                'name' => $provider->area?->name,
            ],
            'latitude' => $provider->latitude,
            'longitude' => $provider->longitude,
            'is_active' => $provider->is_active,
            'created_at' => $provider->created_at,
            'updated_at' => $provider->updated_at,
        ];
    }

    /**
     * Validate that area belongs to the selected city.
     *
     * @param int $cityId
     * @param int $areaId
     * @return bool
     */
    public function validateAreaBelongsToCity(int $cityId, int $areaId): bool
    {
        return DB::table('areas')
            ->where('id', $areaId)
            ->where('city_id', $cityId)
            ->exists();
    }

    /**
     * Get provider statistics for profile overview.
     *
     * @param ServiceProvider $provider
     * @return array
     */
    public function getProviderStatistics(ServiceProvider $provider): array
    {
        return [
            'total_services' => $provider->services()->count(),
            'active_services' => $provider->activeServices()->count(),
            'total_reviews' => 0, // Placeholder for future review system
            'average_rating' => $provider->rating,
            'profile_completion' => $this->calculateProfileCompletion($provider),
        ];
    }

    /**
     * Calculate profile completion percentage.
     *
     * @param ServiceProvider $provider
     * @return int
     */
    private function calculateProfileCompletion(ServiceProvider $provider): int
    {
        $fields = [
            'email' => !empty($provider->email),
            'logo' => !empty($provider->logo),
            'city_id' => !empty($provider->city_id),
            'area_id' => !empty($provider->area_id),
            'latitude' => !empty($provider->latitude),
            'longitude' => !empty($provider->longitude),
            'ar_name' => !empty($provider->translate('ar')?->name),
            'en_name' => !empty($provider->translate('en')?->name),
            'ar_description' => !empty($provider->translate('ar')?->description),
            'en_description' => !empty($provider->translate('en')?->description),
        ];

        $completedFields = array_filter($fields);
        $totalFields = count($fields);
        $completedCount = count($completedFields);

        return $totalFields > 0 ? round(($completedCount / $totalFields) * 100) : 0;
    }
}
