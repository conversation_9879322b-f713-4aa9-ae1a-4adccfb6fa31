<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class UserService
{
    public function __construct(
        private UserRepositoryInterface $userRepository
    ) {
    }

    /**
     * Create a new user.
     */
    public function create(array $data): User
    {
        // Validate unique email and mobile
        if ($this->userRepository->findByEmail($data['email'])) {
            throw ValidationException::withMessages([
                'email' => ['The email has already been taken.']
            ]);
        }

        if (isset($data['mobile']) && $this->userRepository->findByMobile($data['mobile'])) {
            throw ValidationException::withMessages([
                'mobile' => ['The mobile number has already been taken.']
            ]);
        }

        // Hash password
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Set default active status if not provided
        if (!isset($data['is_active'])) {
            $data['is_active'] = true;
        }

        /** @var User $user */
        $user = $this->userRepository->create($data);
        return $user;
    }

    /**
     * Update user.
     */
    public function update(int $userId, array $data): User
    {
        /** @var User $user */
        $user = $this->userRepository->findOrFail($userId);

        // Validate unique email (excluding current user)
        if (isset($data['email']) && $data['email'] !== $user->email) {
            $existingUser = $this->userRepository->findByEmail($data['email']);
            if ($existingUser && $existingUser->id !== $userId) {
                throw ValidationException::withMessages([
                    'email' => ['The email has already been taken.']
                ]);
            }
        }

        // Validate unique mobile (excluding current user)
        if (isset($data['mobile']) && $data['mobile'] !== $user->mobile) {
            $existingUser = $this->userRepository->findByMobile($data['mobile']);
            if ($existingUser && $existingUser->id !== $userId) {
                throw ValidationException::withMessages([
                    'mobile' => ['The mobile number has already been taken.']
                ]);
            }
        }

        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        /** @var User $updatedUser */
        $updatedUser = $this->userRepository->update($userId, $data);
        return $updatedUser;
    }

    /**
     * Delete user (soft delete).
     */
    public function delete(int $userId): bool
    {
        return $this->userRepository->delete($userId);
    }

    /**
     * Get user by ID.
     */
    public function getById(int $userId): ?User
    {
        return $this->userRepository->find($userId);
    }

    /**
     * Get user by ID or fail.
     */
    public function getByIdOrFail(int $userId): User
    {
        /** @var User $user */
        $user = $this->userRepository->findOrFail($userId);
        return $user;
    }

    /**
     * Get all users.
     */
    public function getAllUsers(): Collection
    {
        return $this->userRepository->all();
    }

    /**
     * Get users with pagination and filters.
     */
    public function getUsersWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->userRepository->getUsersWithFilters($filters, $perPage);
    }

    /**
     * Search users.
     */
    public function searchUsers(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->userRepository->searchUsers($query, $perPage);
    }

    /**
     * Get active users.
     */
    public function getActiveUsers(): Collection
    {
        return $this->userRepository->getActiveUsers();
    }

    /**
     * Get inactive users.
     */
    public function getInactiveUsers(): Collection
    {
        return $this->userRepository->getInactiveUsers();
    }

    /**
     * Activate user.
     */
    public function activateUser(int $userId): bool
    {
        return $this->userRepository->activate($userId);
    }

    /**
     * Deactivate user.
     */
    public function deactivateUser(int $userId): bool
    {
        return $this->userRepository->deactivate($userId);
    }

    /**
     * Get users statistics.
     */
    public function getUsersStatistics(): array
    {
        return $this->userRepository->getCountByStatus();
    }

    /**
     * Find user by email.
     */
    public function findByEmail(string $email): ?User
    {
        return $this->userRepository->findByEmail($email);
    }

    /**
     * Find user by mobile.
     */
    public function findByMobile(string $mobile): ?User
    {
        return $this->userRepository->findByMobile($mobile);
    }

    /**
     * Check if user exists.
     */
    public function userExists(int $userId): bool
    {
        return $this->userRepository->exists($userId);
    }

    /**
     * Authenticate user.
     */
    public function authenticate(string $email, string $password): ?User
    {
        $user = $this->userRepository->findByEmail($email);

        if ($user && Hash::check($password, $user->password)) {
            // Check if user is active
            if (!$user->is_active) {
                throw ValidationException::withMessages([
                    'email' => ['Your account is inactive. Please contact administrator.']
                ]);
            }

            return $user;
        }

        return null;
    }
}
