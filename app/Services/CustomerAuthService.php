<?php

namespace App\Services;

use App\Models\Customer;
use App\Repositories\Contracts\CustomerRepositoryInterface;
use Illuminate\Support\Facades\Hash;

class CustomerAuthService
{
    public function __construct(
        private CustomerRepositoryInterface $customerRepository
    ) {
    }

    /**
     * Attempt to authenticate a customer with email and password.
     *
     * @param string $email
     * @param string $password
     * @return array|false|string Returns array with token and customer data on success, false on invalid credentials, 'inactive' if account is inactive
     */
    public function login(string $email, string $password): array|false|string
    {
        // Find customer by email
        $customer = $this->customerRepository->findByEmail($email);

        // Check if customer exists and password is correct
        if (!$customer || !Hash::check($password, $customer->password)) {
            return false;
        }

        // Check if customer account is active
        if (!$customer->is_active) {
            return 'inactive';
        }

        // Generate Sanctum token
        $token = $customer->createToken('customer-auth-token')->plainTextToken;

        return [
            'token' => $token,
            'customer' => $customer
        ];
    }

    /**
     * Validate customer credentials without generating a token.
     *
     * @param string $email
     * @param string $password
     * @return Customer|null
     */
    public function validateCredentials(string $email, string $password): ?Customer
    {
        $customer = $this->customerRepository->findByEmail($email);

        if ($customer && Hash::check($password, $customer->password)) {
            return $customer;
        }

        return null;
    }

    /**
     * Register a new customer.
     *
     * @param array $data
     * @return array Returns array with token and customer data
     */
    public function register(array $data): array
    {
        // Remove password_confirmation and approve_policy from data before creating customer
        unset($data['password_confirmation'], $data['approve_policy']);

        // Hash the password
        $data['password'] = Hash::make($data['password']);

        // Create the customer using the repository
        $customer = $this->customerRepository->create($data);

        // Generate Sanctum token
        $token = $customer->createToken('customer-auth-token')->plainTextToken;

        return [
            'token' => $token,
            'customer' => $customer
        ];
    }

    /**
     * Revoke all tokens for a customer (logout).
     *
     * @param Customer $customer
     * @return bool
     */
    public function logout(Customer $customer): bool
    {
        $customer->tokens()->delete();
        return true;
    }
}
