<?php

namespace App\Services;

use App\Models\Area;
use App\Repositories\Contracts\AreaRepositoryInterface;
use App\Repositories\Contracts\CityRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Validation\ValidationException;

class AreaService
{
    public function __construct(
        private AreaRepositoryInterface $areaRepository,
        private CityRepositoryInterface $cityRepository
    ) {
    }

    /**
     * Create a new area.
     */
    public function create(array $data): Area
    {
        // Validate city exists
        if (!$this->cityRepository->exists($data['city_id'])) {
            throw ValidationException::withMessages([
                'city_id' => ['The selected city does not exist.']
            ]);
        }

        // Extract city_id and translations
        $cityId = $data['city_id'];
        $translations = collect($data)->except('city_id')->toArray();

        // Validate unique name for each locale within the city
        foreach ($translations as $locale => $translation) {
            if (isset($translation['name'])) {
                $existingArea = Area::whereTranslation('name', $translation['name'], $locale)
                    ->where('city_id', $cityId)
                    ->first();
                if ($existingArea) {
                    throw ValidationException::withMessages([
                        "{$locale}.name" => ["The {$locale} name has already been taken in this city."]
                    ]);
                }
            }
        }

        // Create area
        $area = $this->areaRepository->create([
            'city_id' => $cityId
        ]);

        // Add translations
        foreach ($translations as $locale => $translation) {
            if (isset($translation['name'])) {
                $area->translateOrNew($locale)->name = $translation['name'];
            }
        }

        $area->save();
        return $area->fresh(['city']);
    }

    /**
     * Update an area.
     */
    public function update(int $areaId, array $data): Area
    {
        $area = $this->areaRepository->findOrFail($areaId);

        // Validate city exists if being updated
        if (isset($data['city_id']) && !$this->cityRepository->exists($data['city_id'])) {
            throw ValidationException::withMessages([
                'city_id' => ['The selected city does not exist.']
            ]);
        }

        $cityId = $data['city_id'] ?? $area->city_id;

        // Validate unique name for each locale within the city (excluding current area)
        if (isset($data['translations'])) {
            foreach ($data['translations'] as $locale => $translation) {
                if (isset($translation['name'])) {
                    $existingArea = Area::whereTranslation('name', $translation['name'], $locale)
                        ->where('city_id', $cityId)
                        ->where('id', '!=', $areaId)
                        ->first();
                    if ($existingArea) {
                        throw ValidationException::withMessages([
                            "translations.{$locale}.name" => ["The {$locale} name has already been taken in this city."]
                        ]);
                    }
                }
            }
        }

        // Update city if provided
        if (isset($data['city_id'])) {
            $area->city_id = $data['city_id'];
        }

        // Update translations
        if (isset($data['translations'])) {
            foreach ($data['translations'] as $locale => $translation) {
                if (isset($translation['name'])) {
                    $area->translateOrNew($locale)->name = $translation['name'];
                }
            }
        }

        $area->save();
        return $area->fresh(['city']);
    }

    /**
     * Delete an area.
     */
    public function delete(int $areaId): bool
    {
        return $this->areaRepository->delete($areaId);
    }

    /**
     * Get area by ID.
     */
    public function getById(int $areaId): ?Area
    {
        return $this->areaRepository->find($areaId);
    }

    /**
     * Get area by ID or fail.
     */
    public function getByIdOrFail(int $areaId): Area
    {
        return $this->areaRepository->findOrFail($areaId);
    }

    /**
     * Get all areas.
     */
    public function getAllAreas(): Collection
    {
        return $this->areaRepository->all();
    }

    /**
     * Get areas with pagination and filters.
     */
    public function getAreasWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->areaRepository->getAreasWithFilters($filters, $perPage);
    }

    /**
     * Get areas statistics.
     */
    public function getAreasStatistics(): array
    {
        return $this->areaRepository->getStatistics();
    }

    /**
     * Restore soft deleted area.
     */
    public function restore(int $areaId): bool
    {
        return $this->areaRepository->restore($areaId);
    }

    /**
     * Force delete area permanently.
     */
    public function forceDelete(int $areaId): bool
    {
        return $this->areaRepository->forceDelete($areaId);
    }

    /**
     * Get trashed areas.
     */
    public function getTrashedAreas(): Collection
    {
        return $this->areaRepository->getTrashed();
    }

    /**
     * Bulk delete areas.
     */
    public function bulkDelete(array $areaIds): bool
    {
        return $this->areaRepository->bulkDelete($areaIds);
    }

    /**
     * Bulk restore areas.
     */
    public function bulkRestore(array $areaIds): bool
    {
        return $this->areaRepository->bulkRestore($areaIds);
    }

    /**
     * Search areas by name.
     */
    public function searchByName(string $name): Collection
    {
        return $this->areaRepository->searchByName($name);
    }

    /**
     * Get areas by city.
     */
    public function getAreasByCity(int $cityId): Collection
    {
        return $this->areaRepository->getByCity($cityId);
    }

    /**
     * Get areas with city.
     */
    public function getAreasWithCity(): Collection
    {
        return $this->areaRepository->getWithCity();
    }

    /**
     * Get areas with customers count.
     */
    public function getAreasWithCustomersCount(): Collection
    {
        return $this->areaRepository->getWithCustomersCount();
    }

    /**
     * Get areas with service providers count.
     */
    public function getAreasWithServiceProvidersCount(): Collection
    {
        return $this->areaRepository->getWithServiceProvidersCount();
    }

    /**
     * Get areas by city with counts.
     */
    public function getAreasByCityWithCounts(int $cityId): Collection
    {
        return $this->areaRepository->getByCityWithCounts($cityId);
    }
}
