<?php

namespace App\Services;

use App\Models\Notification;
use App\Repositories\Contracts\NotificationRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class NotificationService
{
    public function __construct(
        private NotificationRepositoryInterface $notificationRepository
    ) {
    }

    /**
     * Get all notifications for a customer.
     */
    public function getNotificationsForCustomer(int $customerId): Collection
    {
        return $this->notificationRepository->getForCustomer($customerId);
    }

    /**
     * Get unread notifications for a customer.
     */
    public function getUnreadNotificationsForCustomer(int $customerId): Collection
    {
        return $this->notificationRepository->getUnreadForCustomer($customerId);
    }

    /**
     * Get notifications by type for a customer.
     */
    public function getNotificationsByTypeForCustomer(int $customerId, string $type): Collection
    {
        return $this->notificationRepository->getByTypeForCustomer($customerId, $type);
    }

    /**
     * Mark all notifications as read for a customer.
     */
    public function markAllAsReadForCustomer(int $customerId): bool
    {
        return $this->notificationRepository->markAllAsReadForCustomer($customerId);
    }

    /**
     * Clear all notifications for a customer.
     */
    public function clearAllNotificationsForCustomer(int $customerId): bool
    {
        return $this->notificationRepository->clearAllForCustomer($customerId);
    }

    /**
     * Count unread notifications for a customer.
     */
    public function countUnreadForCustomer(int $customerId): int
    {
        return $this->notificationRepository->countUnreadForCustomer($customerId);
    }

    /**
     * Create a new notification for a customer.
     */
    public function createNotificationForCustomer(int $customerId, array $data): Notification
    {
        return $this->notificationRepository->createForCustomer($customerId, $data);
    }

    /**
     * Mark a specific notification as read.
     */
    public function markNotificationAsRead(int $notificationId): bool
    {
        return $this->notificationRepository->markAsRead($notificationId);
    }

    /**
     * Get notification by ID.
     */
    public function getNotificationById(int $id): ?Notification
    {
        return $this->notificationRepository->find($id);
    }
}
