<?php

namespace App\Services;

use App\Repositories\Contracts\MainImageSliderRepositoryInterface;
use App\Repositories\Contracts\ServiceProviderRepositoryInterface;
use App\Repositories\Contracts\ServiceCategoryRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class HomeScreenService
{
    public function __construct(
        private MainImageSliderRepositoryInterface $sliderRepository,
        private ServiceProviderRepositoryInterface $serviceProviderRepository,
        private ServiceCategoryRepositoryInterface $serviceCategoryRepository
    ) {
    }

    /**
     * Get all home screen data.
     */
    public function getHomeScreenData(?float $latitude = null, ?float $longitude = null, ?string $query = null): array
    {
        return [
            'sliders' => $this->getMainSliders(),
            'highest_rating_providers' => $this->getHighestRatingProviders($query),
            'near_providers' => $this->getNearProviders($latitude, $longitude, $query),
            'service_categories' => $this->getServiceCategories($query),
            'upcoming' => null,
        ];
    }

    /**
     * Get main slider slides.
     */
    public function getMainSliders(): Collection
    {
        return $this->sliderRepository->getActiveOrdered();
    }

    /**
     * Get 6 highest rating service providers.
     */
    public function getHighestRatingProviders(?string $query = null): Collection
    {
        $providers = $this->serviceProviderRepository->getActive();

        if ($query) {
            $providers = $this->serviceProviderRepository->search($query)
                ->where('is_active', true);
        }

        return $providers->sortByDesc('rating')->take(6);
    }

    /**
     * Get 6 near service providers based on location.
     */
    public function getNearProviders(?float $latitude = null, ?float $longitude = null, ?string $query = null): Collection
    {
        $providers = collect();

        if ($latitude && $longitude) {
            // Get providers within 50km radius
            $providers = $this->serviceProviderRepository->getWithinRadius($latitude, $longitude, 50)
                ->where('is_active', true);
        } else {
            // If no location provided, get all active providers
            $providers = $this->serviceProviderRepository->getActive();
        }

        if ($query) {
            $providers = $providers->filter(function ($provider) use ($query) {
                return stripos($provider->name, $query) !== false ||
                    stripos($provider->description, $query) !== false;
            });
        }

        return $providers->shuffle()->take(6);
    }

    /**
     * Get all service categories.
     */
    public function getServiceCategories(?string $query = null): Collection
    {
        $categories = $this->serviceCategoryRepository->getWithActiveServices();

        if ($query) {
            $categories = $categories->filter(function ($category) use ($query) {
                return stripos($category->name, $query) !== false ||
                    stripos($category->description, $query) !== false;
            });
        }

        return $categories;
    }
}
