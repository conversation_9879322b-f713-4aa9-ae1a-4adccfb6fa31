<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Customer extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'gender',
        'city_id',
        'area_id',
        'longitude',
        'latitude',
        'email',
        'password',
        'mobile',
        'is_active',
        'profile_image',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'longitude' => 'decimal:8',
        'latitude' => 'decimal:8',
        'is_active' => 'boolean',
    ];

    /**
     * Get the city that owns the customer.
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the area that owns the customer.
     */
    public function area(): BelongsTo
    {
        return $this->belongsTo(Area::class);
    }

    /**
     * Get the favourite services for the customer.
     */
    public function favouriteServices(): BelongsToMany
    {
        return $this->belongsToMany(ProviderService::class, 'favourite_services')
            ->withTimestamps();
    }

    /**
     * Get the favourite services relationship.
     */
    public function favourites(): HasMany
    {
        return $this->hasMany(FavouriteService::class);
    }

    /**
     * Scope a query to only include customers in a specific city.
     */
    public function scopeInCity($query, $cityId)
    {
        return $query->where('city_id', $cityId);
    }

    /**
     * Scope a query to only include customers in a specific area.
     */
    public function scopeInArea($query, $areaId)
    {
        return $query->where('area_id', $areaId);
    }

    /**
     * Scope a query to include customers with city and area.
     */
    public function scopeWithLocation($query)
    {
        return $query->with(['city', 'area']);
    }

    /**
     * Get the customer's full location.
     */
    public function getFullLocationAttribute()
    {
        $location = '';
        if ($this->area) {
            $location .= $this->area->name;
        }
        if ($this->city) {
            $location .= ($location ? ', ' : '') . $this->city->name;
        }
        return $location;
    }
}
