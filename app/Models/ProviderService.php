<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProviderService extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'service_provider_id',
        'service_category_id',
        'image',
        'title',
        'price',
        'rating',
        'duration',
        'description',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'rating' => 'decimal:2',
        'duration' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the service provider that owns the service.
     */
    public function serviceProvider()
    {
        return $this->belongsTo(ServiceProvider::class);
    }

    /**
     * Get the service category.
     */
    public function serviceCategory()
    {
        return $this->belongsTo(ServiceCategory::class);
    }

    /**
     * Get the images for the service.
     */
    public function images()
    {
        return $this->hasMany(ProviderServiceImage::class)->orderBy('sort_order');
    }

    /**
     * Get the customers who favourited this service.
     */
    public function favouritedByCustomers()
    {
        return $this->belongsToMany(Customer::class, 'favourite_services')
                    ->withTimestamps();
    }

    /**
     * Get the favourite records for this service.
     */
    public function favourites()
    {
        return $this->hasMany(FavouriteService::class);
    }

    /**
     * Scope a query to only include active services.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeInCategory($query, $categoryId)
    {
        return $query->where('service_category_id', $categoryId);
    }

    /**
     * Scope a query to filter by price range.
     */
    public function scopePriceBetween($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('price', [$minPrice, $maxPrice]);
    }

    /**
     * Scope a query to filter by minimum rating.
     */
    public function scopeWithMinRating($query, $rating)
    {
        return $query->where('rating', '>=', $rating);
    }

    /**
     * Scope a query to filter by maximum duration.
     */
    public function scopeWithMaxDuration($query, $duration)
    {
        return $query->where('duration', '<=', $duration);
    }

    /**
     * Get formatted duration string.
     */
    public function getFormattedDurationAttribute()
    {
        if ($this->duration < 60) {
            return $this->duration . ' minutes';
        }

        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;

        if ($minutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }

        return $hours . 'h ' . $minutes . 'm';
    }

    /**
     * Get the main image or first image from gallery.
     */
    public function getMainImageAttribute()
    {
        return $this->image ?: $this->images()->first()?->image;
    }
}
