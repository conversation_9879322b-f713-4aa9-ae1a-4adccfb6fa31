<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkingHour extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'service_provider_id',
        'day',
        'open_time',
        'close_time',
        'is_off_day',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'open_time' => 'datetime:H:i',
        'close_time' => 'datetime:H:i',
        'is_off_day' => 'boolean',
    ];

    /**
     * The days of the week.
     */
    public const DAYS = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
    ];

    /**
     * Get the service provider that owns the working hours.
     */
    public function serviceProvider()
    {
        return $this->belongsTo(ServiceProvider::class);
    }

    /**
     * Scope a query to only include working days.
     */
    public function scopeWorkingDays($query)
    {
        return $query->where('is_off_day', false);
    }

    /**
     * Scope a query to only include off days.
     */
    public function scopeOffDays($query)
    {
        return $query->where('is_off_day', true);
    }

    /**
     * Scope a query to filter by specific day.
     */
    public function scopeForDay($query, $day)
    {
        return $query->where('day', strtolower($day));
    }

    /**
     * Check if the provider is open at a specific time on this day.
     */
    public function isOpenAt($time)
    {
        if ($this->is_off_day) {
            return false;
        }

        $checkTime = is_string($time) ? \Carbon\Carbon::createFromFormat('H:i', $time) : $time;
        $openTime = \Carbon\Carbon::createFromFormat('H:i', $this->open_time);
        $closeTime = \Carbon\Carbon::createFromFormat('H:i', $this->close_time);

        return $checkTime->between($openTime, $closeTime);
    }

    /**
     * Get formatted working hours string.
     */
    public function getFormattedHoursAttribute()
    {
        if ($this->is_off_day) {
            return 'Closed';
        }

        return $this->open_time . ' - ' . $this->close_time;
    }
}
