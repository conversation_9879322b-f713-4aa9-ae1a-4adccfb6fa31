<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceCategory extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'icon',
    ];

    /**
     * Get the provider services for the category.
     */
    public function providerServices()
    {
        return $this->hasMany(ProviderService::class);
    }

    /**
     * Get the active provider services for the category.
     */
    public function activeProviderServices()
    {
        return $this->hasMany(ProviderService::class)->where('is_active', true);
    }

    /**
     * Scope a query to only include categories with active services.
     */
    public function scopeWithActiveServices($query)
    {
        return $query->whereHas('providerServices', function ($query) {
            $query->where('is_active', true);
        });
    }

    /**
     * Get the count of active services in this category.
     */
    public function getActiveServicesCountAttribute()
    {
        return $this->activeProviderServices()->count();
    }
}
