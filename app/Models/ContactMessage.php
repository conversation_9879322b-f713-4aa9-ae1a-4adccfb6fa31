<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContactMessage extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'name',
        'email',
        'message',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Status constants.
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_READ = 'read';
    public const STATUS_ARCHIVED = 'archived';

    /**
     * Get all available status options.
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_READ => 'Read',
            self::STATUS_ARCHIVED => 'Archived',
        ];
    }

    /**
     * Scope a query to search by name.
     */
    public function scopeSearchByName($query, string $name)
    {
        return $query->where('name', 'like', '%' . $name . '%');
    }

    /**
     * Scope a query to search by email.
     */
    public function scopeSearchByEmail($query, string $email)
    {
        return $query->where('email', 'like', '%' . $email . '%');
    }

    /**
     * Scope a query to search by title.
     */
    public function scopeSearchByTitle($query, string $title)
    {
        return $query->where('title', 'like', '%' . $title . '%');
    }

    /**
     * Scope a query to search by content (title or message).
     */
    public function scopeSearchByContent($query, string $content)
    {
        return $query->where(function ($q) use ($content) {
            $q->where('title', 'like', '%' . $content . '%')
                ->orWhere('message', 'like', '%' . $content . '%');
        });
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to get pending messages.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope a query to get read messages.
     */
    public function scopeRead($query)
    {
        return $query->where('status', self::STATUS_READ);
    }

    /**
     * Scope a query to get archived messages.
     */
    public function scopeArchived($query)
    {
        return $query->where('status', self::STATUS_ARCHIVED);
    }

    /**
     * Scope a query to order by latest.
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Check if the message is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the message is read.
     */
    public function isRead(): bool
    {
        return $this->status === self::STATUS_READ;
    }

    /**
     * Check if the message is archived.
     */
    public function isArchived(): bool
    {
        return $this->status === self::STATUS_ARCHIVED;
    }

    /**
     * Mark the message as read.
     */
    public function markAsRead(): bool
    {
        return $this->update(['status' => self::STATUS_READ]);
    }

    /**
     * Mark the message as archived.
     */
    public function markAsArchived(): bool
    {
        return $this->update(['status' => self::STATUS_ARCHIVED]);
    }
}
