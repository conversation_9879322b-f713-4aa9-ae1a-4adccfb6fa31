<?php

return [
    // Main navigation sections
    'dashboard' => 'Dashboard',
    'main_dashboard' => 'Main Dashboard',
    'management' => 'Management',
    'settings' => 'Settings',

    // Dashboard section
    'dashboard_section' => 'Dashboard',

    // Management section items
    'users' => 'Users',
    'all_users' => 'All Users',
    'add_user' => 'Add User',
    'messages' => 'Messages',
    'cities' => 'Cities',
    'areas' => 'Areas',
    'service_providers' => 'Service Providers',
    'services' => 'Services',

    // Settings section items
    'system_settings' => 'System Settings',

    // Header navigation
    'home' => 'Home',

    // User menu and dropdowns
    'profile' => 'Profile',
    'account' => 'Account',
    'logout' => 'Logout',

    // Apps and tools
    'apps' => 'Apps',
    'enabled' => 'Enabled',
    'go_to_apps' => 'Go to Apps',

    // Chat and messaging
    'chat' => 'Chat',
    'hr_team' => 'HR Team',
    'typing' => 'is typing..',
    'invite_users' => 'Invite Users',
    'team' => 'Team',
    'find_members' => 'Find Members',
    'meetings' => 'Meetings',
    'group_settings' => 'Group Settings',
    'decline' => 'Decline',
    'accept' => 'Accept',
    'send' => 'Send',
    'write_message' => 'Write a message...',
    'wants_to_join_chat' => 'wants to join chat',
    'design_team' => 'Design Team',
    'day_ago' => '1 day ago',

    // Header hardcoded strings that need translation
    'hello_message' => 'Hello!',
    'project_closing_message' => 'Next week we are closing the project. Do You have questions?',
    'excellent_news' => 'This is excellent news!',
    'checked_features' => 'I have checked the features, can not wait to demo them!',
    'rollout_plan_review' => 'I have looked over the rollout plan, and everything seems spot on. I am ready on my end and can not wait for the user feedback.',
    'build_review' => 'Haven\'t seen the build yet, I\'ll look now.',
    'checking_build' => 'Checking the build now',
    'meeting_link' => 'Tomorrow, I will send the link for the meeting',
    'jane_perez' => 'Jane Perez',
    'project_management' => 'Project management',
    'notes_management_app' => 'Notes management app',
    'devops_platform' => 'DevOps platform',
    'building_web_experiences' => 'Building web experiences',
    'invites_review_file' => 'invites you to review a file.',
    'added_works_to' => 'added 2 new works to',

    // Notifications
    'notifications_header' => 'Notifications',
    'all' => 'All',
    'inbox' => 'Inbox',
    'following' => 'Following',
    'view' => 'View',
    'export' => 'Export',
    'email' => 'Email',
    'sms' => 'SMS',
];
