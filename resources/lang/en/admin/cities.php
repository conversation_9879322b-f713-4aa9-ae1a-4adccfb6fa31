<?php

return [
    // Page titles and navigation
    'title' => 'Cities',
    'page_title' => 'Cities Management',
    'page_description' => 'Manage cities and their information',
    'create_city' => 'Create City',
    'edit_city' => 'Edit City',
    'city_details' => 'City Details',
    'cities_list' => 'Cities List',

    // Form fields
    'fields' => [
        'id' => 'ID',
        'name_ar' => 'Arabic Name',
        'name_en' => 'English Name',
        'areas_count' => 'Areas Count',
        'customers_count' => 'Customers Count',
        'service_providers_count' => 'Service Providers Count',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
    ],

    // Actions
    'actions' => [
        'create' => 'Create City',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'view' => 'View',
        'restore' => 'Restore',
        'force_delete' => 'Force Delete',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'back' => 'Back',
        'search' => 'Search',
        'filter' => 'Filter',
        'reset' => 'Reset',
        'export' => 'Export',
        'bulk_delete' => 'Bulk Delete',
        'bulk_restore' => 'Bulk Restore',
    ],

    // Status
    'status' => [
        'active' => 'Active',
        'deleted' => 'Deleted',
        'all' => 'All',
    ],

    // Messages
    'messages' => [
        'created_successfully' => 'City created successfully',
        'create_failed' => 'Failed to create city',
        'updated_successfully' => 'City updated successfully',
        'deleted_successfully' => 'City deleted successfully',
        'restored_successfully' => 'City restored successfully',
        'force_deleted_successfully' => 'City permanently deleted',
        'bulk_deleted_successfully' => 'Selected cities deleted successfully',
        'bulk_restored_successfully' => 'Selected cities restored successfully',
        'not_found' => 'City not found',
        'update_failed' => 'Failed to update city',
        'delete_failed' => 'Failed to delete city',
        'restore_failed' => 'Failed to restore city',
        'force_delete_failed' => 'Failed to permanently delete city',
        'bulk_delete_failed' => 'Failed to delete selected cities',
        'bulk_restore_failed' => 'Failed to restore selected cities',
        'delete_confirmation' => 'Are you sure you want to delete this city?',
        'restore_confirmation' => 'Are you sure you want to restore this city?',
        'force_delete_confirmation' => 'Are you sure you want to permanently delete this city? This action cannot be undone.',
        'bulk_delete_confirmation' => 'Are you sure you want to delete the selected cities?',
        'bulk_restore_confirmation' => 'Are you sure you want to restore the selected cities?',
        'no_cities_found' => 'No cities found',
        'search_placeholder' => 'Search by city name...',
    ],

    // Validation messages
    'validation' => [
        'name_ar_required' => 'Arabic name is required',
        'name_ar_unique' => 'This Arabic name is already taken',
        'name_en_required' => 'English name is required',
        'name_en_unique' => 'This English name is already taken',
        'name_string' => 'Name must be a string',
        'name_max' => 'Name may not be greater than 255 characters',
    ],

    // Statistics
    'statistics' => [
        'total_cities' => 'Total Cities',
        'cities_with_areas' => 'Cities with Areas',
        'cities_with_customers' => 'Cities with Customers',
        'cities_with_service_providers' => 'Cities with Service Providers',
        'deleted_cities' => 'Deleted Cities',
    ],

    // Filters
    'filters' => [
        'search_and_filter' => 'Search and Filter',
        'date_from' => 'From Date',
        'date_to' => 'To Date',
        'apply_filters' => 'Apply',
        'clear_filters' => 'Clear',
        'sort_by' => 'Sort By',
        'sort_direction' => 'Sort Direction',
        'ascending' => 'Ascending',
        'descending' => 'Descending',
    ],

    // Table headers
    'table' => [
        'id' => 'ID',
        'name' => 'Name',
        'areas_count' => 'Areas',
        'customers_count' => 'Customers',
        'service_providers_count' => 'Service Providers',
        'created_at' => 'Created',
        'actions' => 'Actions',
    ],

    // Placeholders
    'placeholders' => [
        'name_ar' => 'Enter Arabic city name',
        'name_en' => 'Enter English city name',
    ],

    // Sections
    'sections' => [
        'basic_information' => 'Basic Information',
        'translation_information' => 'Translation Information',
        'statistics_information' => 'Statistics Information',
        'related_data' => 'Related Data',
        'timestamps' => 'Timestamps'
    ],

    // Labels
    'labels' => [
        'multilingual_support' => 'Multilingual Support',
        'arabic_translation' => 'Arabic Translation',
        'english_translation' => 'English Translation',
        'areas_in_city' => 'Areas in this City',
        'customers_in_city' => 'Customers in this City',
        'service_providers_in_city' => 'Service Providers in this City',
    ],

    // Tabs
    'tabs' => [
        'overview' => 'Overview',
        'areas' => 'Areas',
        'customers' => 'Customers',
        'service_providers' => 'Service Providers',
    ],
];
