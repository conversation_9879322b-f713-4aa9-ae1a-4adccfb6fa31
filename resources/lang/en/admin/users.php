<?php

return [
    // Page titles and navigation
    'title' => 'Users',
    'page_title' => 'Users Management',
    'page_description' => 'Manage system users and their permissions',
    'create_user' => 'Create User',
    'edit_user' => 'Edit User',
    'user_details' => 'User Details',
    'users_list' => 'Users List',

    // Form fields
    'fields' => [
        'name' => 'Name',
        'email' => 'Email',
        'mobile' => 'Mobile Number',
        'password' => 'Password',
        'password_confirmation' => 'Confirm Password',
        'is_active' => 'Active Status',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
        'last_login' => 'Last Login',
    ],

    // Actions
    'actions' => [
        'create' => 'Create User',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'view' => 'View',
        'activate' => 'Activate',
        'deactivate' => 'Deactivate',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'back' => 'Back',
        'search' => 'Search',
        'filter' => 'Filter',
        'reset' => 'Reset',
        'export' => 'Export',
    ],

    // Status
    'status' => [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'all' => 'All',
    ],

    // Messages
    'messages' => [
        'created_successfully' => 'User created successfully',
        'updated_successfully' => 'User updated successfully',
        'deleted_successfully' => 'User deleted successfully',
        'activated_successfully' => 'User activated successfully',
        'deactivated_successfully' => 'User deactivated successfully',
        'not_found' => 'User not found',
        'delete_confirmation' => 'Are you sure you want to delete this user?',
        'activate_confirmation' => 'Are you sure you want to activate this user?',
        'deactivate_confirmation' => 'Are you sure you want to deactivate this user?',
        'no_users_found' => 'No users found',
        'search_placeholder' => 'Search by name, email, or mobile...',
    ],

    // Validation messages
    'validation' => [
        'name_required' => 'Name is required',
        'name_string' => 'Name must be a string',
        'name_max' => 'Name may not be greater than 255 characters',
        'email_required' => 'Email is required',
        'email_format' => 'Email must be a valid email address',
        'email_unique' => 'This email is already taken',
        'mobile_format' => 'Mobile number must be in Saudi format (05xxxxxxxx)',
        'mobile_unique' => 'This mobile number is already taken',
        'password_required' => 'Password is required',
        'password_confirmation_required' => 'Password confirmation is required',
        'password_confirmation_same' => 'Password confirmation does not match',
    ],

    // Statistics
    'statistics' => [
        'total_users' => 'Total Users',
        'active_users' => 'Active Users',
        'inactive_users' => 'Inactive Users',
        'new_users_today' => 'New Users Today',
    ],

    // Filters
    'filters' => [
        'search_and_filter' => 'Search and Filter',
        'status' => 'Status',
        'date_from' => 'From Date',
        'date_to' => 'To Date',
        'apply_filters' => 'Apply',
        'clear_filters' => 'Clear',
    ],

    // Table headers
    'table' => [
        'id' => 'ID',
        'name' => 'Name',
        'email' => 'Email',
        'mobile' => 'Mobile',
        'status' => 'Status',
        'created_at' => 'Created',
        'actions' => 'Actions',
    ],

    // Placeholders
    'placeholders' => [
        'name' => 'Enter full name',
        'email' => 'Enter email address',
        'mobile' => 'Enter mobile number (05xxxxxxxx)',
        'password' => 'Enter password',
        'password_confirmation' => 'Confirm password',
    ],

    // Additional sections
    'sections' => [
        'basic_information' => 'Basic Information',
        'system_information' => 'System Information',
        'additional_information' => 'Additional Information',
        'timestamps' => 'Timestamps',
    ],

    // Additional fields and labels
    'labels' => [
        'email_verified_at' => 'Email Verified At',
        'days_since_registration' => 'Days Since Registration',
        'days_since_last_update' => 'Days Since Last Update',
        'current_status' => 'Current Status',
        'password_requirements' => 'Minimum 8 characters with uppercase, lowercase, number and symbol',
        'password_edit_hint' => 'Leave empty to keep current password. Minimum 8 characters with uppercase, lowercase, number and symbol',
    ],
];
