<?php

return [
    // Page titles and navigation
    'title' => 'Areas',
    'page_title' => 'Areas Management',
    'page_description' => 'Manage areas and their information',
    'create_area' => 'Create Area',
    'edit_area' => 'Edit Area',
    'area_details' => 'Area Details',
    'areas_list' => 'Areas List',

    // Form fields
    'fields' => [
        'id' => 'ID',
        'name_ar' => 'Arabic Name',
        'name_en' => 'English Name',
        'city' => 'City',
        'customers_count' => 'Customers Count',
        'service_providers_count' => 'Service Providers Count',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
    ],

    // Actions
    'actions' => [
        'create' => 'Create Area',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'view' => 'View',
        'restore' => 'Restore',
        'force_delete' => 'Force Delete',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'back' => 'Back',
        'search' => 'Search',
        'filter' => 'Filter',
        'reset' => 'Reset',
        'export' => 'Export',
        'bulk_delete' => 'Bulk Delete',
        'bulk_restore' => 'Bulk Restore',
    ],

    // Status
    'status' => [
        'active' => 'Active',
        'deleted' => 'Deleted',
        'all' => 'All',
    ],

    // Messages
    'messages' => [
        'created_successfully' => 'Area created successfully',
        'create_failed' => 'Failed to create area',
        'updated_successfully' => 'Area updated successfully',
        'deleted_successfully' => 'Area deleted successfully',
        'restored_successfully' => 'Area restored successfully',
        'force_deleted_successfully' => 'Area permanently deleted',
        'bulk_deleted_successfully' => 'Selected areas deleted successfully',
        'bulk_restored_successfully' => 'Selected areas restored successfully',
        'not_found' => 'Area not found',
        'update_failed' => 'Failed to update area',
        'delete_failed' => 'Failed to delete area',
        'restore_failed' => 'Failed to restore area',
        'force_delete_failed' => 'Failed to permanently delete area',
        'bulk_delete_failed' => 'Failed to delete selected areas',
        'bulk_restore_failed' => 'Failed to restore selected areas',
        'delete_confirmation' => 'Are you sure you want to delete this area?',
        'restore_confirmation' => 'Are you sure you want to restore this area?',
        'force_delete_confirmation' => 'Are you sure you want to permanently delete this area? This action cannot be undone.',
        'bulk_delete_confirmation' => 'Are you sure you want to delete the selected areas?',
        'bulk_restore_confirmation' => 'Are you sure you want to restore the selected areas?',
        'no_areas_found' => 'No areas found',
        'search_placeholder' => 'Search by area or city name...',
    ],

    // Validation messages
    'validation' => [
        'city_required' => 'City is required',
        'city_exists' => 'Selected city does not exist',
        'name_ar_required' => 'Arabic name is required',
        'name_ar_unique_in_city' => 'This Arabic name is already taken in this city',
        'name_en_required' => 'English name is required',
        'name_en_unique_in_city' => 'This English name is already taken in this city',
        'name_string' => 'Name must be a string',
        'name_max' => 'Name may not be greater than 255 characters',
    ],

    // Statistics
    'statistics' => [
        'total_areas' => 'Total Areas',
        'areas_with_customers' => 'Areas with Customers',
        'areas_with_service_providers' => 'Areas with Service Providers',
        'deleted_areas' => 'Deleted Areas',
    ],

    // Filters
    'filters' => [
        'search_and_filter' => 'Search and Filter',
        'city' => 'City',
        'all_cities' => 'All Cities',
        'date_from' => 'From Date',
        'date_to' => 'To Date',
        'apply_filters' => 'Apply',
        'clear_filters' => 'Clear',
        'sort_by' => 'Sort By',
        'sort_direction' => 'Sort Direction',
        'ascending' => 'Ascending',
        'descending' => 'Descending',
    ],

    // Table headers
    'table' => [
        'id' => 'ID',
        'name' => 'Name',
        'city' => 'City',
        'customers_count' => 'Customers',
        'service_providers_count' => 'Service Providers',
        'created_at' => 'Created',
        'actions' => 'Actions',
    ],

    // Placeholders
    'placeholders' => [
        'name_ar' => 'Enter Arabic area name',
        'name_en' => 'Enter English area name',
        'select_city' => 'Select a city',
    ],

    // Sections
    'sections' => [
        'basic_information' => 'Basic Information',
        'translation_information' => 'Translation Information',
        'city_information' => 'City Information',
        'statistics_information' => 'Statistics Information',
        'related_data' => 'Related Data',
        'timestamps' => 'Timestamps',
    ],

    // Labels
    'labels' => [
        'multilingual_support' => 'Multilingual Support',
        'arabic_translation' => 'Arabic Translation',
        'english_translation' => 'English Translation',
        'belongs_to_city' => 'Belongs to City',
        'customers_in_area' => 'Customers in this Area',
        'service_providers_in_area' => 'Service Providers in this Area',
        'no_customers_in_area' => 'No customers in this area',
        'no_service_providers_in_area' => 'No service providers in this area',
    ],

    // Tabs
    'tabs' => [
        'overview' => 'Overview',
        'customers' => 'Customers',
        'service_providers' => 'Service Providers',
    ],

    // Select options
    'select' => [
        'choose_city' => 'Choose City',
        'no_cities_available' => 'No cities available',
    ],
];
