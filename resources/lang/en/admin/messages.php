<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Messages Module Language Lines (English)
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for the messages module in the admin panel.
    | You are free to modify these language lines according to your application's
    | requirements.
    |
    */

    // Flash messages (keeping existing ones for compatibility)
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'validation_errors' => 'Validation Errors',

    // Page titles and descriptions
    'title' => 'Contact Messages',
    'page_title' => 'Contact Messages Management',
    'page_description' => 'Manage and respond to customer contact messages',

    // Navigation
    'menu_title' => 'Messages',

    // Statistics
    'statistics' => [
        'total_messages' => 'Total Messages',
        'pending_messages' => 'Pending Messages',
        'read_messages' => 'Read Messages',
        'archived_messages' => 'Archived Messages',
    ],

    // Table headers
    'table' => [
        'id' => 'ID',
        'title' => 'Title',
        'name' => 'Name',
        'email' => 'Email',
        'status' => 'Status',
        'created_at' => 'Created At',
        'actions' => 'Actions',
        'message_preview' => 'Message Preview',
    ],

    // Form fields
    'form' => [
        'title' => 'Title',
        'name' => 'Name',
        'email' => 'Email',
        'message' => 'Message',
        'status' => 'Status',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
    ],

    // Status values
    'status' => [
        'pending' => 'Pending',
        'read' => 'Read',
        'archived' => 'Archived',
    ],

    // Actions
    'actions' => [
        'view' => 'View',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'mark_as_read' => 'Mark as Read',
        'mark_as_archived' => 'Archive',
        'bulk_delete' => 'Bulk Delete',
        'bulk_mark_read' => 'Mark Selected as Read',
        'bulk_archive' => 'Archive Selected',
        'bulk_actions' => 'Bulk Actions',
        'search' => 'Search',
        'filter' => 'Filter',
        'reset_filters' => 'Reset',
        'export' => 'Export',
    ],

    // Filters
    'filters' => [
        'search_and_filter' => 'Search and Filter',
        'search_placeholder' => 'Search by title or message content...',
        'status_filter' => 'Filter by Status',
        'all_statuses' => 'All Statuses',
        'date_from' => 'Date From',
        'date_to' => 'Date To',
        'apply_filters' => 'Apply',
        'clear_filters' => 'Clear',
    ],

    // Messages and notifications
    'messages' => [
        'no_messages_found' => 'No messages found',
        'no_messages_description' => 'There are no contact messages to display.',
        'created_successfully' => 'Message created successfully',
        'updated_successfully' => 'Message updated successfully',
        'deleted_successfully' => 'Message deleted successfully',
        'marked_as_read' => 'Message marked as read',
        'marked_as_archived' => 'Message archived successfully',
        'bulk_action_success' => 'Bulk action completed successfully',
        'update_failed' => 'Failed to update message',
        'delete_failed' => 'Failed to delete message',
        'bulk_action_failed' => 'Bulk action failed',
        'confirm_delete' => 'Are you sure you want to delete this message?',
        'confirm_bulk_delete' => 'Are you sure you want to delete the selected messages?',
        'confirm_bulk_mark_read' => 'Are you sure you want to mark the selected messages as read?',
        'confirm_bulk_archive' => 'Are you sure you want to archive the selected messages?',
        'no_items_selected' => 'Please select at least one message.',
    ],

    // Validation messages
    'validation' => [
        'title_required' => 'The title field is required.',
        'title_max' => 'The title may not be greater than 255 characters.',
        'name_required' => 'The name field is required.',
        'name_max' => 'The name may not be greater than 255 characters.',
        'email_required' => 'The email field is required.',
        'email_format' => 'The email must be a valid email address.',
        'email_max' => 'The email may not be greater than 255 characters.',
        'message_required' => 'The message field is required.',
        'message_max' => 'The message may not be greater than 5000 characters.',
        'status_required' => 'The status field is required.',
        'status_invalid' => 'The selected status is invalid.',
    ],

    // Buttons
    'buttons' => [
        'save' => 'Save',
        'cancel' => 'Cancel',
        'back' => 'Back',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'view' => 'View',
        'close' => 'Close',
        'submit' => 'Submit',
        'reset' => 'Reset',
    ],

    // Pagination
    'pagination' => [
        'showing' => 'Showing',
        'to' => 'to',
        'of' => 'of',
        'results' => 'results',
        'previous' => 'Previous',
        'next' => 'Next',
    ],

    // Details view
    'details' => [
        'message_details' => 'Message Details',
        'sender_information' => 'Sender Information',
        'message_content' => 'Message Content',
        'status_information' => 'Status Information',
        'timestamps' => 'Timestamps',
    ],

    // Empty states
    'empty_states' => [
        'no_results' => 'No results found',
        'no_results_description' => 'Try adjusting your search criteria or filters.',
        'no_messages' => 'No messages yet',
        'no_messages_description' => 'Contact messages will appear here when customers send them.',
    ],

    // Help text
    'help' => [
        'title' => 'Help',
        'edit_description' => 'You can edit the message details and update its status.',
        'edit_tip_1' => 'Changing status to "Read" marks the message as reviewed.',
        'edit_tip_2' => 'Use "Archived" status for messages that are resolved.',
        'edit_tip_3' => 'All changes are logged for audit purposes.',
    ],
];
