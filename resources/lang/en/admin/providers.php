<?php

return [
    // Page titles and navigation
    'title' => 'Service Providers',
    'page_title' => 'Service Providers',
    'page_description' => 'Manage service providers and their information',
    'providers_list' => 'Service Providers List',
    'management' => 'Service Providers Management',
    'list' => 'Service Providers List',
    'details' => 'Service Provider Details',
    'edit' => 'Edit Service Provider',
    'view' => 'View Service Provider',

    // Fields
    'fields' => [
        'id' => 'ID',
        'name' => 'Name',
        'name_ar' => 'Name (Arabic)',
        'name_en' => 'Name (English)',
        'description' => 'Description',
        'description_ar' => 'Description (Arabic)',
        'description_en' => 'Description (English)',
        'logo' => 'Logo',
        'rating' => 'Rating',
        'city' => 'City',
        'area' => 'Area',
        'location' => 'Location',
        'longitude' => 'Longitude',
        'latitude' => 'Latitude',
        'coordinates' => 'Coordinates',
        'status' => 'Status',
        'is_active' => 'Active',
        'services_count' => 'Services Count',
        'average_price' => 'Average Price',
        'working_hours' => 'Working Hours',
        'gallery' => 'Gallery',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
        'deleted_at' => 'Deleted At',
    ],

    // Status labels
    'status' => [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'all' => 'All Statuses',
    ],

    // Actions
    'actions' => [
        'add' => 'Add Service Provider',
        'edit' => 'Edit',
        'view' => 'View',
        'delete' => 'Delete',
        'restore' => 'Restore',
        'force_delete' => 'Force Delete',
        'activate' => 'Activate',
        'deactivate' => 'Deactivate',
        'bulk_actions' => 'Bulk Actions',
        'bulk_delete' => 'Bulk Delete',
        'bulk_restore' => 'Bulk Restore',
        'bulk_activate' => 'Bulk Activate',
        'bulk_deactivate' => 'Bulk Deactivate',
        'search' => 'Search',
        'filter' => 'Filter',
        'reset' => 'Reset',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'back' => 'Back',
        'export' => 'Export',
        'import' => 'Import',
    ],

    // Filters
    'filters' => [
        'search_and_filter' => 'Search and Filter',
        'search_placeholder' => 'Search by name or description...',
        'city_filter' => 'Filter by City',
        'area_filter' => 'Filter by Area',
        'status_filter' => 'Filter by Status',
        'date_range' => 'Date Range',
        'date_from' => 'From Date',
        'date_to' => 'To Date',
        'all_cities' => 'All Cities',
        'all_areas' => 'All Areas',
        'select_city' => 'Select City',
        'select_area' => 'Select Area',
        'apply_filters' => 'Apply',
        'clear_filters' => 'Clear',
        'active_filters' => '{1} :count active filter|[2,*] :count active',
    ],

    // Statistics
    'statistics' => [
        'total_providers' => 'Total Providers',
        'active_providers' => 'Active Providers',
        'inactive_providers' => 'Inactive Providers',
        'providers_with_services' => 'Providers with Services',
        'of_total' => 'of total',
    ],

    // Messages
    'messages' => [
        'not_found' => 'Service provider not found.',
        'created_successfully' => 'Service provider created successfully.',
        'updated_successfully' => 'Service provider updated successfully.',
        'deleted_successfully' => 'Service provider deleted successfully.',
        'restored_successfully' => 'Service provider restored successfully.',
        'force_deleted_successfully' => 'Service provider permanently deleted.',
        'create_failed' => 'Failed to create service provider.',
        'update_failed' => 'Failed to update service provider.',
        'delete_failed' => 'Failed to delete service provider.',
        'restore_failed' => 'Failed to restore service provider.',
        'force_delete_failed' => 'Failed to permanently delete service provider.',
        'no_items_selected' => 'No items selected.',
        'bulk_deleted_successfully' => ':count service providers deleted successfully.',
        'bulk_restored_successfully' => ':count service providers restored successfully.',
        'bulk_activated_successfully' => ':count service providers activated successfully.',
        'bulk_deactivated_successfully' => ':count service providers deactivated successfully.',
        'bulk_delete_failed' => 'Failed to delete selected service providers.',
        'bulk_restore_failed' => 'Failed to restore selected service providers.',
        'bulk_status_update_failed' => 'Failed to update status of selected service providers.',
        'no_providers_found' => 'No service providers found.',
        'confirm_delete' => 'Are you sure you want to delete this service provider?',
        'confirm_force_delete' => 'Are you sure you want to permanently delete this service provider? This action cannot be undone.',
        'confirm_bulk_delete' => 'Are you sure you want to delete the selected service providers?',
        'confirm_bulk_restore' => 'Are you sure you want to restore the selected service providers?',
        'confirm_bulk_activate' => 'Are you sure you want to activate the selected service providers?',
        'confirm_bulk_deactivate' => 'Are you sure you want to deactivate the selected service providers?',
    ],

    // Form labels and placeholders
    'form' => [
        'basic_information' => 'Basic Information',
        'location_information' => 'Location Information',
        'status_settings' => 'Status Settings',
        'translations' => 'Translations',
        'arabic_translation' => 'Arabic Translation',
        'english_translation' => 'English Translation',
        'name_placeholder' => 'Enter provider name',
        'description_placeholder' => 'Enter provider description',
        'logo_placeholder' => 'Enter logo URL',
        'rating_placeholder' => 'Enter rating (0-5)',
        'longitude_placeholder' => 'Enter longitude',
        'latitude_placeholder' => 'Enter latitude',
        'required_field' => 'This field is required',
        'optional_field' => 'This field is optional',
        'loading_areas' => 'Loading areas...',
        'select_city_first' => 'Please select a city first',
        'no_areas_found' => 'No areas found for this city',
        'error_loading_areas' => 'Error loading areas',
    ],

    // Sections
    'sections' => [
        'timestamps' => 'Timestamps',
    ],

    // Table headers
    'table' => [
        'provider_info' => 'Provider Information',
        'location_info' => 'Location',
        'status_info' => 'Status',
        'services_info' => 'Services',
        'actions' => 'Actions',
        'select_all' => 'Select All',
        'no_data' => 'No data available',
        'showing_results' => 'Showing :from to :to of :total results',
    ],

    // Pagination
    'pagination' => [
        'previous' => 'Previous',
        'next' => 'Next',
        'showing' => 'Showing',
        'to' => 'to',
        'of' => 'of',
        'results' => 'results',
    ],

    // Validation
    'validation' => [
        'name_required' => 'Provider name is required.',
        'name_max' => 'Provider name may not be greater than 255 characters.',
        'description_max' => 'Description may not be greater than 5000 characters.',
        'rating_numeric' => 'Rating must be a number.',
        'rating_min' => 'Rating must be at least 0.',
        'rating_max' => 'Rating may not be greater than 5.',
        'city_required' => 'Please select a city.',
        'area_required' => 'Please select an area.',
        'city_exists' => 'Selected city does not exist.',
        'area_exists' => 'Selected area does not exist.',
        'longitude_numeric' => 'Longitude must be a number.',
        'longitude_between' => 'Longitude must be between -180 and 180.',
        'latitude_numeric' => 'Latitude must be a number.',
        'latitude_between' => 'Latitude must be between -90 and 90.',
    ],
];
