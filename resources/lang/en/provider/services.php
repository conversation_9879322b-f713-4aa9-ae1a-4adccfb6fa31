<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Provider Services Language Lines (English)
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for the provider services management.
    | You are free to modify these language lines according to your application's
    | requirements.
    |
    */

    // Page titles
    'title' => 'Services',
    'page_title' => 'Services Management',
    'page_description' => 'Manage your services and offerings',

    // Actions
    'actions' => [
        'add_service' => 'Add Service',
        'bulk_actions' => 'Bulk Actions',
        'activate_selected' => 'Activate Selected',
        'deactivate_selected' => 'Deactivate Selected',
        'search' => 'Search',
        'reset' => 'Reset',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'view' => 'View',
    ],

    // Statistics
    'statistics' => [
        'total_services' => 'Total Services',
        'active_services' => 'Active Services',
        'average_price' => 'Average Price',
        'total_bookings' => 'Total Bookings',
    ],

    // Currency
    'currency' => 'SAR',

    // Search and filters
    'search' => [
        'placeholder' => 'Search services...',
    ],

    'filters' => [
        'all_statuses' => 'All Statuses',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'all_categories' => 'All Categories',
    ],

    'sort' => [
        'name' => 'Name',
        'price' => 'Price',
        'newest' => 'Newest',
    ],

    // Table
    'table' => [
        'title' => 'Services List',
        'showing_results' => 'Showing :total results',
        'service' => 'Service',
        'category' => 'Category',
        'price' => 'Price',
        'status' => 'Status',
        'bookings' => 'Bookings',
        'rating' => 'Rating',
        'actions' => 'Actions',
    ],

    // Status
    'status' => [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'pending' => 'Pending',
    ],

    // Sample data (for demo purposes)
    'sample' => [
        'service_name' => 'Home Cleaning Service',
        'service_description' => 'Professional home cleaning service',
        'category' => 'Cleaning',
    ],

    // Pagination
    'pagination' => [
        'showing' => 'Showing',
        'to' => 'to',
        'of' => 'of',
        'results' => 'results',
    ],

    // Empty state
    'empty' => [
        'title' => 'No Services Found',
        'description' => 'You haven\'t added any services yet. Start by creating your first service.',
        'add_first_service' => 'Add Your First Service',
    ],

    // Create/Edit form
    'create' => [
        'title' => 'Add New Service',
        'page_title' => 'Add New Service',
        'page_description' => 'Create a new service offering for your customers',
        'form_title' => 'Service Information',
        'sections' => [
            'basic_info' => 'Basic Information',
            'pricing' => 'Pricing & Duration',
            'description' => 'Service Description',
        ],
        'fields' => [
            'name' => 'Service Name',
            'category' => 'Category',
            'price' => 'Price',
            'duration' => 'Duration (minutes)',
            'description' => 'Description',
            'status' => 'Status',
        ],
        'placeholders' => [
            'name' => 'Enter service name',
            'select_category' => 'Select a category',
            'price' => 'Enter price',
            'duration' => 'Enter duration in minutes',
            'description' => 'Describe your service in detail',
        ],
        'actions' => [
            'save' => 'Save Service',
            'save_and_add_another' => 'Save & Add Another',
            'cancel' => 'Cancel',
        ],
    ],

    // Messages
    'messages' => [
        'no_services_selected' => 'Please select at least one service.',
        'confirm_bulk_action' => 'Are you sure you want to perform this action on the selected services?',
        'service_created' => 'Service created successfully.',
        'service_updated' => 'Service updated successfully.',
        'service_deleted' => 'Service deleted successfully.',
        'services_activated' => 'Selected services have been activated.',
        'services_deactivated' => 'Selected services have been deactivated.',
    ],
];
