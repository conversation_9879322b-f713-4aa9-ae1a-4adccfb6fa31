<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Provider Working Hours Language Lines (English)
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for the provider working hours management.
    | You are free to modify these language lines according to your application's
    | requirements.
    |
    */

    // Page titles
    'title' => 'Working Hours',
    'page_title' => 'Working Hours Management',
    'page_description' => 'Manage your availability and working schedule',

    // Actions
    'actions' => [
        'save_changes' => 'Save Changes',
        'reset_default' => 'Reset to Default',
    ],

    // Sections
    'sections' => [
        'schedule_management' => 'Schedule Management',
        'current_status' => 'Current Status',
        'quick_templates' => 'Quick Templates',
        'availability_statistics' => 'Availability Statistics',
    ],

    // Days of the week
    'days' => [
        'monday' => 'Monday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday',
    ],

    // Form fields
    'fields' => [
        'is_working' => 'Working',
        'from' => 'From',
        'to' => 'To',
        '24_hours' => '24 Hours',
    ],

    // Status
    'status' => [
        'open' => 'Open',
        'closed' => 'Closed',
        'closes_at' => 'Closes at',
        'opens_at' => 'Opens at',
        'accepting_bookings' => 'Accepting Bookings',
        'not_accepting_bookings' => 'Not Accepting Bookings',
    ],

    // Templates
    'templates' => [
        'business_hours' => 'Business Hours (9-5)',
        'extended_hours' => 'Extended Hours (8-8)',
        'weekends_only' => 'Weekends Only',
        '24_7' => '24/7 Available',
    ],

    // Statistics
    'statistics' => [
        'hours_per_week' => 'Hours Per Week',
        'working_days' => 'Working Days',
        'days' => 'days',
        'avg_daily_hours' => 'Avg Daily Hours',
        'availability_score' => 'Availability Score',
    ],

    // Messages
    'messages' => [
        'schedule_updated' => 'Working hours updated successfully.',
        'template_applied' => 'Template applied successfully.',
        'reset_successful' => 'Working hours reset to default.',
        'invalid_time_range' => 'Invalid time range. End time must be after start time.',
        'overlapping_hours' => 'Working hours cannot overlap.',
    ],
];
