<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Provider Accessibility Language Lines (English)
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for accessibility features
    | in the provider dashboard.
    |
    */

    'skip_to_main_content' => 'Skip to main content',
    'main_navigation' => 'Main navigation',
    'user_menu' => 'User menu',
    'notifications_menu' => 'Notifications menu',
    'language_switcher' => 'Language switcher',
    'search_form' => 'Search form',
    'data_table' => 'Data table',
    'pagination' => 'Pagination',
    'modal_dialog' => 'Modal dialog',
    'close_modal' => 'Close modal',
    'loading' => 'Loading content',
    'error_occurred' => 'An error occurred',
    'success_message' => 'Success message',
    'warning_message' => 'Warning message',
    'info_message' => 'Information message',
    'required_field' => 'Required field',
    'optional_field' => 'Optional field',
    'form_validation_errors' => 'Form contains validation errors',
    'page_loading' => 'Page is loading',
    'content_updated' => 'Content has been updated',
    'item_selected' => 'Item selected',
    'items_selected' => ':count items selected',
    'sort_ascending' => 'Sort ascending',
    'sort_descending' => 'Sort descending',
    'filter_applied' => 'Filter applied',
    'search_results' => 'Search results',
    'no_results' => 'No results found',
    'expand_menu' => 'Expand menu',
    'collapse_menu' => 'Collapse menu',
    'toggle_sidebar' => 'Toggle sidebar',
    'current_page' => 'Current page',
    'go_to_page' => 'Go to page :page',
    'previous_page' => 'Previous page',
    'next_page' => 'Next page',
    'first_page' => 'First page',
    'last_page' => 'Last page',
];
