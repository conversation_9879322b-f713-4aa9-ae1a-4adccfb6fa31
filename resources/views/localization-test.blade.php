<!DOCTYPE html>
<html dir="@direction" lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-locale="@lang">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Localization Test - {{ $current_language_name }}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
    @isRtl
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;600;700&display=swap" rel="stylesheet" />
    @endisRtl
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: @langFont;
        }
        
        .rtl {
            direction: rtl;
        }
        
        .ltr {
            direction: ltr;
        }
        
        .test-card {
            @apply bg-white rounded-lg shadow-md p-6 mb-6;
        }
        
        .info-item {
            @apply flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0;
        }
        
        .info-label {
            @apply font-medium text-gray-700;
        }
        
        .info-value {
            @apply text-gray-900 font-mono text-sm;
        }
        
        .language-switch-btn {
            @apply inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors duration-200 mx-1;
        }
        
        .current-language {
            @apply bg-green-500 hover:bg-green-600;
        }
    </style>
</head>
<body class="bg-gray-100 {{ $direction_class }}" style="font-family: {{ $language_font }};">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="test-card">
            <h1 class="text-3xl font-bold text-center mb-4 {{ $text_alignment }}">
                @isRtl
                    اختبار التوطين
                @else
                    Localization Test
                @endisRtl
            </h1>
            <p class="text-center text-gray-600">
                @isRtl
                    اختبار وظائف التوطين ودعم اللغات المتعددة
                @else
                    Testing localization functionality and multi-language support
                @endisRtl
            </p>
        </div>

        <!-- Language Switcher -->
        <div class="test-card">
            <h2 class="text-xl font-semibold mb-4 {{ $text_alignment }}">
                @isRtl
                    تبديل اللغة
                @else
                    Language Switcher
                @endisRtl
            </h2>
            <div class="text-center">
                @foreach($language_switching_urls as $locale => $language)
                    <a href="{{ $language['url'] }}" 
                       class="language-switch-btn {{ $language['is_current'] ? 'current-language' : '' }}"
                       data-locale="{{ $locale }}"
                       data-direction="{{ $language['direction'] }}">
                        {{ $language['native'] }} ({{ $language['name'] }})
                        @if($language['direction'] === 'rtl')
                            <span class="text-xs">RTL</span>
                        @endif
                    </a>
                @endforeach
            </div>
        </div>

        <!-- Current Language Information -->
        <div class="test-card">
            <h2 class="text-xl font-semibold mb-4 {{ $text_alignment }}">
                @isRtl
                    معلومات اللغة الحالية
                @else
                    Current Language Information
                @endisRtl
            </h2>
            <div class="space-y-2">
                <div class="info-item">
                    <span class="info-label">
                        @isRtl كود اللغة @else Language Code @endisRtl
                    </span>
                    <span class="info-value">{{ $current_language }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        @isRtl اسم اللغة @else Language Name @endisRtl
                    </span>
                    <span class="info-value">{{ $current_language_name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        @isRtl الاسم المحلي @else Native Name @endisRtl
                    </span>
                    <span class="info-value">{{ $current_language_native }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        @isRtl الاتجاه @else Direction @endisRtl
                    </span>
                    <span class="info-value">{{ $current_direction }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        @isRtl من اليمين لليسار @else Is RTL @endisRtl
                    </span>
                    <span class="info-value">{{ $is_rtl ? 'Yes' : 'No' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        @isRtl من اليسار لليمين @else Is LTR @endisRtl
                    </span>
                    <span class="info-value">{{ $is_ltr ? 'Yes' : 'No' }}</span>
                </div>
            </div>
        </div>

        <!-- Blade Directives Test -->
        <div class="test-card">
            <h2 class="text-xl font-semibold mb-4 {{ $text_alignment }}">
                @isRtl
                    اختبار توجيهات Blade
                @else
                    Blade Directives Test
                @endisRtl
            </h2>
            <div class="space-y-2">
                <div class="info-item">
                    <span class="info-label">@lang directive:</span>
                    <span class="info-value">@lang</span>
                </div>
                <div class="info-item">
                    <span class="info-label">@direction directive:</span>
                    <span class="info-value">@direction</span>
                </div>
                <div class="info-item">
                    <span class="info-label">@langFont directive:</span>
                    <span class="info-value">@langFont</span>
                </div>
                <div class="info-item">
                    <span class="info-label">@textAlign directive:</span>
                    <span class="info-value">@textAlign</span>
                </div>
            </div>
        </div>

        <!-- RTL/LTR Conditional Content -->
        <div class="test-card">
            <h2 class="text-xl font-semibold mb-4 {{ $text_alignment }}">
                @isRtl
                    المحتوى الشرطي
                @else
                    Conditional Content
                @endisRtl
            </h2>
            
            @isRtl
                <div class="bg-blue-100 p-4 rounded text-right">
                    <h3 class="font-semibold text-blue-800">محتوى خاص باللغة العربية</h3>
                    <p class="text-blue-700">هذا المحتوى يظهر فقط عندما تكون اللغة الحالية تستخدم الكتابة من اليمين إلى اليسار (RTL).</p>
                </div>
            @endisRtl
            
            @isLtr
                <div class="bg-green-100 p-4 rounded text-left">
                    <h3 class="font-semibold text-green-800">English-specific Content</h3>
                    <p class="text-green-700">This content only appears when the current language uses left-to-right (LTR) writing.</p>
                </div>
            @endisLtr
        </div>

        <!-- API Test -->
        <div class="test-card">
            <h2 class="text-xl font-semibold mb-4 {{ $text_alignment }}">
                @isRtl
                    اختبار API
                @else
                    API Test
                @endisRtl
            </h2>
            <button id="testApiBtn" class="language-switch-btn">
                @isRtl
                    اختبار API للغة
                @else
                    Test Language API
                @endisRtl
            </button>
            <pre id="apiResult" class="mt-4 bg-gray-100 p-4 rounded text-xs overflow-auto hidden"></pre>
        </div>
    </div>

    <script>
        document.getElementById('testApiBtn').addEventListener('click', function() {
            fetch('/localization-test/api')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('apiResult').textContent = JSON.stringify(data, null, 2);
                    document.getElementById('apiResult').classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('apiResult').textContent = 'Error: ' + error.message;
                    document.getElementById('apiResult').classList.remove('hidden');
                });
        });

        // Language switching with smooth transition
        document.querySelectorAll('.language-switch-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                const direction = this.getAttribute('data-direction');
                if (direction) {
                    document.documentElement.style.transition = 'all 0.3s ease';
                    document.documentElement.setAttribute('dir', direction);
                }
            });
        });
    </script>
</body>
</html>
