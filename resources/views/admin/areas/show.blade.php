@extends('admin.layouts.app')

@section('title', __('admin/areas.area_details') . ' - ' . config('app.name'))

@section('page-title', __('admin/areas.area_details'))

@section('page-description', $area->name)

@section('page-actions')
    <div class="flex gap-2">
        <a href="{{ route('admin.areas.edit', $area->id) }}" class="btn btn-primary">
            <i class="ki-filled ki-notepad-edit"></i>
            {{ __('admin/areas.actions.edit') }}
        </a>
        <a href="{{ route('admin.areas.index') }}" class="btn btn-light">
            <i class="ki-filled ki-arrow-left"></i>
            {{ __('admin/areas.actions.back') }}
        </a>
    </div>
@endsection

@section('content')
    <!-- Area Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
        <!-- Basic Information -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/areas.sections.basic_information') }}</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <!-- Arabic Name -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/areas.fields.name_ar') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $area->translate('ar')->name ?? '-' }}</span>
                            </div>
                        </div>

                        <!-- English Name -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/areas.fields.name_en') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $area->translate('en')->name ?? '-' }}</span>
                            </div>
                        </div>

                        <!-- City -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/areas.fields.city') }}</label>
                            <div class="flex-1">
                                <a href="{{ route('admin.cities.show', $area->city->id) }}" class="btn btn-link">
                                    {{ $area->city->name }}
                                </a>
                            </div>
                        </div>

                        <!-- Area ID -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/areas.fields.id') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">#{{ $area->id }}</span>
                            </div>
                        </div>

                    </div>

                    <div class="mt-8">
                        <!-- Timestamps Section -->
                        <div class="border-b border-gray-200 pt-4 mb-4">
                            <h4 class="text-lg font-medium mb-3">{{ __('admin/areas.sections.timestamps') }}</h4>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Created At -->
                            <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                <label class="form-label max-w-56">{{ __('admin/areas.fields.created_at') }}</label>
                                <div class="flex-1">
                                    <span
                                        class="text-sm text-gray-900">{{ $area->created_at->format('Y-m-d H:i:s') }}</span>
                                </div>
                            </div>

                            <!-- Updated At -->
                            <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                <label class="form-label max-w-56">{{ __('admin/areas.fields.updated_at') }}</label>
                                <div class="flex-1">
                                    <span
                                        class="text-sm text-gray-900">{{ $area->updated_at->format('Y-m-d H:i:s') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="lg:col-span-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/areas.sections.statistics_information') }}</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <!-- Customers Count -->
                        <div class="flex items-center justify-between p-3 bg-primary-light rounded-lg">
                            <div class="flex items-center gap-3">
                                <i class="ki-filled ki-profile-user text-info text-lg"></i>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ __('admin/areas.fields.customers_count') }}
                                </span>
                            </div>
                            <span
                                class="badge badge-outline badge-info">{{ number_format($area->customers_count ?? 0) }}</span>
                        </div>

                        <!-- Service Providers Count -->
                        <div class="flex items-center justify-between p-3 bg-info-light rounded-lg mt-4">
                            <div class="flex items-center gap-3">
                                <i class="ki-filled ki-handshake text-warning text-lg"></i>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ __('admin/areas.fields.service_providers_count') }}
                                </span>
                            </div>
                            <span
                                class="badge badge-outline badge-warning">{{ number_format($area->service_providers_count ?? 0) }}</span>
                        </div>

                        <!-- City Link -->
                        <div class="flex items-center justify-between p-3 bg-warning-light rounded-lg mt-4">
                            <div class="flex items-center gap-3">
                                <i class="ki-filled ki-geolocation text-warning text-lg"></i>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ __('admin/areas.labels.belongs_to_city') }}
                                </span>
                            </div>
                            <a href="{{ route('admin.cities.show', $area->city->id) }}" class="btn btn-sm btn-primary">
                                {{ __('admin/cities.actions.view') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Related Data Tabs -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center gap-2">
                <h3 class="card-title">{{ __('admin/areas.sections.related_data') }}</h3>
            </div>
        </div>
        <div class="card-body">
            <!-- Tab Navigation -->
            <div>
                <div class="tabs mb-5" data-tabs="true">
                    <button class="tab active" data-tab-toggle="#customers_tab">
                        <i class="ki-filled ki-profile-user"></i>
                        {{ __('admin/areas.tabs.customers') }} ({{ number_format($area->customers_count ?? 0) }})
                    </button>
                    <button class="tab" data-tab-toggle="#service_providers_tab">
                        <i class="ki-filled ki-handshake"></i>
                        {{ __('admin/areas.tabs.service_providers') }}
                        ({{ number_format($area->service_providers_count ?? 0) }})
                    </button>
                </div>

                <!-- Customers Tab -->
                <div class="" id="customers_tab">
                    @if(($area->customers_count ?? 0) > 0)
                        <div class="space-y-3">
                            @foreach($area->customers as $customer)
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="symbol symbol-30px">
                                            <div class="symbol-label bg-light-info text-info text-sm font-semibold">
                                                {{ strtoupper(substr($customer->name, 0, 1)) }}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $customer->name }}</div>
                                            <div class="text-xs text-gray-600">{{ $customer->email }}</div>
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {{ $customer->created_at->format('Y-m-d') }}
                                    </div>
                                </div>
                            @endforeach
                            @if(($area->customers_count ?? 0) > 10)
                                <div class="text-center pt-3">
                                    <span class="text-sm text-gray-600">
                                        {{ __('admin/areas.labels.customers_in_area') }}:
                                        {{ number_format($area->customers_count ?? 0) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="ki-filled ki-profile-user text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600">{{ __('admin/areas.labels.no_customers_in_area') }}</p>
                        </div>
                    @endif
                </div>

                <!-- Service Providers Tab -->
                <div class="hidden" id="service_providers_tab">
                    @if(($area->service_providers_count ?? 0) > 0)
                        <div class="space-y-3">
                            @foreach($area->serviceProviders as $provider)
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="symbol symbol-30px">
                                            <div class="symbol-label bg-light-warning text-warning text-sm font-semibold">
                                                {{ strtoupper(substr($provider->name, 0, 1)) }}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $provider->name }}</div>
                                            <div class="text-xs text-gray-600">{{ $provider->email }}</div>
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {{ $provider->created_at->format('Y-m-d') }}
                                    </div>
                                </div>
                            @endforeach
                            @if(($area->service_providers_count ?? 0) > 10)
                                <div class="text-center pt-3">
                                    <span class="text-sm text-gray-600">
                                        {{ __('admin/areas.labels.service_providers_in_area') }}:
                                        {{ number_format($area->service_providers_count ?? 0) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="ki-filled ki-handshake text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600">{{ __('admin/areas.labels.no_service_providers_in_area') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection