@extends('admin.layouts.app')

@section('title', __('admin/areas.title') . ' - ' . config('app.name'))

@section('page-title', __('admin/areas.page_title'))

@section('page-description', __('admin/areas.page_description'))

@section('page-actions')
    <div class="flex gap-2">
        <a href="{{ route('admin.areas.create') }}" class="btn btn-primary">
            <i class="ki-filled ki-plus"></i>
            {{ __('admin/areas.actions.create') }}
        </a>
    </div>
@endsection

@section('content')
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
        <!-- Total Areas -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/areas.statistics.total_areas') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['total']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                        <i class="ki-filled ki-map text-xl text-primary"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Areas with Customers -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/areas.statistics.areas_with_customers') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['with_customers']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-info-light">
                        <i class="ki-filled ki-badge text-xl text-info"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Areas with Service Providers -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/areas.statistics.areas_with_service_providers') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['with_service_providers']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-warning-light">
                        <i class="ki-filled ki-users text-xl text-warning"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Deleted Areas -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/areas.statistics.deleted_areas') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['trashed']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-danger-light">
                        <i class="ki-filled ki-trash text-xl text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Card -->
    <div class="card mb-5 lg:mb-7.5">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/areas.filters.search_and_filter') }}</h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.areas.index') }}" class="grid grid-cols-1 gap-4">
                <!-- Search -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/areas.actions.search') }}</label>
                    <input type="text" name="search" value="{{ request('search') }}" class="input"
                        placeholder="{{ __('admin/areas.messages.search_placeholder') }}">
                </div>

                <!-- City Filter -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/areas.filters.city') }}</label>
                    <select name="city_id" class="select">
                        <option value="">{{ __('admin/areas.filters.all_cities') }}</option>
                        @foreach($cities as $city)
                            <option value="{{ $city->id }}" {{ request('city_id') == $city->id ? 'selected' : '' }}>
                                {{ $city->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Date From -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/areas.filters.date_from') }}</label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}" class="input">
                </div>

                <!-- Date To -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/areas.filters.date_to') }}</label>
                    <input type="date" name="date_to" value="{{ request('date_to') }}" class="input">
                </div>

                <!-- Sort Options -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/areas.filters.sort_by') }}</label>
                    <select name="sort_by" class="select">
                        <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>
                            {{ __('admin/areas.fields.created_at') }}
                        </option>
                        <option value="updated_at" {{ request('sort_by') == 'updated_at' ? 'selected' : '' }}>
                            {{ __('admin/areas.fields.updated_at') }}
                        </option>
                    </select>
                </div>

                <!-- Filter Actions -->
                <div class="flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="ki-filled ki-magnifier"></i>
                        {{ __('admin/areas.filters.apply_filters') }}
                    </button>
                    <a href="{{ route('admin.areas.index') }}" class="btn btn-light">
                        <i class="ki-filled ki-arrows-circle"></i>
                        {{ __('admin/areas.filters.clear_filters') }}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Areas Data Table -->
    <div class="card min-w-full">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/areas.areas_list') }}</h3>
        </div>

        @if($areas->count() > 0)
            <div class="card-table">
                <table class="table table-border align-middle text-gray-700 font-medium text-sm">
                    <thead>
                        <tr>
                            <th class="min-w-[50px]">{{ __('admin/areas.table.id') }}</th>
                            <th class="min-w-[200px]">{{ __('admin/areas.table.name') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/areas.table.city') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/areas.table.customers_count') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/areas.table.service_providers_count') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/areas.table.created_at') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/areas.table.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($areas as $area)
                            <tr>
                                <td>{{ $area->id }}</td>
                                <td>
                                    <div class="flex flex-col">
                                        <span class="text-sm font-medium text-gray-900">{{ $area->name }}</span>
                                        @if($area->translate('en')->name !== $area->name)
                                            <span class="text-xs text-gray-500">{{ $area->translate('en')->name }}</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center gap-2">
                                        <i class="ki-filled ki-geolocation text-primary text-sm"></i>
                                        <span class="text-sm">{{ $area->city->name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-outline badge-info">{{ number_format($area->customers_count) }}</span>
                                </td>
                                <td>
                                    <span
                                        class="badge badge-outline badge-warning">{{ number_format($area->service_providers_count) }}</span>
                                </td>
                                <td>{{ $area->created_at->format('Y-m-d H:i') }}</td>
                                <td>
                                    <div class="flex gap-2">
                                        <!-- View Button -->
                                        <a href="{{ route('admin.areas.show', $area->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-primary"
                                            title="{{ __('admin/areas.actions.view') }}">
                                            <i class="ki-filled ki-eye"></i>
                                        </a>

                                        <!-- Edit Button -->
                                        <a href="{{ route('admin.areas.edit', $area->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-warning"
                                            title="{{ __('admin/areas.actions.edit') }}">
                                            <i class="ki-filled ki-notepad-edit"></i>
                                        </a>

                                        <!-- Delete Button -->
                                        <form method="POST" action="{{ route('admin.areas.destroy', $area->id) }}"
                                            style="display: inline;"
                                            onsubmit="return confirm('{{ __('admin/areas.messages.delete_confirmation') }}')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-icon btn-outline btn-danger"
                                                title="{{ __('admin/areas.actions.delete') }}">
                                                <i class="ki-filled ki-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($areas->hasPages())
                <div class="card-footer">
                    @include('admin.components.pagination', ['paginator' => $areas])
                </div>
            @endif
        @else
            <div class="card-body text-center py-10">
                <div class="flex flex-col items-center">
                    <i class="ki-filled ki-map text-5xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('admin/areas.messages.no_areas_found') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('admin/areas.page_description') }}</p>
                </div>
            </div>
        @endif
    </div>
@endsection