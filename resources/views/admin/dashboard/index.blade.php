@extends('admin.layouts.app')

@section('title', __('Dashboard') . ' - ' . config('app.name'))

@section('page-title', __('Dashboard'))

@section('page-description', __('Welcome to the admin dashboard'))

@section('page-actions')
<button class="btn btn-primary">
     <i class="ki-filled ki-plus"></i>
     {{ __('Add New') }}
</button>
@endsection

@section('content')
<div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
     <!-- Statistics Cards -->
     <div class="lg:col-span-3">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
               <!-- Total Users -->
               <div class="card">
                    <div class="card-body">
                         <div class="flex items-center justify-between">
                              <div>
                                   <div class="text-2sm font-medium text-gray-600 mb-1">
                                        {{ __('Total Users') }}
                                   </div>
                                   <div class="text-2xl font-semibold text-gray-900">
                                        1,247
                                   </div>
                              </div>
                              <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                                   <i class="ki-filled ki-profile-circle text-xl text-primary"></i>
                              </div>
                         </div>
                         <div class="flex items-center gap-1 mt-3">
                              <span class="badge badge-outline badge-success gap-1">
                                   <i class="ki-filled ki-arrow-up text-2xs"></i>
                                   12%
                              </span>
                              <span class="text-2sm text-gray-600">{{ __('vs last month') }}</span>
                         </div>
                    </div>
               </div>
               
               <!-- Service Providers -->
               <div class="card">
                    <div class="card-body">
                         <div class="flex items-center justify-between">
                              <div>
                                   <div class="text-2sm font-medium text-gray-600 mb-1">
                                        {{ __('Service Providers') }}
                                   </div>
                                   <div class="text-2xl font-semibold text-gray-900">
                                        89
                                   </div>
                              </div>
                              <div class="flex items-center justify-center size-12 rounded-lg bg-success-light">
                                   <i class="ki-filled ki-shop text-xl text-success"></i>
                              </div>
                         </div>
                         <div class="flex items-center gap-1 mt-3">
                              <span class="badge badge-outline badge-success gap-1">
                                   <i class="ki-filled ki-arrow-up text-2xs"></i>
                                   8%
                              </span>
                              <span class="text-2sm text-gray-600">{{ __('vs last month') }}</span>
                         </div>
                    </div>
               </div>
               
               <!-- Total Services -->
               <div class="card">
                    <div class="card-body">
                         <div class="flex items-center justify-between">
                              <div>
                                   <div class="text-2sm font-medium text-gray-600 mb-1">
                                        {{ __('Total Services') }}
                                   </div>
                                   <div class="text-2xl font-semibold text-gray-900">
                                        456
                                   </div>
                              </div>
                              <div class="flex items-center justify-center size-12 rounded-lg bg-info-light">
                                   <i class="ki-filled ki-setting-2 text-xl text-info"></i>
                              </div>
                         </div>
                         <div class="flex items-center gap-1 mt-3">
                              <span class="badge badge-outline badge-success gap-1">
                                   <i class="ki-filled ki-arrow-up text-2xs"></i>
                                   15%
                              </span>
                              <span class="text-2sm text-gray-600">{{ __('vs last month') }}</span>
                         </div>
                    </div>
               </div>
               
               <!-- Active Bookings -->
               <div class="card">
                    <div class="card-body">
                         <div class="flex items-center justify-between">
                              <div>
                                   <div class="text-2sm font-medium text-gray-600 mb-1">
                                        {{ __('Active Bookings') }}
                                   </div>
                                   <div class="text-2xl font-semibold text-gray-900">
                                        23
                                   </div>
                              </div>
                              <div class="flex items-center justify-center size-12 rounded-lg bg-warning-light">
                                   <i class="ki-filled ki-calendar text-xl text-warning"></i>
                              </div>
                         </div>
                         <div class="flex items-center gap-1 mt-3">
                              <span class="badge badge-outline badge-danger gap-1">
                                   <i class="ki-filled ki-arrow-down text-2xs"></i>
                                   3%
                              </span>
                              <span class="text-2sm text-gray-600">{{ __('vs last month') }}</span>
                         </div>
                    </div>
               </div>
          </div>
     </div>
     
     <!-- Recent Activity -->
     <div class="lg:col-span-2">
          <div class="card h-full">
               <div class="card-header">
                    <h3 class="card-title">{{ __('Recent Activity') }}</h3>
                    <div class="flex items-center gap-2">
                         <button class="btn btn-sm btn-light">
                              {{ __('View All') }}
                         </button>
                    </div>
               </div>
               <div class="card-body">
                    <div class="flex flex-col gap-5">
                         <!-- Activity Item -->
                         <div class="flex items-start gap-3.5">
                              <div class="relative">
                                   <img alt="" class="rounded-full size-9" src="{{ asset('assets/media/avatars/300-4.png') }}" />
                                   <span class="size-2.5 badge badge-circle badge-success absolute top-7 end-0.5 ring-1 ring-light transform -translate-y-1/2"></span>
                              </div>
                              <div class="flex flex-col gap-1">
                                   <div class="text-2sm">
                                        <a class="hover:text-primary-active text-gray-900 font-semibold" href="#">John Doe</a>
                                        <span class="text-gray-700">{{ __('registered as a new user') }}</span>
                                   </div>
                                   <span class="flex items-center text-2xs font-medium text-gray-500">
                                        {{ __('2 mins ago') }}
                                   </span>
                              </div>
                         </div>
                         
                         <!-- Activity Item -->
                         <div class="flex items-start gap-3.5">
                              <div class="relative">
                                   <img alt="" class="rounded-full size-9" src="{{ asset('assets/media/avatars/300-1.png') }}" />
                                   <span class="size-2.5 badge badge-circle badge-success absolute top-7 end-0.5 ring-1 ring-light transform -translate-y-1/2"></span>
                              </div>
                              <div class="flex flex-col gap-1">
                                   <div class="text-2sm">
                                        <a class="hover:text-primary-active text-gray-900 font-semibold" href="#">Sarah Wilson</a>
                                        <span class="text-gray-700">{{ __('added a new service') }}</span>
                                   </div>
                                   <span class="flex items-center text-2xs font-medium text-gray-500">
                                        {{ __('5 mins ago') }}
                                   </span>
                              </div>
                         </div>
                         
                         <!-- Activity Item -->
                         <div class="flex items-start gap-3.5">
                              <div class="relative">
                                   <img alt="" class="rounded-full size-9" src="{{ asset('assets/media/avatars/300-2.png') }}" />
                                   <span class="size-2.5 badge badge-circle badge-warning absolute top-7 end-0.5 ring-1 ring-light transform -translate-y-1/2"></span>
                              </div>
                              <div class="flex flex-col gap-1">
                                   <div class="text-2sm">
                                        <a class="hover:text-primary-active text-gray-900 font-semibold" href="#">Mike Johnson</a>
                                        <span class="text-gray-700">{{ __('updated profile information') }}</span>
                                   </div>
                                   <span class="flex items-center text-2xs font-medium text-gray-500">
                                        {{ __('10 mins ago') }}
                                   </span>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     </div>
     
     <!-- Quick Actions -->
     <div class="lg:col-span-1">
          <div class="card h-full">
               <div class="card-header">
                    <h3 class="card-title">{{ __('Quick Actions') }}</h3>
               </div>
               <div class="card-body">
                    <div class="flex flex-col gap-3">
                         <a href="#" class="btn btn-light justify-start">
                              <i class="ki-filled ki-user-plus"></i>
                              {{ __('Add New User') }}
                         </a>
                         <a href="#" class="btn btn-light justify-start">
                              <i class="ki-filled ki-shop"></i>
                              {{ __('Add Service Provider') }}
                         </a>
                         <a href="#" class="btn btn-light justify-start">
                              <i class="ki-filled ki-setting-2"></i>
                              {{ __('Add New Service') }}
                         </a>
                         <a href="#" class="btn btn-light justify-start">
                              <i class="ki-filled ki-document"></i>
                              {{ __('Create Page') }}
                         </a>
                         <a href="#" class="btn btn-light justify-start">
                              <i class="ki-filled ki-notification-status"></i>
                              {{ __('Send Notification') }}
                         </a>
                    </div>
               </div>
          </div>
     </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-5 lg:gap-7.5 mt-5 lg:mt-7.5">
     <!-- User Growth Chart -->
     <div class="card">
          <div class="card-header">
               <h3 class="card-title">{{ __('User Growth') }}</h3>
               <div class="flex items-center gap-2">
                    <select class="select select-sm w-28">
                         <option>{{ __('Last 7 days') }}</option>
                         <option>{{ __('Last 30 days') }}</option>
                         <option>{{ __('Last 90 days') }}</option>
                    </select>
               </div>
          </div>
          <div class="card-body">
               <div id="user_growth_chart" class="h-64"></div>
          </div>
     </div>
     
     <!-- Revenue Chart -->
     <div class="card">
          <div class="card-header">
               <h3 class="card-title">{{ __('Service Categories') }}</h3>
          </div>
          <div class="card-body">
               <div id="service_categories_chart" class="h-64"></div>
          </div>
     </div>
</div>
@endsection

@push('scripts')
<script>
     // Sample chart initialization
     document.addEventListener('DOMContentLoaded', function() {
          // User Growth Chart
          if (document.getElementById('user_growth_chart')) {
               const userGrowthOptions = {
                    chart: {
                         type: 'area',
                         height: 256,
                         toolbar: { show: false }
                    },
                    series: [{
                         name: '{{ __("Users") }}',
                         data: [30, 40, 35, 50, 49, 60, 70]
                    }],
                    xaxis: {
                         categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                    },
                    colors: ['#3E97FF'],
                    fill: {
                         type: 'gradient',
                         gradient: {
                              shadeIntensity: 1,
                              opacityFrom: 0.7,
                              opacityTo: 0.3,
                         }
                    }
               };
               const userGrowthChart = new ApexCharts(document.getElementById('user_growth_chart'), userGrowthOptions);
               userGrowthChart.render();
          }
          
          // Service Categories Chart
          if (document.getElementById('service_categories_chart')) {
               const serviceCategoriesOptions = {
                    chart: {
                         type: 'donut',
                         height: 256
                    },
                    series: [44, 55, 13, 43, 22],
                    labels: ['{{ __("Cleaning") }}', '{{ __("Maintenance") }}', '{{ __("Delivery") }}', '{{ __("Beauty") }}', '{{ __("Others") }}'],
                    colors: ['#3E97FF', '#1BC5BD', '#FFA800', '#F64E60', '#8950FC']
               };
               const serviceCategoriesChart = new ApexCharts(document.getElementById('service_categories_chart'), serviceCategoriesOptions);
               serviceCategoriesChart.render();
          }
     });
</script>
@endpush
