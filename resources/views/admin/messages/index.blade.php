@extends('admin.layouts.app')

@section('title', __('admin/messages.title') . ' - ' . config('app.name'))

@section('page-title', __('admin/messages.page_title'))

@section('page-description', __('admin/messages.page_description'))

@section('content')
    <!-- Statistics Cards -->
    @include('admin.components.messages.statistics-cards', ['statistics' => $statistics])

    <!-- Search and Filters Section -->
    @include('admin.components.messages.search-filters', ['filters' => $filters])

    <!-- Data Table Section -->
    @include('admin.components.messages.data-table', ['messages' => $messages])
@endsection

@push('scripts')
<script>
    // Select All Checkbox functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.message-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Bulk Actions functionality
    function bulkAction(action) {
        const selectedIds = [];
        const checkboxes = document.querySelectorAll('.message-checkbox:checked');
        
        checkboxes.forEach(checkbox => {
            selectedIds.push(checkbox.value);
        });

        if (selectedIds.length === 0) {
            alert('{{ __('admin/messages.messages.no_items_selected') }}');
            return;
        }

        let confirmMessage = '';
        let url = '';
        let method = 'POST';
        let data = {
            _token: '{{ csrf_token() }}',
            ids: selectedIds
        };

        switch (action) {
            case 'mark_read':
                confirmMessage = '{{ __('admin/messages.messages.confirm_bulk_mark_read') }}';
                url = '{{ route('admin.messages.bulk-mark-read') }}';
                data.status = 'read';
                break;
            case 'archive':
                confirmMessage = '{{ __('admin/messages.messages.confirm_bulk_archive') }}';
                url = '{{ route('admin.messages.bulk-archive') }}';
                data.status = 'archived';
                break;
            case 'delete':
                confirmMessage = '{{ __('admin/messages.messages.confirm_bulk_delete') }}';
                url = '{{ route('admin.messages.bulk-delete') }}';
                method = 'DELETE';
                break;
        }

        if (confirm(confirmMessage)) {
            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = url;

            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = '{{ csrf_token() }}';
            form.appendChild(csrfInput);

            // Add method if not POST
            if (method !== 'POST') {
                const methodInput = document.createElement('input');
                methodInput.type = 'hidden';
                methodInput.name = '_method';
                methodInput.value = method;
                form.appendChild(methodInput);
            }

            // Add data
            Object.keys(data).forEach(key => {
                if (key !== '_token') {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = Array.isArray(data[key]) ? JSON.stringify(data[key]) : data[key];
                    form.appendChild(input);
                }
            });

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Auto-refresh statistics every 30 seconds
    setInterval(function() {
        // You can implement AJAX refresh here if needed
    }, 30000);
</script>
@endpush
