@extends('admin.layouts.app')

@section('title', __('admin/messages.details.message_details') . ' - ' . config('app.name'))

@section('page-title', __('admin/messages.details.message_details'))

@section('page-description', __('admin/messages.page_description'))

@section('page-actions')
    <div class="flex items-center gap-2">
        <a href="{{ route('admin.messages.edit', $message->id) }}" class="btn btn-warning">
            <i class="ki-filled ki-notepad-edit"></i>
            {{ __('admin/messages.buttons.edit') }}
        </a>

        <form method="POST" action="{{ route('admin.messages.destroy', $message->id) }}" class="inline-block"
            onsubmit="return confirm('{{ __('admin/messages.messages.confirm_delete') }}')">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-danger">
                <i class="ki-filled ki-trash"></i>
                {{ __('admin/messages.buttons.delete') }}
            </button>
        </form>

        <a href="{{ route('admin.messages.index') }}" class="btn btn-secondary">
            <i class="ki-filled ki-arrow-left"></i>
            {{ __('admin/messages.buttons.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
        <!-- Message Content -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/messages.details.message_content') }}</h3>
                    <div class="card-toolbar">
                        @include('admin.components.messages.status-badge', ['status' => $message->status])
                    </div>
                </div>
                <div class="card-body">
                    <!-- Title -->
                    <div class="mb-6">
                        <label
                            class="form-label text-gray-900 font-semibold mb-2">{{ __('admin/messages.form.title') }}</label>
                        <h2 class="text-xl font-bold text-gray-900">{{ $message->title }}</h2>
                    </div>

                    <!-- Message Content -->
                    <div class="mb-6">
                        <label
                            class="form-label text-gray-900 font-semibold mb-2">{{ __('admin/messages.form.message') }}</label>
                        <div class="bg-gray-50 rounded-lg p-4 border">
                            <p class="text-gray-800 leading-relaxed whitespace-pre-wrap">{{ $message->message }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Information -->
        <div class="lg:col-span-1">
            <!-- Sender Information -->
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/messages.details.sender_information') }}</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 gap-5">
                        <!-- Name -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/messages.form.name') }}</label>
                            <div class="flex-1">
                                <div class="flex items-center gap-2">
                                    <i class="ki-filled ki-profile-circle text-gray-400"></i>
                                    <span class="text-gray-900 font-medium">{{ $message->name }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/messages.form.email') }}</label>
                            <div class="flex-1">
                                <div class="flex items-center gap-2">
                                    <i class="ki-filled ki-sms text-gray-400"></i>
                                    <a href="mailto:{{ $message->email }}" class="text-primary hover:text-primary-dark">
                                        {{ $message->email }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Information -->
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/messages.details.status_information') }}</h3>
                </div>
                <div class="card-body">
                    <!-- Current Status -->
                    <div class="mb-4">
                        <label class="form-label text-gray-600 text-sm mb-1">{{ __('admin/messages.form.status') }}</label>
                        <div>
                            @include('admin.components.messages.status-badge', ['status' => $message->status])
                        </div>
                    </div>

                    <!-- Quick Status Actions -->
                    @if($message->status !== 'read')
                        <form method="POST" action="{{ route('admin.messages.mark-read', $message->id) }}" class="mb-2">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-sm btn-success w-full">
                                <i class="ki-filled ki-check-circle"></i>
                                {{ __('admin/messages.actions.mark_as_read') }}
                            </button>
                        </form>
                    @endif

                    @if($message->status !== 'archived')
                        <form method="POST" action="{{ route('admin.messages.mark-archived', $message->id) }}">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-sm btn-info w-full">
                                <i class="ki-filled ki-archive"></i>
                                {{ __('admin/messages.actions.mark_as_archived') }}
                            </button>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Timestamps -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/messages.details.timestamps') }}</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 gap-5">
                        <!-- Created At -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/messages.form.created_at') }}</label>
                            <div class="flex-1">
                                <div class="flex items-center gap-2">
                                    <i class="ki-filled ki-calendar text-gray-400"></i>
                                    <span class="text-gray-900">{{ $message->created_at->format('Y-m-d H:i:s') }}</span>
                                </div>
                                <span class="text-xs text-gray-500">{{ $message->created_at->diffForHumans() }}</span>
                            </div>
                        </div>

                        <!-- Updated At -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/messages.form.updated_at') }}</label>
                            <div class="flex-1">
                                <div class="flex items-center gap-2">
                                    <i class="ki-filled ki-calendar-edit text-gray-400"></i>
                                    <span class="text-gray-900">{{ $message->updated_at->format('Y-m-d H:i:s') }}</span>
                                </div>
                                <span class="text-xs text-gray-500">{{ $message->updated_at->diffForHumans() }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection