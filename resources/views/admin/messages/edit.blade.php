@extends('admin.layouts.app')

@section('title', __('admin/messages.actions.edit') . ' - ' . config('app.name'))

@section('page-title', __('admin/messages.actions.edit'))

@section('page-description', __('admin/messages.page_description'))

@section('page-actions')
    <a href="{{ route('admin.messages.index') }}" class="btn btn-light">
        <i class="ki-filled ki-arrow-left"></i>
        {{ __('admin/messages.buttons.back') }}
    </a>
    <a href="{{ route('admin.messages.show', $message->id) }}" class="btn btn-light">
        <i class="ki-filled ki-eye"></i>
        {{ __('admin/messages.buttons.view') }}
    </a>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/messages.actions.edit') }} - {{ $message->title }}</h3>
        </div>

        <form method="POST" action="{{ route('admin.messages.update', $message->id) }}">
            @csrf
            @method('PUT')

            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Title -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/messages.form.title') }}</label>
                        <input type="text" name="title" value="{{ old('title', $message->title) }}"
                            class="input @error('title') border-danger @enderror"
                            placeholder="{{ __('admin/messages.form.title') }}">
                        @error('title')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Name -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/messages.form.name') }}</label>
                        <input type="text" name="name" value="{{ old('name', $message->name) }}"
                            class="input @error('name') border-danger @enderror"
                            placeholder="{{ __('admin/messages.form.name') }}">
                        @error('name')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/messages.form.email') }}</label>
                        <input type="email" name="email" value="{{ old('email', $message->email) }}"
                            class="input @error('email') border-danger @enderror"
                            placeholder="{{ __('admin/messages.form.email') }}">
                        @error('email')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/messages.form.status') }}</label>
                        <select name="status" class="select @error('status') border-danger @enderror">
                            <option value="pending" {{ old('status', $message->status) === 'pending' ? 'selected' : '' }}>
                                {{ __('admin/messages.status.pending') }}
                            </option>
                            <option value="read" {{ old('status', $message->status) === 'read' ? 'selected' : '' }}>
                                {{ __('admin/messages.status.read') }}
                            </option>
                            <option value="archived" {{ old('status', $message->status) === 'archived' ? 'selected' : '' }}>
                                {{ __('admin/messages.status.archived') }}
                            </option>
                        </select>
                        @error('status')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Message Content -->
                    <div class="md:col-span-2">
                        <div class="flex flex-col gap-1">
                            <label class="form-label text-gray-900">{{ __('admin/messages.form.message') }}</label>
                            <textarea name="message" rows="8" cols="50"
                                class="textarea @error('message') border-danger @enderror"
                                placeholder="{{ __('admin/messages.form.message') }}">{{ old('message', $message->message) }}</textarea>
                            @error('message')
                                <div class="form-hint text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Timestamps Section -->
                <div class="mt-8">
                    <div class="border-b border-gray-200 pt-4 mb-4">
                        <h4 class="text-lg font-medium mb-3">{{ __('admin/messages.sections.timestamps') }}</h4>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex flex-col gap-1">
                            <label class="form-label text-gray-900">{{ __('admin/messages.form.created_at') }}</label>
                            <input type="text" value="{{ $message->created_at->format('Y-m-d H:i:s') }}" class="input"
                                readonly>
                        </div>

                        <div class="flex flex-col gap-1">
                            <label class="form-label text-gray-900">{{ __('admin/messages.form.updated_at') }}</label>
                            <input type="text" value="{{ $message->updated_at->format('Y-m-d H:i:s') }}" class="input"
                                readonly>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <div class="flex justify-end gap-2">
                            <a href="{{ route('admin.messages.index') }}" class="btn btn-light">
                                {{ __('admin/messages.buttons.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ki-filled ki-check"></i>
                                {{ __('admin/messages.buttons.save') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


@endsection