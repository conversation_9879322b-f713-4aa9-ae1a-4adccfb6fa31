@extends('admin.layouts.app')

@section('title', __('admin/users.create_user') . ' - ' . config('app.name'))

@section('page-title', __('admin/users.create_user'))

@section('page-description', __('admin/users.page_description'))

@section('page-actions')
    <a href="{{ route('admin.users.index') }}" class="btn btn-light">
        <i class="ki-filled ki-arrow-left"></i>
        {{ __('admin/users.actions.back') }}
    </a>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/users.create_user') }}</h3>
        </div>

        <form method="POST" action="{{ route('admin.users.store') }}">
            @csrf

            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Name -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/users.fields.name') }}</label>
                        <input type="text" name="name" value="{{ old('name') }}"
                            class="input @error('name') border-danger @enderror"
                            placeholder="{{ __('admin/users.placeholders.name') }}">
                        @error('name')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/users.fields.email') }}</label>
                        <input type="email" name="email" value="{{ old('email') }}"
                            class="input @error('email') border-danger @enderror"
                            placeholder="{{ __('admin/users.placeholders.email') }}">
                        @error('email')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Mobile -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/users.fields.mobile') }}</label>
                        <input type="text" name="mobile" value="{{ old('mobile') }}"
                            class="input @error('mobile') border-danger @enderror"
                            placeholder="{{ __('admin/users.placeholders.mobile') }}" pattern="05[0-9]{8}"
                            title="{{ __('admin/users.validation.mobile_format') }}">
                        @error('mobile')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                        <div class="form-hint">{{ __('admin/users.validation.mobile_format') }}</div>
                    </div>

                    <!-- Active Status -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/users.fields.is_active') }}</label>
                        <div class="flex items-center gap-2">
                            <label class="switch">
                                <input type="hidden" name="is_active" value="0">
                                <input type="checkbox" name="is_active" value="1" {{ old('is_active', '1') == '1' ? 'checked' : '' }}>
                                <span class="switch-slider"></span>
                            </label>
                            <span class="text-sm text-gray-600">{{ __('admin/users.status.active') }}</span>
                        </div>
                        @error('is_active')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/users.fields.password') }}</label>
                        <input type="password" name="password" class="input @error('password') border-danger @enderror"
                            placeholder="{{ __('admin/users.placeholders.password') }}">
                        @error('password')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                        <div class="form-hint">
                            {{ __('admin/users.labels.password_requirements') }}
                        </div>
                    </div>

                    <!-- Password Confirmation -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/users.fields.password_confirmation') }}</label>
                        <input type="password" name="password_confirmation"
                            class="input @error('password_confirmation') border-danger @enderror"
                            placeholder="{{ __('admin/users.placeholders.password_confirmation') }}">
                        @error('password_confirmation')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="card-footer">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <div class="flex justify-end gap-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-light">
                                {{ __('admin/users.actions.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ki-filled ki-check"></i>
                                {{ __('admin/users.actions.save') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Mobile number formatting
            const mobileInput = document.querySelector('input[name="mobile"]');
            if (mobileInput) {
                mobileInput.addEventListener('input', function (e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 0 && !value.startsWith('05')) {
                        if (value.startsWith('5')) {
                            value = '0' + value;
                        } else if (!value.startsWith('0')) {
                            value = '05' + value;
                        }
                    }
                    if (value.length > 10) {
                        value = value.substring(0, 10);
                    }
                    e.target.value = value;
                });
            }

            // Password strength indicator
            const passwordInput = document.querySelector('input[name="password"]');
            if (passwordInput) {
                passwordInput.addEventListener('input', function (e) {
                    const password = e.target.value;
                    const strength = calculatePasswordStrength(password);
                    // You can add visual feedback here
                });
            }
        });

        function calculatePasswordStrength(password) {
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            return strength;
        }
    </script>
@endpush