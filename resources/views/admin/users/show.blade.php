@extends('admin.layouts.app')

@section('title', __('admin/users.user_details') . ' - ' . config('app.name'))

@section('page-title', __('admin/users.user_details'))

@section('page-description', $user->name)

@section('page-actions')
    <a href="{{ route('admin.users.index') }}" class="btn btn-light">
        <i class="ki-filled ki-arrow-left"></i>
        {{ __('admin/users.actions.back') }}
    </a>
    <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-primary">
        <i class="ki-filled ki-notepad-edit"></i>
        {{ __('admin/users.actions.edit') }}
    </a>
@endsection

@section('content')
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
        <!-- User Profile Card -->
        <div class="lg:col-span-1">
            <div class="card">
                <div class="card-body text-center">
                    <!-- User Avatar -->
                    <div class="symbol symbol-100px mx-auto mb-5">
                        <div class="symbol-label bg-light-primary text-primary text-3xl font-semibold">
                            {{ strtoupper(substr($user->name, 0, 1)) }}
                        </div>
                    </div>

                    <!-- User Name -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $user->name }}</h3>

                    <!-- User Status -->
                    <div class="mb-4">
                        @if($user->is_active)
                            <span class="badge badge-outline badge-success">{{ __('admin/users.status.active') }}</span>
                        @else
                            <span class="badge badge-outline badge-danger">{{ __('admin/users.status.inactive') }}</span>
                        @endif
                    </div>

                    <!-- Quick Actions -->
                    <div class="flex justify-center gap-2">
                        @if($user->is_active)
                            <form method="POST" action="{{ route('admin.users.deactivate', $user->id) }}"
                                style="display: inline;"
                                onsubmit="return confirm('{{ __('admin/users.messages.deactivate_confirmation') }}')">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn btn-sm btn-light">
                                    <i class="ki-filled ki-cross-circle"></i>
                                    {{ __('admin/users.actions.deactivate') }}
                                </button>
                            </form>
                        @else
                            <form method="POST" action="{{ route('admin.users.activate', $user->id) }}" style="display: inline;"
                                onsubmit="return confirm('{{ __('admin/users.messages.activate_confirmation') }}')">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn btn-sm btn-success">
                                    <i class="ki-filled ki-check-circle"></i>
                                    {{ __('admin/users.actions.activate') }}
                                </button>
                            </form>
                        @endif

                        <form method="POST" action="{{ route('admin.users.destroy', $user->id) }}" style="display: inline;"
                            onsubmit="return confirm('{{ __('admin/users.messages.delete_confirmation') }}')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger">
                                <i class="ki-filled ki-trash"></i>
                                {{ __('admin/users.actions.delete') }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Details -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/users.user_details') }}</h3>
                </div>

                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <!-- Name -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/users.fields.name') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $user->name }}</span>
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/users.fields.email') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $user->email }}</span>
                            </div>
                        </div>

                        <!-- Mobile -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/users.fields.mobile') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $user->mobile ?? '-' }}</span>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/users.fields.is_active') }}</label>
                            <div class="flex-1">
                                @if($user->is_active)
                                    <span class="badge badge-outline badge-success">{{ __('admin/users.status.active') }}</span>
                                @else
                                    <span
                                        class="badge badge-outline badge-danger">{{ __('admin/users.status.inactive') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Timestamps Section -->
                        <div class="md:col-span-2">
                            <div class="border-t border-gray-200 pt-4 mt-2 mb-4">
                                <h4 class="text-lg font-medium mb-3">{{ __('admin/users.sections.timestamps') }}</h4>
                            </div>
                        </div>

                        <!-- User ID -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/users.table.id') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">#{{ $user->id }}</span>
                            </div>
                        </div>

                        <!-- Created At -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/users.fields.created_at') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $user->created_at->format('Y-m-d H:i:s') }}</span>
                            </div>
                        </div>

                        <!-- Updated At -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/users.fields.updated_at') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $user->updated_at->format('Y-m-d H:i:s') }}</span>
                            </div>
                        </div>

                        @if($user->email_verified_at)
                            <!-- Email Verified At -->
                            <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                <label class="form-label max-w-56">{{ __('admin/users.labels.email_verified_at') }}</label>
                                <div class="flex-1">
                                    <span
                                        class="text-sm text-gray-900">{{ $user->email_verified_at->format('Y-m-d H:i:s') }}</span>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Additional Information Card -->
            <div class="card mt-5">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/users.sections.additional_information') }}</h3>
                </div>

                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Account Age -->
                        <div class="text-center p-4 bg-light rounded">
                            <div class="text-2xl font-semibold text-primary mb-1">
                                {{ $user->created_at->diffInDays(now()) }}
                            </div>
                            <div class="text-sm text-gray-600">{{ __('admin/users.labels.days_since_registration') }}</div>
                        </div>

                        <!-- Last Update -->
                        <div class="text-center p-4 bg-light rounded">
                            <div class="text-2xl font-semibold text-success mb-1">
                                {{ $user->updated_at->diffInDays(now()) }}
                            </div>
                            <div class="text-sm text-gray-600">{{ __('admin/users.labels.days_since_last_update') }}</div>
                        </div>

                        <!-- Status Duration -->
                        <div class="text-center p-4 bg-light rounded">
                            <div
                                class="text-2xl font-semibold {{ $user->is_active ? 'text-success' : 'text-danger' }} mb-1">
                                {{ $user->is_active ? __('admin/users.status.active') : __('admin/users.status.inactive') }}
                            </div>
                            <div class="text-sm text-gray-600">{{ __('admin/users.labels.current_status') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection