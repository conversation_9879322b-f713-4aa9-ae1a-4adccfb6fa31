@extends('admin.layouts.app')

@section('title', __('admin/users.title') . ' - ' . config('app.name'))

@section('page-title', __('admin/users.page_title'))

@section('page-description', __('admin/users.page_description'))

@section('page-actions')
    <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
        <i class="ki-filled ki-plus"></i>
        {{ __('admin/users.actions.create') }}
    </a>
@endsection

@section('content')
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 grid-cols-3 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
        <!-- Total Users -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/users.statistics.total_users') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['total']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                        <i class="ki-filled ki-profile-circle text-xl text-primary"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/users.statistics.active_users') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['active']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-success-light">
                        <i class="ki-filled ki-check-circle text-xl text-success"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inactive Users -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/users.statistics.inactive_users') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['inactive']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-danger-light">
                        <i class="ki-filled ki-cross-circle text-xl text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters Section -->
    <div class="card mb-5 lg:mb-7.5">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/users.filters.search_and_filter') }}</h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.users.index') }}" class="grid grid-cols-1 gap-4">
                <!-- Search -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/users.actions.search') }}</label>
                    <input type="text" name="search" value="{{ request('search') }}" class="input"
                        placeholder="{{ __('admin/users.messages.search_placeholder') }}">
                </div>

                <!-- Status Filter -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/users.filters.status') }}</label>
                    <select name="is_active" class="select">
                        <option value="">{{ __('admin/users.status.all') }}</option>
                        <option value="1" {{ request('is_active') == '1' ? 'selected' : '' }}>
                            {{ __('admin/users.status.active') }}
                        </option>
                        <option value="0" {{ request('is_active') == '0' ? 'selected' : '' }}>
                            {{ __('admin/users.status.inactive') }}
                        </option>
                    </select>
                </div>

                <!-- Date From -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/users.filters.date_from') }}</label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}" class="input">
                </div>

                <!-- Date To -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/users.filters.date_to') }}</label>
                    <input type="date" name="date_to" value="{{ request('date_to') }}" class="input">
                </div>

                <!-- Filter Actions -->
                <div class="flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="ki-filled ki-magnifier"></i>
                        {{ __('admin/users.filters.apply_filters') }}
                    </button>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-light">
                        <i class="ki-filled ki-arrows-circle"></i>
                        {{ __('admin/users.filters.clear_filters') }}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Data Table -->
    <div class="card min-w-full">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/users.users_list') }}</h3>
        </div>

        @if($users->count() > 0)
            <div class="card-table">
                <table class="table table-border align-middle text-gray-700 font-medium text-sm">
                    <thead>
                        <tr>
                            <th class="min-w-[50px]">{{ __('admin/users.table.id') }}</th>
                            <th class="min-w-[200px]">{{ __('admin/users.table.name') }}</th>
                            <th class="min-w-[200px]">{{ __('admin/users.table.email') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/users.table.mobile') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/users.table.status') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/users.table.created_at') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/users.table.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                            <tr>
                                <td>{{ $user->id }}</td>
                                <td>
                                    <div class="flex items-center gap-2.5">
                                        <div class="symbol symbol-30px">
                                            <div class="symbol-label bg-light-primary text-primary text-sm font-semibold">
                                                {{ strtoupper(substr($user->name, 0, 1)) }}
                                            </div>
                                        </div>
                                        <div class="flex flex-col">
                                            <span class="text-sm font-medium text-gray-900">{{ $user->name }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ $user->email }}</td>
                                <td>{{ $user->mobile ?? '-' }}</td>
                                <td>
                                    @if($user->is_active)
                                        <span class="badge badge-outline badge-success">{{ __('admin/users.status.active') }}</span>
                                    @else
                                        <span class="badge badge-outline badge-danger">{{ __('admin/users.status.inactive') }}</span>
                                    @endif
                                </td>
                                <td>{{ $user->created_at->format('Y-m-d H:i') }}</td>
                                <td>
                                    <div class="flex gap-2">
                                        <!-- View Button -->
                                        <a href="{{ route('admin.users.show', $user->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-primary"
                                            title="{{ __('admin/users.actions.view') }}">
                                            <i class="ki-filled ki-eye"></i>
                                        </a>

                                        <!-- Edit Button -->
                                        <a href="{{ route('admin.users.edit', $user->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-warning"
                                            title="{{ __('admin/users.actions.edit') }}">
                                            <i class="ki-filled ki-notepad-edit"></i>
                                        </a>

                                        <!-- Activate/Deactivate Button -->
                                        @if($user->is_active)
                                            <form method="POST" action="{{ route('admin.users.deactivate', $user->id) }}"
                                                style="display: inline;"
                                                onsubmit="return confirm('{{ __('admin/users.messages.deactivate_confirmation') }}')">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="btn btn-sm btn-icon btn-outline btn-warning"
                                                    title="{{ __('admin/users.actions.deactivate') }}">
                                                    <i class="ki-filled ki-cross-circle"></i>
                                                </button>
                                            </form>
                                        @else
                                            <form method="POST" action="{{ route('admin.users.activate', $user->id) }}"
                                                style="display: inline;"
                                                onsubmit="return confirm('{{ __('admin/users.messages.activate_confirmation') }}')">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="btn btn-sm btn-icon btn-outline btn-success"
                                                    title="{{ __('admin/users.actions.activate') }}">
                                                    <i class="ki-filled ki-check-circle"></i>
                                                </button>
                                            </form>
                                        @endif

                                        <!-- Delete Button -->
                                        <form method="POST" action="{{ route('admin.users.destroy', $user->id) }}"
                                            style="display: inline;"
                                            onsubmit="return confirm('{{ __('admin/users.messages.delete_confirmation') }}')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-icon btn-outline btn-danger"
                                                title="{{ __('admin/users.actions.delete') }}">
                                                <i class="ki-filled ki-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <!-- Pagination -->

            @if($users->hasPages())
                <div class="card-footer">
                    @include('admin.components.pagination', ['paginator' => $users])
                </div>
            @endif
        @else
            <div class="card-body text-center py-10">
                <div class="flex flex-col items-center">
                    <i class="ki-filled ki-file-search text-5xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('admin/users.messages.no_users_found') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('admin/users.page_description') }}</p>
                    <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                        <i class="ki-filled ki-plus"></i>
                        {{ __('admin/users.actions.create') }}
                    </a>
                </div>
            </div>
        @endif
    </div>
@endsection