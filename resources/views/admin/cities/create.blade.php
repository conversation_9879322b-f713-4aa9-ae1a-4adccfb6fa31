@extends('admin.layouts.app')

@section('title', __('admin/cities.create_city') . ' - ' . config('app.name'))

@section('page-title', __('admin/cities.create_city'))

@section('page-description', __('admin/cities.page_description'))

@section('page-actions')
    <div class="flex gap-2">
        <a href="{{ route('admin.cities.index') }}" class="btn btn-light">
            <i class="ki-filled ki-arrow-left"></i>
            {{ __('admin/cities.actions.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/cities.create_city') }}</h3>
        </div>

        <form method="POST" action="{{ route('admin.cities.store') }}">
            @csrf

            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Arabic Name -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/cities.fields.name_ar') }}</label>
                        <input type="text" name="ar[name]" value="{{ old('ar.name') }}"
                            class="input @error('ar.name') border-danger @enderror"
                            placeholder="{{ __('admin/cities.placeholders.name_ar') }}">
                        @error('ar.name')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- English Name -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/cities.fields.name_en') }}</label>
                        <input type="text" name="en[name]" value="{{ old('en.name') }}"
                            class="input @error('en.name') border-danger @enderror"
                            placeholder="{{ __('admin/cities.placeholders.name_en') }}">
                        @error('en.name')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Information Section -->
                <div class="mt-8">
                    <div class="border-b border-gray-200 pt-4 mb-4">
                        <h4 class="text-lg font-medium mb-3">{{ __('admin/cities.sections.basic_information') }}</h4>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center gap-3">
                            <i class="ki-filled ki-information-2 text-2xl text-info"></i>
                            <div>
                                <h5 class="text-sm font-medium text-gray-900 mb-1">
                                    {{ __('admin/cities.labels.multilingual_support') }}
                                </h5>
                                <p class="text-xs text-gray-600">{{ __('admin/cities.page_description') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <div class="flex justify-end gap-2">
                            <a href="{{ route('admin.cities.index') }}" class="btn btn-light">
                                {{ __('admin/cities.actions.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ki-filled ki-check"></i>
                                {{ __('admin/cities.actions.save') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Form validation
            const form = document.querySelector('form');
            const arNameInput = document.querySelector('input[name="ar[name]"]');
            const enNameInput = document.querySelector('input[name="en[name]"]');

            form.addEventListener('submit', function (e) {
                let hasErrors = false;

                // Clear previous errors
                document.querySelectorAll('.text-red-500').forEach(el => el.remove());
                document.querySelectorAll('.border-red-500').forEach(el => el.classList.remove('border-red-500'));

                // Validate Arabic name
                if (!arNameInput.value.trim()) {
                    showError(arNameInput, '{{ __("admin/cities.validation.name_ar_required") }}');
                    hasErrors = true;
                }

                // Validate English name
                if (!enNameInput.value.trim()) {
                    showError(enNameInput, '{{ __("admin/cities.validation.name_en_required") }}');
                    hasErrors = true;
                }

                if (hasErrors) {
                    e.preventDefault();
                }
            });

            function showError(input, message) {
                input.classList.add('border-red-500');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'text-red-500 text-sm mt-1';
                errorDiv.textContent = message;
                input.parentNode.appendChild(errorDiv);
            }
        });
    </script>
@endpush