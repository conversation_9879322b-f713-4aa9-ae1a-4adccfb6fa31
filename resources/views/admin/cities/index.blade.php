@extends('admin.layouts.app')

@section('title', __('admin/cities.title') . ' - ' . config('app.name'))

@section('page-title', __('admin/cities.page_title'))

@section('page-description', __('admin/cities.page_description'))

@section('page-actions')
    <div class="flex gap-2">
        <a href="{{ route('admin.cities.create') }}" class="btn btn-primary">
            <i class="ki-filled ki-plus"></i>
            {{ __('admin/cities.actions.create') }}
        </a>
    </div>
@endsection

@section('content')
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
        <!-- Total Cities -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/cities.statistics.total_cities') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['total']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                        <i class="ki-filled ki-geolocation text-xl text-primary"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cities with Areas -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/cities.statistics.cities_with_areas') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['with_areas']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-success-light">
                        <i class="ki-filled ki-home text-xl text-success"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cities with Customers -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/cities.statistics.cities_with_customers') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['with_customers']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-info-light">
                        <i class="ki-filled ki-directbox-default text-xl text-info"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cities with Service Providers -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2sm font-medium text-gray-600 mb-1">
                            {{ __('admin/cities.statistics.cities_with_service_providers') }}
                        </div>
                        <div class="text-2xl font-semibold text-gray-900">
                            {{ number_format($statistics['with_service_providers']) }}
                        </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-warning-light">
                        <i class="ki-filled ki-shop text-xl text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Card -->
    <div class="card mb-5 lg:mb-7.5">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/cities.filters.search_and_filter') }}</h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.cities.index') }}" class="grid grid-cols-1 gap-4">
                <!-- Search -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/cities.actions.search') }}</label>
                    <input type="text" name="search" value="{{ request('search') }}" class="input"
                        placeholder="{{ __('admin/cities.messages.search_placeholder') }}">
                </div>

                <!-- Date From -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/cities.filters.date_from') }}</label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}" class="input">
                </div>

                <!-- Date To -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/cities.filters.date_to') }}</label>
                    <input type="date" name="date_to" value="{{ request('date_to') }}" class="input">
                </div>

                <!-- Sort Options -->
                <div class="flex flex-col gap-1">
                    <label class="form-label text-gray-900">{{ __('admin/cities.filters.sort_by') }}</label>
                    <select name="sort_by" class="select">
                        <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>
                            {{ __('admin/cities.fields.created_at') }}
                        </option>
                        <option value="updated_at" {{ request('sort_by') == 'updated_at' ? 'selected' : '' }}>
                            {{ __('admin/cities.fields.updated_at') }}
                        </option>
                    </select>
                </div>

                <!-- Filter Actions -->
                <div class="flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="ki-filled ki-magnifier"></i>
                        {{ __('admin/cities.filters.apply_filters') }}
                    </button>
                    <a href="{{ route('admin.cities.index') }}" class="btn btn-light">
                        <i class="ki-filled ki-arrows-circle"></i>
                        {{ __('admin/cities.filters.clear_filters') }}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Cities Data Table -->
    <div class="card min-w-full">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/cities.cities_list') }}</h3>
        </div>

        @if($cities->count() > 0)
            <div class="card-table">
                <table class="table table-border align-middle text-gray-700 font-medium text-sm">
                    <thead>
                        <tr>
                            <th class="min-w-[50px]">{{ __('admin/cities.table.id') }}</th>
                            <th class="min-w-[200px]">{{ __('admin/cities.table.name') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/cities.table.areas_count') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/cities.table.customers_count') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/cities.table.service_providers_count') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/cities.table.created_at') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/cities.table.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($cities as $city)
                            <tr>
                                <td>{{ $city->id }}</td>
                                <td>
                                    <div class="flex flex-col">
                                        <span class="text-sm font-medium text-gray-900">{{ $city->name }}</span>
                                        @if($city->translate('en')->name !== $city->name)
                                            <span class="text-xs text-gray-500">{{ $city->translate('en')->name }}</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-outline badge-primary">{{ number_format($city->areas_count) }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-outline badge-info">{{ number_format($city->customers_count) }}</span>
                                </td>
                                <td>
                                    <span
                                        class="badge badge-outline badge-warning">{{ number_format($city->service_providers_count) }}</span>
                                </td>
                                <td>{{ $city->created_at->format('Y-m-d H:i') }}</td>
                                <td>
                                    <div class="flex gap-2">
                                        <!-- View Button -->
                                        <a href="{{ route('admin.cities.show', $city->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-primary"
                                            title="{{ __('admin/cities.actions.view') }}">
                                            <i class="ki-filled ki-eye"></i>
                                        </a>

                                        <!-- Edit Button -->
                                        <a href="{{ route('admin.cities.edit', $city->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-warning"
                                            title="{{ __('admin/cities.actions.edit') }}">
                                            <i class="ki-filled ki-notepad-edit"></i>
                                        </a>

                                        <!-- Delete Button -->
                                        <form method="POST" action="{{ route('admin.cities.destroy', $city->id) }}"
                                            style="display: inline;"
                                            onsubmit="return confirm('{{ __('admin/cities.messages.delete_confirmation') }}')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-icon btn-outline btn-danger"
                                                title="{{ __('admin/cities.actions.delete') }}">
                                                <i class="ki-filled ki-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($cities->hasPages())
                <div class="card-footer">
                    @include('admin.components.pagination', ['paginator' => $cities])
                </div>
            @endif
        @else
            <div class="card-body text-center py-10">
                <div class="flex flex-col items-center">
                    <i class="ki-filled ki-geolocation text-5xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('admin/cities.messages.no_cities_found') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('admin/cities.page_description') }}</p>
                </div>
            </div>
        @endif
    </div>
@endsection