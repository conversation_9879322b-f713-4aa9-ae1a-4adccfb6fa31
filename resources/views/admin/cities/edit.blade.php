@extends('admin.layouts.app')

@section('title', __('admin/cities.edit_city') . ' - ' . config('app.name'))

@section('page-title', __('admin/cities.edit_city'))

@section('page-description', $city->name)

@section('page-actions')
    <div class="flex gap-2">
        <a href="{{ route('admin.cities.show', $city->id) }}" class="btn btn-light">
            <i class="ki-filled ki-eye"></i>
            {{ __('admin/cities.actions.view') }}
        </a>
        <a href="{{ route('admin.cities.index') }}" class="btn btn-light">
            <i class="ki-filled ki-arrow-left"></i>
            {{ __('admin/cities.actions.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/cities.edit_city') }} - {{ $city->name }}</h3>
        </div>

        <form method="POST" action="{{ route('admin.cities.update', $city->id) }}">
            @csrf
            @method('PUT')

            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Arabic Name -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/cities.fields.name_ar') }}</label>
                        <input type="text" name="ar[name]" value="{{ old('ar.name', $city->translate('ar')->name ?? '') }}"
                            class="input @error('ar.name') border-danger @enderror"
                            placeholder="{{ __('admin/cities.placeholders.name_ar') }}">
                        @error('ar.name')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- English Name -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/cities.fields.name_en') }}</label>
                        <input type="text" name="en[name]" value="{{ old('en.name', $city->translate('en')->name ?? '') }}"
                            class="input @error('en.name') border-danger @enderror"
                            placeholder="{{ __('admin/cities.placeholders.name_en') }}">
                        @error('en.name')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Statistics Section -->
                <div class="mt-8">
                    <div class="border-b border-gray-200 pt-4 mb-4">
                        <h4 class="text-lg font-medium mb-3">{{ __('admin/cities.sections.statistics_information') }}</h4>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Areas Count -->
                        <div class="text-center p-4 bg-primary-light rounded-lg">
                            <i class="ki-filled ki-map text-3xl text-primary mb-2"></i>
                            <div class="text-2xl font-semibold text-gray-900">{{ number_format($city->areas_count) }}</div>
                            <div class="text-sm text-gray-600">{{ __('admin/cities.fields.areas_count') }}</div>
                        </div>

                        <!-- Customers Count -->
                        <div class="text-center p-4 bg-info-light rounded-lg">
                            <i class="ki-filled ki-profile-user text-3xl text-info mb-2"></i>
                            <div class="text-2xl font-semibold text-gray-900">{{ number_format($city->customers_count) }}
                            </div>
                            <div class="text-sm text-gray-600">{{ __('admin/cities.fields.customers_count') }}</div>
                        </div>

                        <!-- Service Providers Count -->
                        <div class="text-center p-4 bg-warning-light rounded-lg">
                            <i class="ki-filled ki-handshake text-3xl text-warning mb-2"></i>
                            <div class="text-2xl font-semibold text-gray-900">
                                {{ number_format($city->service_providers_count) }}
                            </div>
                            <div class="text-sm text-gray-600">{{ __('admin/cities.fields.service_providers_count') }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <div class="flex justify-end gap-2">
                            <a href="{{ route('admin.cities.index') }}" class="btn btn-light">
                                {{ __('admin/cities.actions.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ki-filled ki-check"></i>
                                {{ __('admin/cities.actions.save') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Form validation
            const form = document.querySelector('form');
            const arNameInput = document.querySelector('input[name="ar[name]"]');
            const enNameInput = document.querySelector('input[name="en[name]"]');

            form.addEventListener('submit', function (e) {
                let hasErrors = false;

                // Clear previous errors
                document.querySelectorAll('.text-red-500').forEach(el => el.remove());
                document.querySelectorAll('.border-red-500').forEach(el => el.classList.remove('border-red-500'));

                // Validate Arabic name
                if (!arNameInput.value.trim()) {
                    showError(arNameInput, '{{ __("admin/cities.validation.name_ar_required") }}');
                    hasErrors = true;
                }

                // Validate English name
                if (!enNameInput.value.trim()) {
                    showError(enNameInput, '{{ __("admin/cities.validation.name_en_required") }}');
                    hasErrors = true;
                }

                if (hasErrors) {
                    e.preventDefault();
                }
            });

            function showError(input, message) {
                input.classList.add('border-red-500');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'text-red-500 text-sm mt-1';
                errorDiv.textContent = message;
                input.parentNode.appendChild(errorDiv);
            }
        });
    </script>
@endpush