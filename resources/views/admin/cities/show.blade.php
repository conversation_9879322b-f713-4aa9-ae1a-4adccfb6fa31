@extends('admin.layouts.app')

@section('title', __('admin/cities.city_details') . ' - ' . config('app.name'))

@section('page-title', __('admin/cities.city_details'))

@section('page-description', $city->name)

@section('page-actions')
    <div class="flex gap-2">
        <a href="{{ route('admin.cities.edit', $city->id) }}" class="btn btn-primary">
            <i class="ki-filled ki-notepad-edit"></i>
            {{ __('admin/cities.actions.edit') }}
        </a>
        <a href="{{ route('admin.cities.index') }}" class="btn btn-light">
            <i class="ki-filled ki-arrow-left"></i>
            {{ __('admin/cities.actions.back') }}
        </a>
    </div>
@endsection

@section('content')
    <!-- City Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
        <!-- Basic Information -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/cities.sections.basic_information') }}</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <!-- Arabic Name -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/cities.fields.name_ar') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $city->translate('ar')->name ?? '-' }}</span>
                            </div>
                        </div>

                        <!-- English Name -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/cities.fields.name_en') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ $city->translate('en')->name ?? '-' }}</span>
                            </div>
                        </div>

                        <!-- City ID -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/cities.fields.id') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">#{{ $city->id }}</span>
                            </div>
                        </div>

                        <!-- Areas Count -->
                        <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">{{ __('admin/cities.fields.areas_count') }}</label>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900">{{ number_format($city->areas->count()) }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8">
                        <!-- Timestamps Section -->
                        <div class="border-b border-gray-200 pt-4 mb-4">
                            <h4 class="text-lg font-medium mb-3">{{ __('admin/cities.sections.timestamps') }}</h4>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Created At -->
                            <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                <label class="form-label max-w-56">{{ __('admin/cities.fields.created_at') }}</label>
                                <div class="flex-1">
                                    <span
                                        class="text-sm text-gray-900">{{ $city->created_at->format('Y-m-d H:i:s') }}</span>
                                </div>
                            </div>

                            <!-- Updated At -->
                            <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                <label class="form-label max-w-56">{{ __('admin/cities.fields.updated_at') }}</label>
                                <div class="flex-1">
                                    <span
                                        class="text-sm text-gray-900">{{ $city->updated_at->format('Y-m-d H:i:s') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="lg:col-span-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/cities.sections.statistics_information') }}</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <!-- Areas Count -->
                        <div class="flex items-center justify-between p-3 bg-primary-light rounded-lg">
                            <div class="flex items-center gap-3">
                                <i class="ki-filled ki-map text-primary text-lg"></i>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ __('admin/cities.fields.areas_count') }}
                                </span>
                            </div>
                            <span class="badge badge-primary">{{ number_format($city->areas->count()) }}</span>
                        </div>

                        <!-- Customers Count -->
                        <div class="flex items-center justify-between p-3 bg-info-light rounded-lg mt-4">
                            <div class="flex items-center gap-3">
                                <i class="ki-filled ki-profile-user text-info text-lg"></i>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ __('admin/cities.fields.customers_count') }}
                                </span>
                            </div>
                            <span class="badge badge-info">{{ number_format($city->customers->count()) }}</span>
                        </div>

                        <!-- Service Providers Count -->
                        <div class="flex items-center justify-between p-3 bg-warning-light rounded-lg mt-4">
                            <div class="flex items-center gap-3">
                                <i class="ki-filled ki-handshake text-warning text-lg"></i>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ __('admin/cities.fields.service_providers_count') }}
                                </span>
                            </div>
                            <span class="badge badge-warning">{{ number_format($city->serviceProviders->count()) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Data Tabs -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center gap-2">
                <h3 class="card-title">{{ __('admin/cities.sections.related_data') }}</h3>
            </div>
        </div>
        <div class="card-body">
            <!-- Tab Navigation -->
            <div>
                <div class="tabs mb-5" data-tabs="true">
                    <button class="tab active" data-tab-toggle="#areas_tab">
                        <i class="ki-filled ki-map"></i>
                        {{ __('admin/cities.tabs.areas') }} ({{ number_format($city->areas->count()) }})
                    </button>
                    <button class="tab" data-tab-toggle="#customers_tab">
                        <i class="ki-filled ki-profile-user"></i>
                        {{ __('admin/cities.tabs.customers') }} ({{ number_format($city->customers->count()) }})
                    </button>
                    <button class="tab" data-tab-toggle="#service_providers_tab">
                        <i class="ki-filled ki-handshake"></i>
                        {{ __('admin/cities.tabs.service_providers') }}
                        ({{ number_format($city->serviceProviders->count()) }})
                    </button>
                </div>

                <!-- Areas Tab -->
                <div class="" id="areas_tab">
                    @if($city->areas->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($city->areas as $area)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="text-sm font-medium text-gray-900">{{ $area->name }}</h4>
                                        <a href="{{ route('admin.areas.show', $area->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-primary">
                                            <i class="ki-filled ki-eye"></i>
                                        </a>
                                    </div>
                                    <div class="text-xs text-gray-600">
                                        {{ __('admin/areas.fields.customers_count') }}: {{ $area->customers_count ?? 0 }}
                                    </div>
                                    <div class="text-xs text-gray-600">
                                        {{ __('admin/areas.fields.service_providers_count') }}:
                                        {{ $area->service_providers_count ?? 0 }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="ki-filled ki-map text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600">{{ __('admin/cities.labels.areas_in_city') }}</p>
                        </div>
                    @endif
                </div>

                <!-- Customers Tab -->
                <div class="hidden" id="customers_tab">
                    @if($city->customers->count() > 0)
                        <div class="space-y-3">
                            @foreach($city->customers->take(10) as $customer)
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="symbol symbol-30px">
                                            <div class="symbol-label bg-light-info text-info text-sm font-semibold">
                                                {{ strtoupper(substr($customer->name, 0, 1)) }}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $customer->name }}</div>
                                            <div class="text-xs text-gray-600">{{ $customer->email }}</div>
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {{ $customer->created_at->format('Y-m-d') }}
                                    </div>
                                </div>
                            @endforeach
                            @if($city->customers->count() > 10)
                                <div class="text-center pt-3">
                                    <span class="text-sm text-gray-600">
                                        {{ __('admin/cities.labels.customers_in_city') }}:
                                        {{ number_format($city->customers->count()) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="ki-filled ki-profile-user text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600">{{ __('admin/cities.labels.customers_in_city') }}</p>
                        </div>
                    @endif
                </div>

                <!-- Service Providers Tab -->
                <div class="hidden" id="service_providers_tab">
                    @if($city->serviceProviders->count() > 0)
                        <div class="space-y-3">
                            @foreach($city->serviceProviders->take(10) as $provider)
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="symbol symbol-30px">
                                            <div class="symbol-label bg-light-warning text-warning text-sm font-semibold">
                                                {{ strtoupper(substr($provider->name, 0, 1)) }}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $provider->name }}</div>
                                            <div class="text-xs text-gray-600">{{ $provider->email }}</div>
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {{ $provider->created_at->format('Y-m-d') }}
                                    </div>
                                </div>
                            @endforeach
                            @if($city->serviceProviders->count() > 10)
                                <div class="text-center pt-3">
                                    <span class="text-sm text-gray-600">
                                        {{ __('admin/cities.labels.service_providers_in_city') }}:
                                        {{ number_format($city->serviceProviders->count()) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="ki-filled ki-handshake text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600">{{ __('admin/cities.labels.service_providers_in_city') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection