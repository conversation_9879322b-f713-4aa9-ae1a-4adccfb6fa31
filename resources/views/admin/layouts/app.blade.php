<!DOCTYPE html>
<html class="h-full" data-theme="true" data-theme-mode="light" dir="@direction" lang="@lang">

<head>
     <base href="{{ env('APP_URL')}}">
     <title>@yield('title', __('admin/layout.title'))</title>
     <meta charset="utf-8" />
     <meta content="follow, index" name="robots" />
     <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name="viewport" />
     <meta content="@yield('description', __('admin/layout.description'))" name="description" />
     <meta name="csrf-token" content="{{ csrf_token() }}">

     <!-- Favicons -->
     <link href="{{ asset('assets/media/app/apple-touch-icon.png') }}" rel="apple-touch-icon" sizes="180x180" />
     <link href="{{ asset('assets/media/app/favicon-32x32.png') }}" rel="icon" sizes="32x32" type="image/png" />
     <link href="{{ asset('assets/media/app/favicon-16x16.png') }}" rel="icon" sizes="16x16" type="image/png" />
     <link href="{{ asset('assets/media/app/favicon.ico') }}" rel="shortcut icon" />

     <!-- Fonts -->
     <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
     @isRtl
     <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet" />
     @endisRtl

     <!-- Vendor Styles -->
     <link href="{{ asset('assets/vendors/apexcharts/apexcharts.css') }}" rel="stylesheet" />
     <link href="{{ asset('assets/vendors/keenicons/styles.bundle.css') }}" rel="stylesheet" />
     <link href="{{ asset('assets/vendors/toastr/toastr.min.css') }}" rel="stylesheet" />

     <!-- Main Styles -->
     <link href="{{ asset('assets/css/styles.css') }}" rel="stylesheet" />

     <!-- Additional Styles -->
     @stack('styles')

     @isRtl
     <style>
          .select {
               line-height: inherit;
          }
     </style>
     @endisRtl
</head>

<body class="antialiased flex h-full text-base text-gray-700 [--tw-page-bg:#fefefe] [--tw-page-bg-dark:var(--tw-coal-500)] demo1 sidebar-fixed header-fixed bg-[--tw-page-bg] dark:bg-[--tw-page-bg-dark] {{ $languageService->getDirectionClass() }}"
     style="font-family: @langFont;">

     <!-- Theme Mode Script -->
     <script>
          const defaultThemeMode = 'light'; // light|dark|system
          let themeMode;

          if (document.documentElement) {
               if (localStorage.getItem('theme')) {
                    themeMode = localStorage.getItem('theme');
               } else if (document.documentElement.hasAttribute('data-theme-mode')) {
                    themeMode = document.documentElement.getAttribute('data-theme-mode');
               } else {
                    themeMode = defaultThemeMode;
               }

               if (themeMode === 'system') {
                    themeMode = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
               }

               document.documentElement.classList.add(themeMode);
          }
     </script>

     <!-- Page -->
     <!-- Main -->
     <div class="flex grow">
          <!-- Include Sidebar Component -->
          @include('admin.components.sidebar')

          <!-- Wrapper -->
          <div class="wrapper flex grow flex-col">
               <!-- Include Header Component -->
               @include('admin.components.header')

               <!-- Content -->
               <main class="grow content pt-5" id="content" role="content">
                    <!-- Container -->
                    <div class="container-fixed" id="content_container">
                         <!-- Page Title -->
                         @hasSection('page-title')
                                    <div class="flex flex-wrap items-center justify-between gap-5 pb-7.5">
                                          <div class="flex flex-col justify-center gap-2">
                                                <h1 class="text-xl font-semibold leading-none text-gray-900">
                                                       @yield('page-title')
                                                </h1>
                                                @hasSection('page-description')
                                                                 <div class="flex items-center gap-2 text-sm font-medium text-gray-600">
                                                                         @yield('page-description')
                                                                 </div>
                                                          @endif
                                          </div>
                                          @hasSection('page-actions')
                                                         <div class="flex items-center gap-2.5">
                                                                 @yield('page-actions')
                                                         </div>
                                                   @endif
                                    </div>
                               @endif

                         <!-- Flash Messages -->
                         @include('admin.components.flash-messages')
                    </div>
                    <!-- End of Container -->

                    <!-- Main Content -->
                    <div class="container-fixed">
                         @yield('content')
                    </div>
                    <!-- End of Main Content -->
               </main>
               <!-- End of Content -->

               <!-- Include Footer Component -->
               @include('admin.components.footer')
          </div>
          <!-- End of Wrapper -->
     </div>
     <!-- End of Main -->

     <!-- Search Modal -->
     <div class="modal" data-modal="true" id="search_modal">
          <div class="modal-content max-w-[600px] top-[15%]">
               <div class="modal-header py-4 px-5">
                    <i class="ki-filled ki-magnifier text-gray-700 text-xl"></i>
                    <input class="input px-0 border-none bg-transparent shadow-none ms-2.5" name="query"
                         placeholder="{{ __('admin/layout.search_placeholder') }}" type="text" value="" />
                    <button class="btn btn-sm btn-icon btn-light btn-clear shrink-0" data-modal-dismiss="true">
                         <i class="ki-filled ki-cross"></i>
                    </button>
               </div>
               <div class="modal-body p-0 pb-5">
                    <div class="tabs justify-center px-5 mb-2.5" data-tabs="true" id="search_modal_tabs">
                         <button class="tab active"
                              data-tab-toggle="#search_modal_mixed">{{ __('admin/layout.mixed') }}</button>
                         <button class="tab"
                              data-tab-toggle="#search_modal_settings">{{ __('admin/navigation.settings') }}</button>
                    </div>
                    <div class="scrollable-y-auto" data-scrollable="true" data-scrollable-max-height="auto"
                         data-scrollable-offset="300px">
                         <div class="" id="search_modal_mixed">
                              <div class="flex flex-col gap-2.5">
                                   <div class="text-xs text-gray-600 font-medium pt-2.5 pb-1.5 ps-5">
                                        {{ __('admin/layout.recent_searches') }}
                                   </div>
                                   <div class="menu menu-default p-0 flex-col">
                                        <div class="menu-item">
                                             <a class="menu-link" href="#">
                                                  <span class="menu-icon">
                                                       <i class="ki-filled ki-magnifier"></i>
                                                  </span>
                                                  <span class="menu-title">{{ __('admin/navigation.dashboard') }}</span>
                                             </a>
                                        </div>
                                   </div>
                              </div>
                         </div>
                         <div class="hidden" id="search_modal_settings">
                              <div class="menu menu-default p-0 flex-col">
                                   <div class="menu-item">
                                        <a class="menu-link" href="#">
                                             <span class="menu-icon">
                                                  <i class="ki-filled ki-setting-2"></i>
                                             </span>
                                             <span class="menu-title">{{ __('admin/navigation.settings') }}</span>
                                        </a>
                                   </div>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     </div>
     <!-- End of Search Modal -->

     <!-- Scripts -->
     <script src="{{ asset('assets/js/core.bundle.js') }}"></script>
     <script src="{{ asset('assets/vendors/apexcharts/apexcharts.min.js') }}"></script>
     <script src="{{ asset('assets/js/widgets/general.js') }}"></script>
     <script src="{{ asset('assets/js/layouts/demo1.js') }}"></script>

     <!-- jQuery (required for Toastr) -->
     <script src="https://code.jquery.com/jquery-3.7.1.min.js"
          integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>

     <!-- Toastr Scripts -->
     <script src="{{ asset('assets/vendors/toastr/toastr.min.js') }}"></script>
     <script src="{{ asset('assets/js/admin-toastr.js') }}"></script>

     <!-- Laravel Flash Data for Toastr -->
     <script>
          // Pass Laravel session data to JavaScript
          window.laravelFlashData = {
               success: @json(session('success')),
               error: @json(session('error')),
               warning: @json(session('warning')),
               info: @json(session('info')),
               errors: @json($errors->getMessages())
          };

          // Pass translations for Toastr titles
          window.translations = {
               success: @json(__('admin/messages.success')),
               error: @json(__('admin/messages.error')),
               warning: @json(__('admin/messages.warning')),
               info: @json(__('admin/messages.info')),
               validation_errors: @json(__('admin/messages.validation_errors'))
          };
     </script>

     <!-- Additional Scripts -->
     @stack('scripts')

     <!-- End of Scripts -->
</body>

</html>