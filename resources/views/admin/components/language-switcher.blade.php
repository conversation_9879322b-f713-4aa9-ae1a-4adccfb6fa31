{{-- Language Switcher Component --}}
<div class="dropdown" data-dropdown="true" data-dropdown-placement="bottom-end" data-dropdown-trigger="click">
    <button class="dropdown-toggle btn btn-sm btn-icon btn-light btn-clear" data-dropdown-toggle="#language_dropdown">
        <i class="ki-filled ki-setting-2"></i>
        <span class="text-2xs font-medium text-gray-600 me-1">
            {{ $languageService->getCurrentLanguageNativeName() }}
        </span>
        <i class="ki-filled ki-down text-2xs text-gray-500"></i>
    </button>
    <div class="dropdown-content w-48" id="language_dropdown">
        <div class="menu menu-default" data-menu="true">
            <div class="menu-item">
                <div class="menu-link">
                    <span class="menu-title text-sm font-medium text-gray-700">
                        {{ __('admin/common.choose_language') }}
                    </span>
                </div>
            </div>
            <div class="menu-separator"></div>

            @foreach($languageService->getLanguageSwitchingUrls() as $locale => $language)
                <div class="menu-item">
                    <a href="{{ $language['url'] }}" class="menu-link {{ $language['is_current'] ? 'active' : '' }}"
                        data-locale="{{ $locale }}" data-direction="{{ $language['direction'] }}">
                        <span class="menu-icon">
                            @if($language['is_current'])
                                <i class="ki-filled ki-check text-success text-sm"></i>
                            @else
                                <i class="ki-filled ki-global text-gray-500 text-sm"></i>
                            @endif
                        </span>
                        <span class="menu-title">
                            <span class="text-sm font-medium text-gray-800">{{ $language['name'] }}</span>
                            <span class="text-xs text-gray-600 block">{{ $language['native'] }}</span>
                        </span>
                        @if($language['direction'] === 'rtl')
                            <span class="menu-badge">
                                <span class="badge badge-xs badge-light-primary">RTL</span>
                            </span>
                        @endif
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</div>

{{-- Language Switcher JavaScript --}}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Handle language switching
        const languageLinks = document.querySelectorAll('[data-locale]');

        languageLinks.forEach(link => {
            link.addEventListener('click', function (e) {
                const direction = this.getAttribute('data-direction');
                const locale = this.getAttribute('data-locale');

                // Add loading state
                this.classList.add('loading');

                // Store the selected language preference
                localStorage.setItem('preferred_language', locale);
                localStorage.setItem('preferred_direction', direction);

                // Update document direction immediately for better UX
                document.documentElement.setAttribute('dir', direction);
                document.documentElement.setAttribute('data-direction', direction);
                document.documentElement.setAttribute('data-locale', locale);

                // Add transition class for smooth direction change
                document.body.classList.add('direction-transition');

                // Navigate to the new language URL
                window.location.href = this.href;
            });
        });

        // Handle direction transition
        if (document.body.classList.contains('direction-transition')) {
            setTimeout(() => {
                document.body.classList.remove('direction-transition');
            }, 300);
        }
    });
</script>

{{-- Language Switcher Styles --}}
<style>
    .direction-transition {
        transition: all 0.3s ease-in-out;
    }

    .language-switcher .menu-link.active {
        background-color: var(--tw-primary-light);
        color: var(--tw-primary);
    }

    .language-switcher .menu-link:hover {
        background-color: var(--tw-gray-100);
    }

    .language-switcher .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .language-switcher .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin: -8px 0 0 -8px;
        border: 2px solid transparent;
        border-top: 2px solid var(--tw-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* RTL specific adjustments */
    [dir="rtl"] .language-switcher .menu-icon {
        margin-left: 8px;
        margin-right: 0;
    }

    [dir="rtl"] .language-switcher .menu-badge {
        margin-right: 8px;
        margin-left: 0;
    }
</style>