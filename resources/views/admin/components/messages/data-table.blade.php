{{-- Data Table Component for Messages --}}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">{{ __('admin/messages.title') }}</h3>
        <div class="card-toolbar">
            @if($messages->count() > 0)
                <div class="flex items-center gap-2">
                    <!-- Bulk Actions -->
                    <div class="dropdown" data-dropdown="true">
                        <button class="dropdown-toggle btn btn-sm btn-outline btn-primary" data-dropdown-trigger="click">
                            <i class="ki-filled ki-setting-2"></i>
                            {{ __('admin/messages.actions.bulk_actions') }}
                        </button>
                        <div class="dropdown-content menu menu-default w-48">
                            <div class="menu-item">
                                <button type="button" class="menu-link" onclick="bulkAction('mark_read')">
                                    <span class="menu-icon">
                                        <i class="ki-filled ki-check-circle"></i>
                                    </span>
                                    <span class="menu-title">{{ __('admin/messages.actions.bulk_mark_read') }}</span>
                                </button>
                            </div>
                            <div class="menu-item">
                                <button type="button" class="menu-link" onclick="bulkAction('archive')">
                                    <span class="menu-icon">
                                        <i class="ki-filled ki-archive"></i>
                                    </span>
                                    <span class="menu-title">{{ __('admin/messages.actions.bulk_archive') }}</span>
                                </button>
                            </div>
                            <div class="menu-separator"></div>
                            <div class="menu-item">
                                <button type="button" class="menu-link text-danger" onclick="bulkAction('delete')">
                                    <span class="menu-icon">
                                        <i class="ki-filled ki-trash"></i>
                                    </span>
                                    <span class="menu-title">{{ __('admin/messages.actions.bulk_delete') }}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    @if($messages->count() > 0)
        <div class="card-table">
            <table class="table table-border align-middle text-gray-700 font-medium text-sm">
                <thead>
                    <tr>
                        <th class="w-[50px]">
                            <input type="checkbox" class="checkbox" id="select-all">
                        </th>
                        <th class="min-w-[50px]">{{ __('admin/messages.table.id') }}</th>
                        <th class="min-w-[200px]">{{ __('admin/messages.table.title') }}</th>
                        <th class="min-w-[150px]">{{ __('admin/messages.table.name') }}</th>
                        <th class="min-w-[200px]">{{ __('admin/messages.table.email') }}</th>
                        <th class="min-w-[100px]">{{ __('admin/messages.table.status') }}</th>
                        <th class="min-w-[150px]">{{ __('admin/messages.table.created_at') }}</th>
                        <th class="min-w-[150px]">{{ __('admin/messages.table.actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($messages as $message)
                        <tr>
                            <td>
                                <input type="checkbox" class="checkbox message-checkbox" value="{{ $message->id }}">
                            </td>
                            <td>{{ $message->id }}</td>
                            <td>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-900">{{ Str::limit($message->title, 50) }}</span>
                                    <span class="text-xs text-gray-600">{{ Str::limit($message->message, 80) }}</span>
                                </div>
                            </td>
                            <td>{{ $message->name }}</td>
                            <td>{{ $message->email }}</td>
                            <td>
                                @if($message->status === 'pending')
                                    <span class="badge badge-outline badge-warning">{{ __('admin/messages.status.pending') }}</span>
                                @elseif($message->status === 'read')
                                    <span class="badge badge-outline badge-success">{{ __('admin/messages.status.read') }}</span>
                                @elseif($message->status === 'archived')
                                    <span class="badge badge-outline badge-info">{{ __('admin/messages.status.archived') }}</span>
                                @endif
                            </td>
                            <td>{{ $message->created_at->format('Y-m-d H:i') }}</td>
                            <td>
                                <div class="flex gap-2">
                                    <!-- View Button -->
                                    <a href="{{ route('admin.messages.show', $message->id) }}"
                                        class="btn btn-sm btn-icon btn-outline btn-primary"
                                        title="{{ __('admin/messages.actions.view') }}">
                                        <i class="ki-filled ki-eye"></i>
                                    </a>

                                    <!-- Edit Button -->
                                    <a href="{{ route('admin.messages.edit', $message->id) }}"
                                        class="btn btn-sm btn-icon btn-outline btn-warning"
                                        title="{{ __('admin/messages.actions.edit') }}">
                                        <i class="ki-filled ki-notepad-edit"></i>
                                    </a>

                                    <!-- Delete Button -->
                                    <form method="POST" action="{{ route('admin.messages.destroy', $message->id) }}" 
                                          class="inline-block"
                                          onsubmit="return confirm('{{ __('admin/messages.messages.confirm_delete') }}')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="btn btn-sm btn-icon btn-outline btn-danger"
                                                title="{{ __('admin/messages.actions.delete') }}">
                                            <i class="ki-filled ki-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        @if($messages->hasPages())
            <div class="card-footer">
                @include('admin.components.pagination', ['paginator' => $messages])
            </div>
        @endif
    @else
        <div class="card-body text-center py-10">
            <div class="flex flex-col items-center">
                <i class="ki-filled ki-message-text-2 text-5xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('admin/messages.messages.no_messages_found') }}</h3>
                <p class="text-gray-600 mb-4">{{ __('admin/messages.messages.no_messages_description') }}</p>
            </div>
        </div>
    @endif
</div>
