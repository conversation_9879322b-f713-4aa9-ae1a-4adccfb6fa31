{{-- Search and Filters Component for Messages --}}
<div class="card mb-5 lg:mb-7.5">
    <div class="card-header">
        <h3 class="card-title">{{ __('admin/messages.filters.search_and_filter') }}</h3>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.messages.index') }}" class="grid grid-cols-1 gap-4">
            <!-- Search -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/messages.actions.search') }}</label>
                <input type="text" name="search" value="{{ request('search') }}" class="input"
                    placeholder="{{ __('admin/messages.filters.search_placeholder') }}">
            </div>

            <!-- Status Filter -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/messages.filters.status_filter') }}</label>
                <select name="status" class="select">
                    <option value="">{{ __('admin/messages.filters.all_statuses') }}</option>
                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>
                        {{ __('admin/messages.status.pending') }}
                    </option>
                    <option value="read" {{ request('status') === 'read' ? 'selected' : '' }}>
                        {{ __('admin/messages.status.read') }}
                    </option>
                    <option value="archived" {{ request('status') === 'archived' ? 'selected' : '' }}>
                        {{ __('admin/messages.status.archived') }}
                    </option>
                </select>
            </div>

            <!-- Date From -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/messages.filters.date_from') }}</label>
                <input type="date" name="date_from" value="{{ request('date_from') }}" class="input">
            </div>

            <!-- Date To -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/messages.filters.date_to') }}</label>
                <input type="date" name="date_to" value="{{ request('date_to') }}" class="input">
            </div>

            <!-- Filter Actions -->
            <div class="flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="ki-filled ki-magnifier"></i>
                    {{ __('admin/messages.filters.apply_filters') }}
                </button>

                <a href="{{ route('admin.messages.index') }}" class="btn btn-outline btn-secondary">
                    <i class="ki-filled ki-arrows-circle"></i>
                    {{ __('admin/messages.filters.clear_filters') }}
                </a>

                @if(count(array_filter($filters ?? [])) > 0)
                    <div class="flex items-center gap-2 text-sm text-gray-600">
                        <i class="ki-filled ki-filter text-primary"></i>
                        <span>{{ trans_choice('admin/messages.filters.active_filters', count(array_filter($filters)), ['count' => count(array_filter($filters))]) }}</span>
                    </div>
                @endif
            </div>
        </form>
    </div>
</div>