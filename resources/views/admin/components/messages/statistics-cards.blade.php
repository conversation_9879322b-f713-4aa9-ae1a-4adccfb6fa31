{{-- Statistics Cards Component for Messages --}}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
    <!-- Total Messages Card -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-2xl font-bold text-gray-900">{{ number_format($statistics['total']) }}</span>
                    <span class="text-sm font-medium text-gray-600">{{ __('admin/messages.statistics.total_messages') }}</span>
                </div>
                <div class="flex items-center justify-center w-12 h-12 bg-light-primary rounded-lg">
                    <i class="ki-filled ki-message-text-2 text-xl text-primary"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Messages Card -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-2xl font-bold text-gray-900">{{ number_format($statistics['pending']) }}</span>
                    <span class="text-sm font-medium text-gray-600">{{ __('admin/messages.statistics.pending_messages') }}</span>
                </div>
                <div class="flex items-center justify-center w-12 h-12 bg-light-warning rounded-lg">
                    <i class="ki-filled ki-time text-xl text-warning"></i>
                </div>
            </div>
            @if($statistics['total'] > 0)
                <div class="mt-2">
                    <div class="flex items-center gap-1">
                        <span class="text-xs text-gray-500">
                            {{ round(($statistics['pending'] / $statistics['total']) * 100, 1) }}%
                        </span>
                        <span class="text-xs text-gray-500">{{ __('admin/messages.statistics.of_total') }}</span>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Read Messages Card -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-2xl font-bold text-gray-900">{{ number_format($statistics['read']) }}</span>
                    <span class="text-sm font-medium text-gray-600">{{ __('admin/messages.statistics.read_messages') }}</span>
                </div>
                <div class="flex items-center justify-center w-12 h-12 bg-light-success rounded-lg">
                    <i class="ki-filled ki-check-circle text-xl text-success"></i>
                </div>
            </div>
            @if($statistics['total'] > 0)
                <div class="mt-2">
                    <div class="flex items-center gap-1">
                        <span class="text-xs text-gray-500">
                            {{ round(($statistics['read'] / $statistics['total']) * 100, 1) }}%
                        </span>
                        <span class="text-xs text-gray-500">{{ __('admin/messages.statistics.of_total') }}</span>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Archived Messages Card -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-2xl font-bold text-gray-900">{{ number_format($statistics['archived']) }}</span>
                    <span class="text-sm font-medium text-gray-600">{{ __('admin/messages.statistics.archived_messages') }}</span>
                </div>
                <div class="flex items-center justify-center w-12 h-12 bg-light-info rounded-lg">
                    <i class="ki-filled ki-archive text-xl text-info"></i>
                </div>
            </div>
            @if($statistics['total'] > 0)
                <div class="mt-2">
                    <div class="flex items-center gap-1">
                        <span class="text-xs text-gray-500">
                            {{ round(($statistics['archived'] / $statistics['total']) * 100, 1) }}%
                        </span>
                        <span class="text-xs text-gray-500">{{ __('admin/messages.statistics.of_total') }}</span>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
