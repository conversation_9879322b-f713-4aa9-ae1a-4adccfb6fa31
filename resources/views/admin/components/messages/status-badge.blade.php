{{-- Status Badge Component for Messages --}}
@props(['status'])

@if($status === 'pending')
    <span class="badge badge-outline badge-warning">
        <i class="ki-filled ki-time text-xs me-1"></i>
        {{ __('admin/messages.status.pending') }}
    </span>
@elseif($status === 'read')
    <span class="badge badge-outline badge-success">
        <i class="ki-filled ki-check-circle text-xs me-1"></i>
        {{ __('admin/messages.status.read') }}
    </span>
@elseif($status === 'archived')
    <span class="badge badge-outline badge-info">
        <i class="ki-filled ki-archive text-xs me-1"></i>
        {{ __('admin/messages.status.archived') }}
    </span>
@else
    <span class="badge badge-outline badge-secondary">
        <i class="ki-filled ki-question text-xs me-1"></i>
        {{ ucfirst($status) }}
    </span>
@endif
