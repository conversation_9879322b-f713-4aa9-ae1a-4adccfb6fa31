{{-- Statistics Component for Service Providers --}}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
    <!-- Total Providers -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-2xl font-bold text-gray-900">{{ number_format($statistics['total']) }}</span>
                    <span
                        class="text-sm font-medium text-gray-600">{{ __('admin/providers.statistics.total_providers') }}</span>
                </div>
                <div class="flex items-center justify-center w-12 h-12 bg-light-primary rounded-lg">
                    <i class="ki-filled ki-users text-xl text-primary"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Providers -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-2xl font-bold text-gray-900">{{ number_format($statistics['active']) }}</span>
                    <span
                        class="text-sm font-medium text-gray-600">{{ __('admin/providers.statistics.active_providers') }}</span>
                </div>
                <div class="flex items-center justify-center w-12 h-12 bg-light-success rounded-lg">
                    <i class="ki-filled ki-check-circle text-xl text-success"></i>
                </div>
            </div>
            @if($statistics['total'] > 0)
                <div class="mt-2">
                    <div class="flex items-center gap-1">
                        <span class="text-xs text-gray-500">
                            {{ round(($statistics['active'] / $statistics['total']) * 100, 1) }}%
                        </span>
                        <span class="text-xs text-gray-500">{{ __('admin/providers.statistics.of_total') }}</span>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Inactive Providers -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-2xl font-bold text-gray-900">{{ number_format($statistics['inactive']) }}</span>
                    <span
                        class="text-sm font-medium text-gray-600">{{ __('admin/providers.statistics.inactive_providers') }}</span>
                </div>
                <div class="flex items-center justify-center w-12 h-12 bg-light-danger rounded-lg">
                    <i class="ki-filled ki-cross-circle text-xl text-danger"></i>
                </div>
            </div>
            @if($statistics['total'] > 0)
                <div class="mt-2">
                    <div class="flex items-center gap-1">
                        <span class="text-xs text-gray-500">
                            {{ round(($statistics['inactive'] / $statistics['total']) * 100, 1) }}%
                        </span>
                        <span class="text-xs text-gray-500">{{ __('admin/providers.statistics.of_total') }}</span>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>