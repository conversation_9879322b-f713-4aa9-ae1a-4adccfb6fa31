{{-- Data Table Component for Service Providers --}}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">{{ __('admin/providers.title') }}</h3>
        <div class="card-toolbar">
            @if($providers->count() > 0)
                <div class="flex items-center gap-2">
                    <!-- Bulk Actions -->
                    <div class="dropdown" data-dropdown="true">
                        <button class="dropdown-toggle btn btn-sm btn-outline btn-primary" data-dropdown-trigger="click">
                            <i class="ki-filled ki-setting-2"></i>
                            {{ __('admin/providers.actions.bulk_actions') }}
                        </button>
                        <div class="dropdown-content menu menu-default w-48">
                            <div class="menu-item">
                                <button type="button" class="menu-link" onclick="bulkAction('activate')">
                                    <span class="menu-icon">
                                        <i class="ki-filled ki-check-circle"></i>
                                    </span>
                                    <span class="menu-title">{{ __('admin/providers.actions.bulk_activate') }}</span>
                                </button>
                            </div>
                            <div class="menu-item">
                                <button type="button" class="menu-link" onclick="bulkAction('deactivate')">
                                    <span class="menu-icon">
                                        <i class="ki-filled ki-cross-circle"></i>
                                    </span>
                                    <span class="menu-title">{{ __('admin/providers.actions.bulk_deactivate') }}</span>
                                </button>
                            </div>
                            <div class="menu-separator"></div>
                            <div class="menu-item">
                                <button type="button" class="menu-link text-danger" onclick="bulkAction('delete')">
                                    <span class="menu-icon">
                                        <i class="ki-filled ki-trash"></i>
                                    </span>
                                    <span class="menu-title">{{ __('admin/providers.actions.bulk_delete') }}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
    <div class="card-table">
        @if($providers->count() > 0)
            <div class="scrollable-x-auto">
                <table class="table table-auto table-border">
                    <thead>
                        <tr>
                            <th class="w-[50px]">
                                <input type="checkbox" class="checkbox checkbox-sm" id="select-all">
                            </th>
                            <th class="min-w-[200px]">{{ __('admin/providers.table.provider_info') }}</th>
                            <th class="min-w-[150px]">{{ __('admin/providers.table.location_info') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/providers.fields.rating') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/providers.table.services_info') }}</th>
                            <th class="min-w-[100px]">{{ __('admin/providers.table.status_info') }}</th>
                            <th class="min-w-[120px]">{{ __('admin/providers.fields.created_at') }}</th>
                            <th class="w-[120px]">{{ __('admin/providers.table.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($providers as $provider)
                            <tr>
                                <td>
                                    <input type="checkbox" class="checkbox checkbox-sm row-checkbox"
                                        value="{{ $provider->id }}">
                                </td>
                                <td>
                                    <div class="flex items-center gap-2.5">
                                        @if($provider->logo)
                                            <img src="{{ $provider->logo }}" class="size-9 rounded-full"
                                                alt="{{ $provider->name }}">
                                        @else
                                            <div class="size-9 rounded-full bg-gray-100 flex items-center justify-center">
                                                <i class="ki-filled ki-user text-gray-500"></i>
                                            </div>
                                        @endif
                                        <div class="flex flex-col gap-0.5">
                                            <span class="text-sm font-medium text-gray-900">{{ $provider->name }}</span>
                                            @if($provider->description)
                                                <span
                                                    class="text-xs text-gray-600">{{ Str::limit($provider->description, 50) }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex flex-col gap-0.5">
                                        @if($provider->city)
                                            <span class="text-sm text-gray-900">{{ $provider->city->name }}</span>
                                        @endif
                                        @if($provider->area)
                                            <span class="text-xs text-gray-600">{{ $provider->area->name }}</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center gap-1">
                                        <i class="ki-filled ki-star text-yellow-400 text-sm"></i>
                                        <span class="text-sm font-medium">{{ number_format($provider->rating, 1) }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-outline badge-info">
                                        {{ $provider->services_count ?? 0 }}
                                    </span>
                                </td>
                                <td>
                                    @include('admin.components.providers.status-badge', ['status' => $provider->is_active])
                                </td>
                                <td>
                                    <span class="text-sm text-gray-600">{{ $provider->created_at->format('Y-m-d') }}</span>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <!-- View Button -->
                                        <a href="{{ route('admin.providers.show', $provider->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-primary"
                                            title="{{ __('admin/providers.actions.view') }}">
                                            <i class="ki-filled ki-eye"></i>
                                        </a>

                                        <!-- Edit Button -->
                                        <a href="{{ route('admin.providers.edit', $provider->id) }}"
                                            class="btn btn-sm btn-icon btn-outline btn-warning"
                                            title="{{ __('admin/providers.actions.edit') }}">
                                            <i class="ki-filled ki-notepad-edit"></i>
                                        </a>

                                        <!-- Delete Button -->
                                        <button type="button" class="btn btn-sm btn-icon btn-outline btn-danger"
                                            onclick="deleteProvider({{ $provider->id }})"
                                            title="{{ __('admin/providers.actions.delete') }}">
                                            <i class="ki-filled ki-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($providers->hasPages())
                <div class="card-footer">
                    @include('admin.components.pagination', ['paginator' => $providers])
                </div>
            @endif
        @else
            <div class="flex flex-col items-center justify-center py-10">
                <i class="ki-filled ki-file-search text-gray-400 text-5xl mb-3"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-1">{{ __('admin/providers.messages.no_providers_found') }}
                </h3>
                <p class="text-sm text-gray-600">{{ __('admin/providers.table.no_data') }}</p>
            </div>
        @endif
    </div>
</div>

<!-- Bulk Action Forms -->
<form id="bulk-action-form" method="POST" style="display: none;">
    @csrf
    <input type="hidden" name="ids" id="bulk-ids">
    <input type="hidden" name="status" id="bulk-status">
</form>

<form id="delete-form" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>