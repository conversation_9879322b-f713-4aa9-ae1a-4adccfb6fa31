{{-- Search and Filters Component for Service Providers --}}
<div class="card mb-5 lg:mb-7.5">
    <div class="card-header">
        <h3 class="card-title">{{ __('admin/providers.filters.search_and_filter') }}</h3>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.providers.index') }}" class="grid grid-cols-1 gap-4">
            <!-- Search -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/providers.actions.search') }}</label>
                <input type="text" name="search" value="{{ request('search') }}" class="input"
                    placeholder="{{ __('admin/providers.filters.search_placeholder') }}">
            </div>

            <!-- City Filter -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/providers.filters.city_filter') }}</label>
                <select name="city_id" class="select">
                    <option value="">{{ __('admin/providers.filters.all_cities') }}</option>
                    @foreach($cities as $city)
                        <option value="{{ $city->id }}" {{ request('city_id') == $city->id ? 'selected' : '' }}>
                            {{ $city->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Area Filter -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/providers.filters.area_filter') }}</label>
                <select name="area_id" class="select">
                    <option value="">{{ __('admin/providers.filters.all_areas') }}</option>
                    @foreach($areas as $area)
                        <option value="{{ $area->id }}" {{ request('area_id') == $area->id ? 'selected' : '' }}>
                            {{ $area->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Status Filter -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/providers.filters.status_filter') }}</label>
                <select name="is_active" class="select">
                    <option value="">{{ __('admin/providers.status.all') }}</option>
                    <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>
                        {{ __('admin/providers.status.active') }}
                    </option>
                    <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>
                        {{ __('admin/providers.status.inactive') }}
                    </option>
                </select>
            </div>

            <!-- Date From -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/providers.filters.date_from') }}</label>
                <input type="date" name="date_from" value="{{ request('date_from') }}" class="input">
            </div>

            <!-- Date To -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/providers.filters.date_to') }}</label>
                <input type="date" name="date_to" value="{{ request('date_to') }}" class="input">
            </div>

            <!-- Sort By -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/common.sort_by') }}</label>
                <select name="sort_by" class="select">
                    <option value="created_at" {{ request('sort_by', 'created_at') === 'created_at' ? 'selected' : '' }}>
                        {{ __('admin/providers.fields.created_at') }}
                    </option>
                    <option value="rating" {{ request('sort_by') === 'rating' ? 'selected' : '' }}>
                        {{ __('admin/providers.fields.rating') }}
                    </option>
                </select>
            </div>

            <!-- Sort Direction -->
            <div class="flex flex-col gap-1">
                <label class="form-label text-gray-900">{{ __('admin/common.sort_direction') }}</label>
                <select name="sort_direction" class="select">
                    <option value="desc" {{ request('sort_direction', 'desc') === 'desc' ? 'selected' : '' }}>
                        {{ __('admin/common.descending') }}
                    </option>
                    <option value="asc" {{ request('sort_direction') === 'asc' ? 'selected' : '' }}>
                        {{ __('admin/common.ascending') }}
                    </option>
                </select>
            </div>

            <!-- Filter Actions -->
            <div class="flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="ki-filled ki-magnifier"></i>
                    {{ __('admin/providers.filters.apply_filters') }}
                </button>

                <a href="{{ route('admin.providers.index') }}" class="btn btn-outline btn-secondary">
                    <i class="ki-filled ki-arrows-circle"></i>
                    {{ __('admin/providers.filters.clear_filters') }}
                </a>

                @php
                    $activeFilters = array_filter([
                        request('search'),
                        request('city_id'),
                        request('area_id'),
                        request('is_active'),
                        request('date_from'),
                        request('date_to'),
                        request('sort_by') !== 'created_at' ? request('sort_by') : null,
                        request('sort_direction') !== 'desc' ? request('sort_direction') : null,
                    ]);
                @endphp

                @if(count($activeFilters) > 0)
                    <div class="flex items-center gap-2 text-sm text-gray-600">
                        <i class="ki-filled ki-filter text-primary"></i>
                        <span>{{ trans_choice('admin/providers.filters.active_filters', count($activeFilters), ['count' => count($activeFilters)]) }}</span>
                    </div>
                @endif
            </div>
        </form>
    </div>
</div>