@if ($paginator->hasPages())
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4" role="navigation"
        aria-label="{{ __('admin/pagination.navigation') }}">
        <!-- Pagination Info -->
        <div class="flex items-center gap-2 text-sm text-gray-600 order-2 lg:order-1">
            <span>{{ __('admin/pagination.showing') }}</span>
            @if ($paginator->firstItem())
                <span class="font-medium text-gray-900">{{ number_format($paginator->firstItem()) }}</span>
                <span>{{ __('admin/pagination.to') }}</span>
                <span class="font-medium text-gray-900">{{ number_format($paginator->lastItem()) }}</span>
            @else
                <span class="font-medium text-gray-900">{{ number_format($paginator->count()) }}</span>
            @endif
            <span>{{ __('admin/pagination.of') }}</span>
            <span class="font-medium text-gray-900">{{ number_format($paginator->total()) }}</span>
            <span>{{ __('admin/pagination.results') }}</span>
        </div>

        <!-- Pagination Controls -->
        <div class="flex items-center justify-center lg:justify-end gap-1 order-1 lg:order-2">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <span class="btn btn-sm btn-icon btn-light cursor-not-allowed opacity-50 select-none" aria-disabled="true"
                    aria-label="{{ __('admin/pagination.previous') }}" title="{{ __('admin/pagination.previous') }}">
                    <i class="ki-filled ki-left text-sm rtl:rotate-180"></i>
                </span>
            @else
                <a href="{{ $paginator->previousPageUrl() }}"
                    class="btn btn-sm btn-icon btn-light hover:btn-primary focus:btn-primary transition-colors duration-200"
                    rel="prev" aria-label="{{ __('admin/pagination.previous') }}" title="{{ __('admin/pagination.previous') }}">
                    <i class="ki-filled ki-left text-sm rtl:rotate-180"></i>
                </a>
            @endif

            {{-- Pagination Elements --}}
            @php
                $currentPage = $paginator->currentPage();
                $lastPage = $paginator->lastPage();
                $onEachSide = 2; // Number of pages to show on each side of current page

                // Calculate the range of pages to show
                $start = max(1, $currentPage - $onEachSide);
                $end = min($lastPage, $currentPage + $onEachSide);

                // Adjust start and end to always show a consistent number of pages when possible
                if ($end - $start < ($onEachSide * 2)) {
                    if ($start == 1) {
                        $end = min($lastPage, $start + ($onEachSide * 2));
                    } else {
                        $start = max(1, $end - ($onEachSide * 2));
                    }
                }
            @endphp

            {{-- First page and ellipsis --}}
            @if ($start > 1)
                <a href="{{ $paginator->url(1) }}"
                    class="btn btn-sm btn-light hover:btn-primary focus:btn-primary transition-colors duration-200"
                    aria-label="{{ __('admin/pagination.go_to_page', ['page' => 1]) }}"
                    title="{{ __('admin/pagination.go_to_page', ['page' => 1]) }}">
                    1
                </a>
                @if ($start > 2)
                    <span class="btn btn-sm btn-light cursor-default" aria-disabled="true">...</span>
                @endif
            @endif

            {{-- Page range --}}
            @for ($page = $start; $page <= $end; $page++)
                @if ($page == $currentPage)
                    <span class="btn btn-sm btn-primary font-semibold" aria-current="page"
                        aria-label="{{ __('admin/pagination.current_page', ['page' => $page]) }}"
                        title="{{ __('admin/pagination.current_page', ['page' => $page]) }}">
                        {{ $page }}
                    </span>
                @else
                    <a href="{{ $paginator->url($page) }}"
                        class="btn btn-sm btn-light hover:btn-primary focus:btn-primary transition-colors duration-200"
                        aria-label="{{ __('admin/pagination.go_to_page', ['page' => $page]) }}"
                        title="{{ __('admin/pagination.go_to_page', ['page' => $page]) }}">
                        {{ $page }}
                    </a>
                @endif
            @endfor

            {{-- Last page and ellipsis --}}
            @if ($end < $lastPage)
                @if ($end < $lastPage - 1)
                    <span class="btn btn-sm btn-light cursor-default" aria-disabled="true">...</span>
                @endif
                <a href="{{ $paginator->url($lastPage) }}"
                    class="btn btn-sm btn-light hover:btn-primary focus:btn-primary transition-colors duration-200"
                    aria-label="{{ __('admin/pagination.go_to_page', ['page' => $lastPage]) }}"
                    title="{{ __('admin/pagination.go_to_page', ['page' => $lastPage]) }}">
                    {{ $lastPage }}
                </a>
            @endif

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <a href="{{ $paginator->nextPageUrl() }}"
                    class="btn btn-sm btn-icon btn-light hover:btn-primary focus:btn-primary transition-colors duration-200"
                    rel="next" aria-label="{{ __('admin/pagination.next') }}" title="{{ __('admin/pagination.next') }}">
                    <i class="ki-filled ki-right text-sm rtl:rotate-180"></i>
                </a>
            @else
                <span class="btn btn-sm btn-icon btn-light cursor-not-allowed opacity-50 select-none" aria-disabled="true"
                    aria-label="{{ __('admin/pagination.next') }}" title="{{ __('admin/pagination.next') }}">
                    <i class="ki-filled ki-right text-sm rtl:rotate-180"></i>
                </span>
            @endif
        </div>
    </div>
@endif