@extends('admin.layouts.app')

@section('title', __('admin/providers.title') . ' - ' . config('app.name'))

@section('page-title', __('admin/providers.page_title'))

@section('page-description', __('admin/providers.page_description'))

@section('content')
    <!-- Statistics Cards -->
    @include('admin.components.providers.statistics', ['statistics' => $statistics])

    <!-- Search and Filters Section -->
    @include('admin.components.providers.search-filters', ['cities' => $cities, 'areas' => $areas])

    <!-- Providers Data Table -->
    @include('admin.components.providers.data-table', ['providers' => $providers])
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Select All functionality
            const selectAllCheckbox = document.getElementById('select-all');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');

            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function () {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }

            // Update select all when individual checkboxes change
            rowCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function () {
                    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
                    selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
                });
            });
        });

        // Bulk Actions
        function bulkAction(action) {
            const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

            if (checkedBoxes.length === 0) {
                alert('{{ __("admin/providers.messages.no_items_selected") }}');
                return;
            }

            const ids = Array.from(checkedBoxes).map(cb => cb.value);
            const form = document.getElementById('bulk-action-form');
            const idsInput = document.getElementById('bulk-ids');
            const statusInput = document.getElementById('bulk-status');

            idsInput.value = JSON.stringify(ids);

            let confirmMessage = '';
            let actionUrl = '';

            switch (action) {
                case 'activate':
                    confirmMessage = '{{ __("admin/providers.messages.confirm_bulk_activate") }}';
                    actionUrl = '{{ route("admin.providers.bulk-update-status") }}';
                    statusInput.value = '1';
                    break;
                case 'deactivate':
                    confirmMessage = '{{ __("admin/providers.messages.confirm_bulk_deactivate") }}';
                    actionUrl = '{{ route("admin.providers.bulk-update-status") }}';
                    statusInput.value = '0';
                    break;
                case 'delete':
                    confirmMessage = '{{ __("admin/providers.messages.confirm_bulk_delete") }}';
                    actionUrl = '{{ route("admin.providers.bulk-delete") }}';
                    break;
            }

            if (confirm(confirmMessage)) {
                form.action = actionUrl;
                form.submit();
            }
        }

        // Single Delete
        function deleteProvider(id) {
            if (confirm('{{ __("admin/providers.messages.confirm_delete") }}')) {
                const form = document.getElementById('delete-form');
                form.action = '{{ route("admin.providers.destroy", ":id") }}'.replace(':id', id);
                form.submit();
            }
        }
    </script>
@endpush