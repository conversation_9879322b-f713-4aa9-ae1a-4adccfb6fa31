@extends('admin.layouts.app')

@section('title', __('admin/providers.details') . ' - ' . config('app.name'))

@section('page-title', __('admin/providers.details'))

@section('page-description', __('admin/providers.page_description'))

@section('page-actions')
    <a href="{{ route('admin.providers.index') }}" class="btn btn-light">
        <i class="ki-filled ki-arrow-left"></i>
        {{ __('admin/providers.actions.back') }}
    </a>
    <a href="{{ route('admin.providers.edit', $provider->id) }}" class="btn btn-primary">
        <i class="ki-filled ki-notepad-edit"></i>
        {{ __('admin/providers.actions.edit') }}
    </a>
@endsection

@section('content')

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
        <!-- Main Information -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/providers.form.basic_information') }}</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Provider Name -->
                        <div class="md:col-span-2">
                            <div class="flex items-center gap-2.5 mb-4">
                                @if($provider->logo)
                                    <img src="{{ $provider->logo }}" class="size-16 rounded-lg" alt="{{ $provider->name }}">
                                @else
                                    <div class="size-16 rounded-lg bg-gray-100 flex items-center justify-center">
                                        <i class="ki-filled ki-user text-gray-500 text-2xl"></i>
                                    </div>
                                @endif
                                <div>
                                    <h2 class="text-xl font-semibold text-gray-900">{{ $provider->name }}</h2>
                                    @include('admin.components.providers.status-badge', ['status' => $provider->is_active])
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        @if($provider->description)
                            <div class="md:col-span-2">
                                <label class="form-label">{{ __('admin/providers.fields.description') }}</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <p class="text-sm text-gray-700">{{ $provider->description }}</p>
                                </div>
                            </div>
                        @endif

                        <!-- Rating -->
                        <div>
                            <label class="form-label">{{ __('admin/providers.fields.rating') }}</label>
                            <div class="flex items-center gap-2">
                                <div class="flex items-center gap-1">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i
                                            class="ki-filled ki-star {{ $i <= $provider->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                    @endfor
                                </div>
                                <span class="text-sm font-medium">{{ number_format($provider->rating, 1) }}</span>
                            </div>
                        </div>

                        <!-- Services Count -->
                        <div>
                            <label class="form-label">{{ __('admin/providers.fields.services_count') }}</label>
                            <div class="text-sm text-gray-900">{{ $provider->services_count ?? 0 }}</div>
                        </div>

                        <!-- Created At -->
                        <div>
                            <label class="form-label">{{ __('admin/providers.fields.created_at') }}</label>
                            <div class="text-sm text-gray-900">{{ $provider->created_at->format('Y-m-d H:i') }}</div>
                        </div>

                        <!-- Updated At -->
                        <div>
                            <label class="form-label">{{ __('admin/providers.fields.updated_at') }}</label>
                            <div class="text-sm text-gray-900">{{ $provider->updated_at->format('Y-m-d H:i') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Information -->
        <div class="lg:col-span-1">
            <!-- Location Information -->
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">{{ __('admin/providers.form.location_information') }}</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <!-- City -->
                        @if($provider->city)
                            <div>
                                <label class="form-label">{{ __('admin/providers.fields.city') }}</label>
                                <div class="text-sm text-gray-900">{{ $provider->city->name }}</div>
                            </div>
                        @endif

                        <!-- Area -->
                        @if($provider->area)
                            <div>
                                <label class="form-label">{{ __('admin/providers.fields.area') }}</label>
                                <div class="text-sm text-gray-900">{{ $provider->area->name }}</div>
                            </div>
                        @endif

                        <!-- Coordinates -->
                        @if($provider->latitude && $provider->longitude)
                            <div>
                                <label class="form-label">{{ __('admin/providers.fields.coordinates') }}</label>
                                <div class="text-sm text-gray-900">
                                    {{ $provider->latitude }}, {{ $provider->longitude }}
                                </div>
                                @if($provider->google_maps_url)
                                    <a href="{{ $provider->google_maps_url }}" target="_blank"
                                        class="text-primary text-sm hover:text-primary-active">
                                        <i class="ki-filled ki-geolocation"></i>
                                        {{ __('admin/common.view_on_map') }}
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Working Hours -->
            @if($provider->workingHours && $provider->workingHours->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">{{ __('admin/providers.fields.working_hours') }}</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-2">
                            @foreach($provider->formatted_working_hours as $dayHours)
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium">{{ $dayHours['day'] }}</span>
                                    <span class="text-sm text-gray-600">{{ $dayHours['formatted'] }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection