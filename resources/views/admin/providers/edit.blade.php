@extends('admin.layouts.app')

@section('title', __('admin/providers.edit') . ' - ' . config('app.name'))

@section('page-title', __('admin/providers.edit'))

@section('page-description', __('admin/providers.page_description'))

@section('page-actions')
    <a href="{{ route('admin.providers.index') }}" class="btn btn-light">
        <i class="ki-filled ki-arrow-left"></i>
        {{ __('admin/providers.actions.back') }}
    </a>
    <a href="{{ route('admin.providers.show', $provider->id) }}" class="btn btn-light">
        <i class="ki-filled ki-eye"></i>
        {{ __('admin/providers.actions.view') }}
    </a>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">{{ __('admin/providers.edit') }} - {{ $provider->name }}</h3>
        </div>

        <form method="POST" action="{{ route('admin.providers.update', $provider->id) }}">
            @csrf
            @method('PUT')

            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Arabic Name -->
                    <div class="md:col-span-2">
                        <div class="flex flex-col gap-1">
                            <label class="form-label text-gray-900">{{ __('admin/providers.fields.name_ar') }}</label>
                            <input type="text" name="translations[ar][name]"
                                value="{{ old('translations.ar.name', $provider->translate('ar')->name ?? '') }}"
                                class="input @error('translations.ar.name') border-danger @enderror"
                                placeholder="{{ __('admin/providers.form.name_placeholder') }}">
                            @error('translations.ar.name')
                                <div class="form-hint text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- English Name -->
                    <div class="md:col-span-2">
                        <div class="flex flex-col gap-1">
                            <label class="form-label text-gray-900">{{ __('admin/providers.fields.name_en') }}</label>
                            <input type="text" name="translations[en][name]"
                                value="{{ old('translations.en.name', $provider->translate('en')->name ?? '') }}"
                                class="input @error('translations.en.name') border-danger @enderror"
                                placeholder="{{ __('admin/providers.form.name_placeholder') }}">
                            @error('translations.en.name')
                                <div class="form-hint text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Arabic Description -->
                    <div class="md:col-span-2">
                        <div class="flex flex-col gap-1">
                            <label
                                class="form-label text-gray-900">{{ __('admin/providers.fields.description_ar') }}</label>
                            <textarea name="translations[ar][description]" rows="5" cols="50"
                                class="textarea @error('translations.ar.description') border-danger @enderror"
                                placeholder="{{ __('admin/providers.form.description_placeholder') }}">{{ old('translations.ar.description', $provider->translate('ar')->description ?? '') }}</textarea>
                            @error('translations.ar.description')
                                <div class="form-hint text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- English Description -->
                    <div class="md:col-span-2">
                        <div class="flex flex-col gap-1">
                            <label
                                class="form-label text-gray-900">{{ __('admin/providers.fields.description_en') }}</label>
                            <textarea name="translations[en][description]" rows="5" cols="50"
                                class="textarea @error('translations.en.description') border-danger @enderror"
                                placeholder="{{ __('admin/providers.form.description_placeholder') }}">{{ old('translations.en.description', $provider->translate('en')->description ?? '') }}</textarea>
                            @error('translations.en.description')
                                <div class="form-hint text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Logo -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/providers.fields.logo') }}</label>
                        <input type="text" name="logo" value="{{ old('logo', $provider->logo) }}"
                            class="input @error('logo') border-danger @enderror"
                            placeholder="{{ __('admin/providers.form.logo_placeholder') }}">
                        @error('logo')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Rating -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/providers.fields.rating') }}</label>
                        <input type="text" name="rating" value="{{ old('rating', $provider->rating) }}"
                            class="input @error('rating') border-danger @enderror" readonly>
                        @error('rating')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- City -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/providers.fields.city') }}</label>
                        <select name="city_id" id="city_id" class="select @error('city_id') border-danger @enderror"
                           >
                            <option value="">{{ __('admin/providers.filters.select_city') }} *</option>
                            @foreach($cities as $city)
                                <option value="{{ $city->id }}" {{ old('city_id', $provider->city_id) == $city->id ? 'selected' : '' }}>
                                    {{ $city->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('city_id')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Area -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/providers.fields.area') }}</label>
                        <select name="area_id" id="area_id" class="select @error('area_id') border-danger @enderror"
                           >
                            <option value="">{{ __('admin/providers.filters.select_area') }} *</option>
                            @if(old('city_id', $provider->city_id))
                                @foreach($areas->where('city_id', old('city_id', $provider->city_id)) as $area)
                                    <option value="{{ $area->id }}" {{ old('area_id', $provider->area_id) == $area->id ? 'selected' : '' }}>
                                        {{ $area->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @error('area_id')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                        <div id="area-loading" class="text-sm text-gray-500 mt-1" style="display: none;">
                            <i class="ki-filled ki-loading animate-spin"></i>
                            {{ __('admin/providers.form.loading_areas') }}
                        </div>
                    </div>

                    <!-- Latitude -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/providers.fields.latitude') }}</label>
                        <input type="number" name="latitude" value="{{ old('latitude', $provider->latitude) }}"
                            class="input @error('latitude') border-danger @enderror"
                            placeholder="{{ __('admin/providers.form.latitude_placeholder') }}" step="any">
                        @error('latitude')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Longitude -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/providers.fields.longitude') }}</label>
                        <input type="number" name="longitude" value="{{ old('longitude', $provider->longitude) }}"
                            class="input @error('longitude') border-danger @enderror"
                            placeholder="{{ __('admin/providers.form.longitude_placeholder') }}" step="any">
                        @error('longitude')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Active Status -->
                    <div class="flex flex-col gap-1">
                        <label class="form-label text-gray-900">{{ __('admin/providers.fields.status') }}</label>
                        <div class="flex items-center gap-2">
                            <label class="switch">
                                <input type="hidden" name="is_active" value="0">
                                <input type="checkbox" name="is_active" value="1" {{ old('is_active', $provider->is_active) ? 'checked' : '' }}>
                                <span class="switch-slider"></span>
                            </label>
                            <span class="text-sm text-gray-600">{{ __('admin/providers.fields.is_active') }}</span>
                        </div>
                        @error('is_active')
                            <div class="form-hint text-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Timestamps Section -->
                <div class="mt-8">
                    <div class="border-b border-gray-200 pt-4 mb-4">
                        <h4 class="text-lg font-medium mb-3">{{ __('admin/providers.sections.timestamps') }}</h4>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex flex-col gap-1">
                            <label class="form-label text-gray-900">{{ __('admin/providers.fields.created_at') }}</label>
                            <input type="text" value="{{ $provider->created_at->format('Y-m-d H:i:s') }}" class="input"
                                readonly>
                        </div>

                        <div class="flex flex-col gap-1">
                            <label class="form-label text-gray-900">{{ __('admin/providers.fields.updated_at') }}</label>
                            <input type="text" value="{{ $provider->updated_at->format('Y-m-d H:i:s') }}" class="input"
                                readonly>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <div class="flex justify-end gap-2">
                            <a href="{{ route('admin.providers.index') }}" class="btn btn-light">
                                {{ __('admin/providers.actions.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ki-filled ki-check"></i>
                                {{ __('admin/providers.actions.save') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const citySelect = document.getElementById('city_id');
            const areaSelect = document.getElementById('area_id');
            const areaLoading = document.getElementById('area-loading');
            const areasUrl = '{{ route('admin.providers.areas-by-city') }}';

            // Store original area options for restoration
            const originalAreaOptions = Array.from(areaSelect.options);

            function showLoading() {
                areaLoading.style.display = 'block';
                areaSelect.disabled = true;
            }

            function hideLoading() {
                areaLoading.style.display = 'none';
                areaSelect.disabled = false;
            }

            function clearAreaOptions() {
                areaSelect.innerHTML = '<option value="">{{ __('admin/providers.filters.select_area') }} *</option>';
            }

            function populateAreas(areas) {
                clearAreaOptions();

                if (areas.length === 0) {
                    areaSelect.innerHTML = '<option value="">{{ __('admin/providers.form.no_areas_found') }}</option>';
                    areaSelect.disabled = true;
                    return;
                }

                areas.forEach(function (area) {
                    const option = document.createElement('option');
                    option.value = area.id;
                    option.textContent = area.name;
                    areaSelect.appendChild(option);
                });

                areaSelect.disabled = false;
            }

            function handleCityChange() {
                const cityId = citySelect.value;

                if (!cityId) {
                    clearAreaOptions();
                    areaSelect.innerHTML = '<option value="">{{ __('admin/providers.form.select_city_first') }}</option>';
                    areaSelect.disabled = true;
                    return;
                }

                showLoading();

                // Make AJAX request to fetch areas
                fetch(`${areasUrl}?city_id=${cityId}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        hideLoading();

                        if (data.success) {
                            populateAreas(data.data);
                        } else {
                            clearAreaOptions();
                            areaSelect.innerHTML = '<option value="">{{ __('admin/providers.form.error_loading_areas') }}</option>';
                            areaSelect.disabled = true;
                            console.error('Error loading areas:', data.message);
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        clearAreaOptions();
                        areaSelect.innerHTML = '<option value="">{{ __('admin/providers.form.error_loading_areas') }}</option>';
                        areaSelect.disabled = true;
                        console.error('Error fetching areas:', error);
                    });
            }

            // Add event listener for city change
            citySelect.addEventListener('change', handleCityChange);

            // Initialize area dropdown state on page load
            if (!citySelect.value) {
                areaSelect.innerHTML = '<option value="">{{ __('admin/providers.form.select_city_first') }}</option>';
                areaSelect.disabled = true;
            }

            // Form validation
            const form = citySelect.closest('form');
            if (form) {
                form.addEventListener('submit', function (e) {
                    if (!citySelect.value) {
                        e.preventDefault();
                        alert('{{ __('admin/providers.validation.city_required') }}');
                        citySelect.focus();
                        return false;
                    }

                    if (!areaSelect.value) {
                        e.preventDefault();
                        alert('{{ __('admin/providers.validation.area_required') }}');
                        areaSelect.focus();
                        return false;
                    }
                });
            }
        });
    </script>
@endpush