@extends('admin.layouts.auth')

@section('title', __('admin/auth.login_title'))
@section('description', __('admin/auth.login_description'))



@section('content')
    <form action="{{ route('admin.login.submit') }}" class="card-body flex flex-col gap-5 p-10" id="sign_in_form"
        method="post">
        @csrf



        <!-- Header Section -->
        <div class="text-center mb-2.5">
            <h3 class="text-lg font-medium text-gray-900 leading-none mb-2.5">
                {{ __('admin/auth.sign_in') }}
            </h3>
        </div>

        <!-- Email Field -->
        <div class="flex flex-col gap-1">
            <label class="form-label font-normal text-gray-900">
                {{ __('admin/auth.email') }}
            </label>
            <input class="input @error('email') border-danger @enderror" name="email"
                placeholder="{{ __('admin/auth.email_placeholder') }}" type="email" value="{{ old('email') }}"
                autocomplete="email" />
            @error('email')
                <span class="form-hint text-danger">{{ $message }}</span>
            @enderror
        </div>

        <!-- Password Field -->
        <div class="flex flex-col gap-1">
            <label class="form-label font-normal text-gray-900">
                {{ __('admin/auth.password') }}
            </label>
            <div class="input @error('password') border-danger @enderror" data-toggle-password="true">
                <input name="password" placeholder="{{ __('admin/auth.password_placeholder') }}" type="password"
                    autocomplete="current-password" />
                <button class="btn btn-icon" data-toggle-password-trigger="true" type="button">
                    <i class="ki-filled ki-eye text-gray-500 toggle-password-active:hidden"></i>
                    <i class="ki-filled ki-eye-slash text-gray-500 hidden toggle-password-active:block"></i>
                </button>
            </div>
            @error('password')
                <span class="form-hint text-danger">{{ $message }}</span>
            @enderror
        </div>

        <!-- Remember Me Checkbox -->
        <label class="checkbox-group">
            <input class="checkbox checkbox-sm" name="remember" type="checkbox" value="1" {{ old('remember') ? 'checked' : '' }} />
            <span class="checkbox-label">
                {{ __('admin/auth.remember_me') }}
            </span>
        </label>

        <!-- Submit Button -->
        <button class="btn btn-primary flex justify-center grow" type="submit">
            {{ __('admin/auth.sign_in_button') }}
        </button>

        <!-- Language Toggle Switcher -->
        @php
            use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
            $currentLocale = LaravelLocalization::getCurrentLocale();
            $supportedLocales = LaravelLocalization::getSupportedLocales();

            // Determine the alternative language
            $alternativeLocale = $currentLocale === 'ar' ? 'en' : 'ar';
            $alternativeLanguage = $supportedLocales[$alternativeLocale] ?? null;
            $alternativeUrl = LaravelLocalization::getLocalizedURL($alternativeLocale);
        @endphp

        @if($alternativeLanguage)
            <div class="flex items-center gap-2 mt-6">
                <span class="border-t border-gray-200 w-full"></span>
            </div>

            <div class="text-center mt-4">
                <div class="flex items-center justify-center">
                    <span class="text-2sm text-gray-700 me-1.5 {{ $currentLocale === 'ar' ? 'ml-2' : 'mr-2' }}">
                        {{ $currentLocale === 'ar' ? 'تغيير اللغة؟' : 'Change language?' }}
                    </span>

                    <a href="{{ $alternativeUrl }}" class="btn btn-link">
                        {{ $alternativeLanguage['native'] }}
                    </a>
                </div>
            </div>
        @endif
    </form>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Focus on email field when page loads
            const emailInput = document.querySelector('input[name="email"]');
            if (emailInput) {
                emailInput.focus();
            }
        });
    </script>
@endpush