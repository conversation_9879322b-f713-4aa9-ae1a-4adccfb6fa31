@extends('provider.layouts.app')

@section('title', __('provider/working_hours.title') . ' - ' . config('app.name'))

@section('page-title', __('provider/working_hours.page_title'))

@section('page-description', __('provider/working_hours.page_description'))

@section('page-actions')
<div class="flex items-center gap-2.5">
     <button type="submit" form="working_hours_form" class="btn btn-primary">
          <i class="ki-filled ki-check"></i>
          {{ __('provider/working_hours.actions.save_changes') }}
     </button>
     <button type="button" class="btn btn-light" onclick="resetToDefault()">
          <i class="ki-filled ki-arrows-circle"></i>
          {{ __('provider/working_hours.actions.reset_default') }}
     </button>
</div>
@endsection

@section('content')
<div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
     <!-- Working Hours Form -->
     <div class="lg:col-span-2">
          <div class="card">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/working_hours.sections.schedule_management') }}</h3>
               </div>
               <div class="card-body">
                    <form id="working_hours_form" method="POST" action="{{ route('provider.working-hours.update') }}">
                         @csrf
                         @method('PUT')
                         
                         <div class="flex flex-col gap-5">
                              @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
                                   <div class="flex items-center gap-4 p-4 border border-gray-200 rounded-lg">
                                        <!-- Day Name -->
                                        <div class="min-w-[120px]">
                                             <label class="form-label font-semibold text-gray-900">
                                                  {{ __('provider/working_hours.days.' . $day) }}
                                             </label>
                                        </div>

                                        <!-- Is Working Checkbox -->
                                        <div class="flex items-center gap-2">
                                             <label class="checkbox-group">
                                                  <input class="checkbox" 
                                                       type="checkbox" 
                                                       name="working_days[{{ $day }}][is_working]" 
                                                       value="1"
                                                       onchange="toggleDayInputs('{{ $day }}', this.checked)"
                                                       {{ old("working_days.{$day}.is_working", true) ? 'checked' : '' }} />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/working_hours.fields.is_working') }}
                                                  </span>
                                             </label>
                                        </div>

                                        <!-- Time Inputs -->
                                        <div class="flex items-center gap-2 day-inputs" id="{{ $day }}_inputs">
                                             <div class="flex items-center gap-1">
                                                  <label class="form-label text-sm">
                                                       {{ __('provider/working_hours.fields.from') }}
                                                  </label>
                                                  <input class="input w-24" 
                                                       type="time" 
                                                       name="working_days[{{ $day }}][start_time]" 
                                                       value="{{ old("working_days.{$day}.start_time", '09:00') }}" />
                                             </div>
                                             <span class="text-gray-500">-</span>
                                             <div class="flex items-center gap-1">
                                                  <label class="form-label text-sm">
                                                       {{ __('provider/working_hours.fields.to') }}
                                                  </label>
                                                  <input class="input w-24" 
                                                       type="time" 
                                                       name="working_days[{{ $day }}][end_time]" 
                                                       value="{{ old("working_days.{$day}.end_time", '17:00') }}" />
                                             </div>
                                        </div>

                                        <!-- 24/7 Option -->
                                        <div class="flex items-center gap-2">
                                             <label class="checkbox-group">
                                                  <input class="checkbox" 
                                                       type="checkbox" 
                                                       name="working_days[{{ $day }}][is_24_hours]" 
                                                       value="1"
                                                       onchange="toggle24Hours('{{ $day }}', this.checked)"
                                                       {{ old("working_days.{$day}.is_24_hours", false) ? 'checked' : '' }} />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/working_hours.fields.24_hours') }}
                                                  </span>
                                             </label>
                                        </div>
                                   </div>
                              @endforeach
                         </div>
                    </form>
               </div>
          </div>
     </div>

     <!-- Quick Actions & Status -->
     <div class="lg:col-span-1">
          <!-- Current Status -->
          <div class="card mb-5">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/working_hours.sections.current_status') }}</h3>
               </div>
               <div class="card-body text-center">
                    <div class="mb-4">
                         <div class="flex items-center justify-center size-16 rounded-full bg-success-light mx-auto mb-3">
                              <i class="ki-filled ki-check-circle text-2xl text-success"></i>
                         </div>
                         <h4 class="text-lg font-semibold text-gray-900 mb-1">
                              {{ __('provider/working_hours.status.open') }}
                         </h4>
                         <p class="text-sm text-gray-600">
                              {{ __('provider/working_hours.status.closes_at') }} 17:00
                         </p>
                    </div>
                    <div class="kt-badge kt-badge-outline kt-badge-success">
                         {{ __('provider/working_hours.status.accepting_bookings') }}
                    </div>
               </div>
          </div>

          <!-- Quick Templates -->
          <div class="card">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/working_hours.sections.quick_templates') }}</h3>
               </div>
               <div class="card-body">
                    <div class="flex flex-col gap-2">
                         <button type="button" class="btn btn-light btn-sm justify-start" onclick="applyTemplate('business')">
                              <i class="ki-filled ki-briefcase"></i>
                              {{ __('provider/working_hours.templates.business_hours') }}
                         </button>
                         <button type="button" class="btn btn-light btn-sm justify-start" onclick="applyTemplate('extended')">
                              <i class="ki-filled ki-clock"></i>
                              {{ __('provider/working_hours.templates.extended_hours') }}
                         </button>
                         <button type="button" class="btn btn-light btn-sm justify-start" onclick="applyTemplate('weekend')">
                              <i class="ki-filled ki-calendar-2"></i>
                              {{ __('provider/working_hours.templates.weekends_only') }}
                         </button>
                         <button type="button" class="btn btn-light btn-sm justify-start" onclick="applyTemplate('24_7')">
                              <i class="ki-filled ki-time"></i>
                              {{ __('provider/working_hours.templates.24_7') }}
                         </button>
                    </div>
               </div>
          </div>
     </div>
</div>

<!-- Statistics -->
<div class="card mt-5 lg:mt-7.5">
     <div class="card-header">
          <h3 class="card-title">{{ __('provider/working_hours.sections.availability_statistics') }}</h3>
     </div>
     <div class="card-body">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
               <!-- Total Hours Per Week -->
               <div class="flex items-center gap-3.5">
                    <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                         <i class="ki-filled ki-clock text-xl text-primary"></i>
                    </div>
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/working_hours.statistics.hours_per_week') }}
                         </div>
                         <div class="text-xl font-semibold text-gray-900">
                              40h
                         </div>
                    </div>
               </div>

               <!-- Working Days -->
               <div class="flex items-center gap-3.5">
                    <div class="flex items-center justify-center size-12 rounded-lg bg-success-light">
                         <i class="ki-filled ki-calendar text-xl text-success"></i>
                    </div>
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/working_hours.statistics.working_days') }}
                         </div>
                         <div class="text-xl font-semibold text-gray-900">
                              5 {{ __('provider/working_hours.statistics.days') }}
                         </div>
                    </div>
               </div>

               <!-- Average Daily Hours -->
               <div class="flex items-center gap-3.5">
                    <div class="flex items-center justify-center size-12 rounded-lg bg-info-light">
                         <i class="ki-filled ki-time text-xl text-info"></i>
                    </div>
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/working_hours.statistics.avg_daily_hours') }}
                         </div>
                         <div class="text-xl font-semibold text-gray-900">
                              8h
                         </div>
                    </div>
               </div>

               <!-- Availability Score -->
               <div class="flex items-center gap-3.5">
                    <div class="flex items-center justify-center size-12 rounded-lg bg-warning-light">
                         <i class="ki-filled ki-chart-line text-xl text-warning"></i>
                    </div>
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/working_hours.statistics.availability_score') }}
                         </div>
                         <div class="text-xl font-semibold text-gray-900">
                              85%
                         </div>
                    </div>
               </div>
          </div>
     </div>
</div>
@endsection

@push('scripts')
<script>
     function toggleDayInputs(day, isWorking) {
          const inputs = document.getElementById(day + '_inputs');
          const timeInputs = inputs.querySelectorAll('input[type="time"]');
          const checkbox24 = inputs.parentElement.querySelector('input[name="working_days[' + day + '][is_24_hours]"]');
          
          if (isWorking) {
               inputs.style.display = 'flex';
               timeInputs.forEach(input => input.disabled = false);
               checkbox24.disabled = false;
          } else {
               inputs.style.display = 'none';
               timeInputs.forEach(input => input.disabled = true);
               checkbox24.disabled = true;
               checkbox24.checked = false;
          }
     }

     function toggle24Hours(day, is24Hours) {
          const timeInputs = document.getElementById(day + '_inputs').querySelectorAll('input[type="time"]');
          
          if (is24Hours) {
               timeInputs.forEach(input => {
                    input.disabled = true;
                    input.style.opacity = '0.5';
               });
          } else {
               timeInputs.forEach(input => {
                    input.disabled = false;
                    input.style.opacity = '1';
               });
          }
     }

     function applyTemplate(template) {
          const templates = {
               business: {
                    monday: { is_working: true, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    tuesday: { is_working: true, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    wednesday: { is_working: true, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    thursday: { is_working: true, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    friday: { is_working: true, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    saturday: { is_working: false, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    sunday: { is_working: false, start_time: '09:00', end_time: '17:00', is_24_hours: false }
               },
               extended: {
                    monday: { is_working: true, start_time: '08:00', end_time: '20:00', is_24_hours: false },
                    tuesday: { is_working: true, start_time: '08:00', end_time: '20:00', is_24_hours: false },
                    wednesday: { is_working: true, start_time: '08:00', end_time: '20:00', is_24_hours: false },
                    thursday: { is_working: true, start_time: '08:00', end_time: '20:00', is_24_hours: false },
                    friday: { is_working: true, start_time: '08:00', end_time: '20:00', is_24_hours: false },
                    saturday: { is_working: true, start_time: '10:00', end_time: '18:00', is_24_hours: false },
                    sunday: { is_working: true, start_time: '10:00', end_time: '18:00', is_24_hours: false }
               },
               weekend: {
                    monday: { is_working: false, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    tuesday: { is_working: false, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    wednesday: { is_working: false, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    thursday: { is_working: false, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    friday: { is_working: false, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    saturday: { is_working: true, start_time: '09:00', end_time: '17:00', is_24_hours: false },
                    sunday: { is_working: true, start_time: '09:00', end_time: '17:00', is_24_hours: false }
               },
               '24_7': {
                    monday: { is_working: true, start_time: '00:00', end_time: '23:59', is_24_hours: true },
                    tuesday: { is_working: true, start_time: '00:00', end_time: '23:59', is_24_hours: true },
                    wednesday: { is_working: true, start_time: '00:00', end_time: '23:59', is_24_hours: true },
                    thursday: { is_working: true, start_time: '00:00', end_time: '23:59', is_24_hours: true },
                    friday: { is_working: true, start_time: '00:00', end_time: '23:59', is_24_hours: true },
                    saturday: { is_working: true, start_time: '00:00', end_time: '23:59', is_24_hours: true },
                    sunday: { is_working: true, start_time: '00:00', end_time: '23:59', is_24_hours: true }
               }
          };

          const templateData = templates[template];
          
          Object.keys(templateData).forEach(day => {
               const dayData = templateData[day];
               
               // Set working checkbox
               const workingCheckbox = document.querySelector(`input[name="working_days[${day}][is_working]"]`);
               workingCheckbox.checked = dayData.is_working;
               toggleDayInputs(day, dayData.is_working);
               
               // Set time inputs
               document.querySelector(`input[name="working_days[${day}][start_time]"]`).value = dayData.start_time;
               document.querySelector(`input[name="working_days[${day}][end_time]"]`).value = dayData.end_time;
               
               // Set 24 hours checkbox
               const hours24Checkbox = document.querySelector(`input[name="working_days[${day}][is_24_hours]"]`);
               hours24Checkbox.checked = dayData.is_24_hours;
               toggle24Hours(day, dayData.is_24_hours);
          });
     }

     function resetToDefault() {
          applyTemplate('business');
     }

     // Initialize page
     document.addEventListener('DOMContentLoaded', function() {
          // Initialize all day inputs based on current state
          ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].forEach(day => {
               const workingCheckbox = document.querySelector(`input[name="working_days[${day}][is_working]"]`);
               const hours24Checkbox = document.querySelector(`input[name="working_days[${day}][is_24_hours]"]`);
               
               toggleDayInputs(day, workingCheckbox.checked);
               toggle24Hours(day, hours24Checkbox.checked);
          });
     });
</script>
@endpush
