@extends('provider.layouts.app')

@section('title', __('provider/dashboard.title') . ' - ' . config('app.name'))

@section('page-title', __('provider/dashboard.page_title'))

@section('page-description', __('provider/dashboard.page_description'))

@section('page-actions')
<div class="flex items-center gap-2.5">
     <a href="{{ route('provider.services.create') }}" class="btn btn-primary">
          <i class="ki-filled ki-plus"></i>
          {{ __('provider/dashboard.actions.add_service') }}
     </a>
     <button class="btn btn-light" data-modal-toggle="#quick_actions_modal">
          <i class="ki-filled ki-setting-2"></i>
          {{ __('provider/dashboard.actions.quick_actions') }}
     </button>
</div>
@endsection

@section('content')
<!-- Statistics Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
     <!-- Total Services -->
     <div class="card">
          <div class="card-body">
               <div class="flex items-center justify-between">
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/dashboard.statistics.total_services') }}
                         </div>
                         <div class="text-2xl font-semibold text-gray-900">
                              {{ $statistics['total_services']['count'] ?? 0 }}
                         </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                         <i class="ki-filled ki-setting-2 text-xl text-primary"></i>
                    </div>
               </div>
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline kt-badge-success gap-1">
                         <i class="ki-filled ki-arrow-up text-2xs"></i>
                         {{ __('provider/dashboard.statistics.active') }}
                    </span>
               </div>
          </div>
     </div>

     <!-- Pending Bookings -->
     <div class="card">
          <div class="card-body">
               <div class="flex items-center justify-between">
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/dashboard.statistics.pending_bookings') }}
                         </div>
                         <div class="text-2xl font-semibold text-gray-900">
                              {{ $statistics['active_bookings']['count'] ?? 0 }}
                         </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-warning-light">
                         <i class="ki-filled ki-calendar text-xl text-warning"></i>
                    </div>
               </div>
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline kt-badge-warning gap-1">
                         <i class="ki-filled ki-clock text-2xs"></i>
                         {{ __('provider/dashboard.statistics.awaiting_response') }}
                    </span>
               </div>
          </div>
     </div>

     <!-- Completed Bookings -->
     <div class="card">
          <div class="card-body">
               <div class="flex items-center justify-between">
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/dashboard.statistics.completed_bookings') }}
                         </div>
                         <div class="text-2xl font-semibold text-gray-900">
                              {{ $statistics['completed_bookings']['count'] ?? 0 }}
                         </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-success-light">
                         <i class="ki-filled ki-check-circle text-xl text-success"></i>
                    </div>
               </div>
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline kt-badge-success gap-1">
                         <i class="ki-filled ki-arrow-up text-2xs"></i>
                         {{ __('provider/dashboard.statistics.this_month') }}
                    </span>
               </div>
          </div>
     </div>

     <!-- Average Rating -->
     <div class="card">
          <div class="card-body">
               <div class="flex items-center justify-between">
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/dashboard.statistics.average_rating') }}
                         </div>
                         <div class="text-2xl font-semibold text-gray-900">
                              {{ number_format($statistics['average_rating']['rating'] ?? 0, 1) }}
                         </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-info-light">
                         <i class="ki-filled ki-star text-xl text-info"></i>
                    </div>
               </div>
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline kt-badge-info gap-1">
                         <i class="ki-filled ki-star text-2xs"></i>
                         {{ $statistics['average_rating']['total_reviews'] ?? 0 }} {{ __('provider/dashboard.statistics.reviews') }}
                    </span>
               </div>
          </div>
     </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
     <!-- Recent Bookings -->
     <div class="lg:col-span-2">
          <div class="card h-full">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/dashboard.recent_bookings.title') }}</h3>
                    <div class="flex items-center gap-2">
                         <a href="{{ route('provider.bookings.index') }}" class="btn btn-sm btn-light">
                              {{ __('provider/dashboard.recent_bookings.view_all') }}
                         </a>
                    </div>
               </div>
               <div class="card-body">
                    @if(isset($recentBookings) && $recentBookings->count() > 0)
                         <div class="flex flex-col gap-5">
                              @foreach($recentBookings as $booking)
                                   <div class="flex items-start gap-3.5">
                                        <div class="relative">
                                             @if($booking->customer->image)
                                                  <img alt="{{ $booking->customer->name }}" class="rounded-full size-9" 
                                                       src="{{ asset('storage/' . $booking->customer->image) }}" />
                                             @else
                                                  <div class="flex items-center justify-center size-9 rounded-full bg-primary-light text-primary">
                                                       {{ strtoupper(substr($booking->customer->name, 0, 1)) }}
                                                  </div>
                                             @endif
                                             <span class="size-2.5 kt-badge kt-badge-circle kt-badge-{{ $booking->status === 'pending' ? 'warning' : ($booking->status === 'completed' ? 'success' : 'info') }} absolute top-7 end-0.5 ring-1 ring-light transform -translate-y-1/2"></span>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                             <div class="text-2sm">
                                                  <a class="hover:text-primary-active text-gray-900 font-semibold" href="#">
                                                       {{ $booking->customer->name }}
                                                  </a>
                                                  <span class="text-gray-700">{{ __('provider/dashboard.recent_bookings.booked') }}</span>
                                                  <span class="text-gray-900 font-medium">{{ $booking->service->name }}</span>
                                             </div>
                                             <span class="flex items-center text-2xs font-medium text-gray-500">
                                                  {{ $booking->created_at->diffForHumans() }}
                                             </span>
                                        </div>
                                   </div>
                              @endforeach
                         </div>
                    @else
                         <div class="text-center py-10">
                              <i class="ki-filled ki-calendar-2 text-4xl text-gray-300 mb-3"></i>
                              <p class="text-gray-500">{{ __('provider/dashboard.recent_bookings.no_bookings') }}</p>
                         </div>
                    @endif
               </div>
          </div>
     </div>

     <!-- Quick Actions -->
     <div class="lg:col-span-1">
          <div class="card h-full">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/dashboard.quick_actions.title') }}</h3>
               </div>
               <div class="card-body">
                    <div class="flex flex-col gap-3">
                         <a href="{{ route('provider.services.create') }}" class="btn btn-light justify-start">
                              <i class="ki-filled ki-plus"></i>
                              {{ __('provider/dashboard.quick_actions.add_service') }}
                         </a>
                         <a href="{{ route('provider.working-hours.index') }}" class="btn btn-light justify-start">
                              <i class="ki-filled ki-calendar"></i>
                              {{ __('provider/dashboard.quick_actions.manage_hours') }}
                         </a>
                         <a href="{{ route('provider.profile.basic-info') }}" class="btn btn-light justify-start">
                              <i class="ki-filled ki-profile-circle"></i>
                              {{ __('provider/dashboard.quick_actions.update_profile') }}
                         </a>
                         <a href="{{ route('provider.bookings.index') }}" class="btn btn-light justify-start">
                              <i class="ki-filled ki-calendar-2"></i>
                              {{ __('provider/dashboard.quick_actions.view_bookings') }}
                         </a>
                         <a href="{{ route('provider.settings.account') }}" class="btn btn-light justify-start">
                              <i class="ki-filled ki-setting-3"></i>
                              {{ __('provider/dashboard.quick_actions.account_settings') }}
                         </a>
                    </div>
               </div>
          </div>
     </div>
</div>

<!-- Performance Chart Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-5 lg:gap-7.5 mt-5 lg:mt-7.5">
     <!-- Bookings Chart -->
     <div class="card">
          <div class="card-header">
               <h3 class="card-title">{{ __('provider/dashboard.charts.bookings_overview') }}</h3>
               <div class="flex items-center gap-2">
                    <select class="select select-sm w-28">
                         <option>{{ __('provider/dashboard.charts.last_7_days') }}</option>
                         <option>{{ __('provider/dashboard.charts.last_30_days') }}</option>
                         <option>{{ __('provider/dashboard.charts.last_90_days') }}</option>
                    </select>
               </div>
          </div>
          <div class="card-body">
               <div id="bookings_chart" class="h-64"></div>
          </div>
     </div>

     <!-- Services Performance -->
     <div class="card">
          <div class="card-header">
               <h3 class="card-title">{{ __('provider/dashboard.charts.services_performance') }}</h3>
          </div>
          <div class="card-body">
               <div id="services_performance_chart" class="h-64"></div>
          </div>
     </div>
</div>
@endsection

@push('scripts')
<script>
     // Sample chart initialization
     document.addEventListener('DOMContentLoaded', function() {
          // Bookings Chart
          if (document.getElementById('bookings_chart')) {
               const bookingsOptions = {
                    chart: {
                         type: 'area',
                         height: 256,
                         toolbar: { show: false }
                    },
                    series: [{
                         name: '{{ __("provider/dashboard.charts.bookings") }}',
                         data: [10, 15, 12, 20, 18, 25, 30]
                    }],
                    xaxis: {
                         categories: ['{{ __("provider/dashboard.charts.mon") }}', '{{ __("provider/dashboard.charts.tue") }}', '{{ __("provider/dashboard.charts.wed") }}', '{{ __("provider/dashboard.charts.thu") }}', '{{ __("provider/dashboard.charts.fri") }}', '{{ __("provider/dashboard.charts.sat") }}', '{{ __("provider/dashboard.charts.sun") }}']
                    },
                    colors: ['#3E97FF'],
                    fill: {
                         type: 'gradient',
                         gradient: {
                              shadeIntensity: 1,
                              opacityFrom: 0.7,
                              opacityTo: 0.3,
                         }
                    }
               };
               const bookingsChart = new ApexCharts(document.getElementById('bookings_chart'), bookingsOptions);
               bookingsChart.render();
          }

          // Services Performance Chart
          if (document.getElementById('services_performance_chart')) {
               const servicesOptions = {
                    chart: {
                         type: 'donut',
                         height: 256
                    },
                    series: [44, 55, 13, 43],
                    labels: ['{{ __("provider/dashboard.charts.cleaning") }}', '{{ __("provider/dashboard.charts.maintenance") }}', '{{ __("provider/dashboard.charts.delivery") }}', '{{ __("provider/dashboard.charts.others") }}'],
                    colors: ['#3E97FF', '#1BC5BD', '#FFA800', '#F64E60']
               };
               const servicesChart = new ApexCharts(document.getElementById('services_performance_chart'), servicesOptions);
               servicesChart.render();
          }
     });
</script>
@endpush
