@extends('provider.layouts.app')

@section('title', __('provider/settings.account.title') . ' - ' . config('app.name'))

@section('page-title', __('provider/settings.account.page_title'))

@section('page-description', __('provider/settings.account.page_description'))

@section('content')
<div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
     <!-- Settings Navigation -->
     <div class="lg:col-span-1">
          <div class="card">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/settings.account.sections.settings_menu') }}</h3>
               </div>
               <div class="card-body p-0">
                    <div class="menu menu-default">
                         <div class="menu-item">
                              <a class="menu-link active" href="#password-section" onclick="showSection('password')">
                                   <span class="menu-icon">
                                        <i class="ki-filled ki-lock"></i>
                                   </span>
                                   <span class="menu-title">{{ __('provider/settings.account.menu.password') }}</span>
                              </a>
                         </div>
                         <div class="menu-item">
                              <a class="menu-link" href="#notifications-section" onclick="showSection('notifications')">
                                   <span class="menu-icon">
                                        <i class="ki-filled ki-notification-on"></i>
                                   </span>
                                   <span class="menu-title">{{ __('provider/settings.account.menu.notifications') }}</span>
                              </a>
                         </div>
                         <div class="menu-item">
                              <a class="menu-link" href="#privacy-section" onclick="showSection('privacy')">
                                   <span class="menu-icon">
                                        <i class="ki-filled ki-shield-tick"></i>
                                   </span>
                                   <span class="menu-title">{{ __('provider/settings.account.menu.privacy') }}</span>
                              </a>
                         </div>
                         <div class="menu-item">
                              <a class="menu-link" href="#preferences-section" onclick="showSection('preferences')">
                                   <span class="menu-icon">
                                        <i class="ki-filled ki-setting-3"></i>
                                   </span>
                                   <span class="menu-title">{{ __('provider/settings.account.menu.preferences') }}</span>
                              </a>
                         </div>
                         <div class="menu-item">
                              <a class="menu-link text-red-600" href="#danger-section" onclick="showSection('danger')">
                                   <span class="menu-icon">
                                        <i class="ki-filled ki-trash"></i>
                                   </span>
                                   <span class="menu-title">{{ __('provider/settings.account.menu.danger_zone') }}</span>
                              </a>
                         </div>
                    </div>
               </div>
          </div>
     </div>

     <!-- Settings Content -->
     <div class="lg:col-span-2">
          <!-- Password Section -->
          <div id="password-section" class="settings-section">
               <div class="card">
                    <div class="card-header">
                         <h3 class="card-title">{{ __('provider/settings.account.sections.password_security') }}</h3>
                    </div>
                    <div class="card-body">
                         <form method="POST" action="{{ route('provider.settings.password.update') }}">
                              @csrf
                              @method('PUT')
                              
                              <div class="grid grid-cols-1 gap-4">
                                   <!-- Current Password -->
                                   <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                        <label class="form-label max-w-56">
                                             {{ __('provider/settings.account.fields.current_password') }}
                                        </label>
                                        <div class="input" data-toggle-password="true">
                                             <input class="@error('current_password') border-red-500 @enderror" 
                                                  name="current_password" 
                                                  type="password" 
                                                  placeholder="{{ __('provider/settings.account.placeholders.current_password') }}" 
                                                  required />
                                             <button class="btn btn-icon" data-toggle-password-trigger="true" type="button">
                                                  <i class="ki-filled ki-eye text-gray-500 toggle-password-active:hidden"></i>
                                                  <i class="ki-filled ki-eye-slash text-gray-500 hidden toggle-password-active:block"></i>
                                             </button>
                                        </div>
                                   </div>

                                   <!-- New Password -->
                                   <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                        <label class="form-label max-w-56">
                                             {{ __('provider/settings.account.fields.new_password') }}
                                        </label>
                                        <div class="input" data-toggle-password="true">
                                             <input class="@error('password') border-red-500 @enderror" 
                                                  name="password" 
                                                  type="password" 
                                                  placeholder="{{ __('provider/settings.account.placeholders.new_password') }}" 
                                                  required />
                                             <button class="btn btn-icon" data-toggle-password-trigger="true" type="button">
                                                  <i class="ki-filled ki-eye text-gray-500 toggle-password-active:hidden"></i>
                                                  <i class="ki-filled ki-eye-slash text-gray-500 hidden toggle-password-active:block"></i>
                                             </button>
                                        </div>
                                   </div>

                                   <!-- Confirm Password -->
                                   <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                        <label class="form-label max-w-56">
                                             {{ __('provider/settings.account.fields.confirm_password') }}
                                        </label>
                                        <div class="input" data-toggle-password="true">
                                             <input class="@error('password_confirmation') border-red-500 @enderror" 
                                                  name="password_confirmation" 
                                                  type="password" 
                                                  placeholder="{{ __('provider/settings.account.placeholders.confirm_password') }}" 
                                                  required />
                                             <button class="btn btn-icon" data-toggle-password-trigger="true" type="button">
                                                  <i class="ki-filled ki-eye text-gray-500 toggle-password-active:hidden"></i>
                                                  <i class="ki-filled ki-eye-slash text-gray-500 hidden toggle-password-active:block"></i>
                                             </button>
                                        </div>
                                   </div>
                              </div>

                              <div class="flex justify-end mt-6">
                                   <button type="submit" class="btn btn-primary">
                                        <i class="ki-filled ki-check"></i>
                                        {{ __('provider/settings.account.actions.update_password') }}
                                   </button>
                              </div>
                         </form>

                         <!-- Password Requirements -->
                         <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                              <h4 class="text-sm font-semibold text-gray-900 mb-2">
                                   {{ __('provider/settings.account.password_requirements.title') }}
                              </h4>
                              <ul class="text-2xs text-gray-600 space-y-1">
                                   <li>• {{ __('provider/settings.account.password_requirements.min_length') }}</li>
                                   <li>• {{ __('provider/settings.account.password_requirements.uppercase') }}</li>
                                   <li>• {{ __('provider/settings.account.password_requirements.lowercase') }}</li>
                                   <li>• {{ __('provider/settings.account.password_requirements.number') }}</li>
                              </ul>
                         </div>
                    </div>
               </div>
          </div>

          <!-- Notifications Section -->
          <div id="notifications-section" class="settings-section hidden">
               <div class="card">
                    <div class="card-header">
                         <h3 class="card-title">{{ __('provider/settings.account.sections.notification_preferences') }}</h3>
                    </div>
                    <div class="card-body">
                         <form method="POST" action="{{ route('provider.settings.notifications.update') }}">
                              @csrf
                              @method('PUT')
                              
                              <div class="flex flex-col gap-5">
                                   <!-- Email Notifications -->
                                   <div>
                                        <h4 class="text-sm font-semibold text-gray-900 mb-3">
                                             {{ __('provider/settings.account.notifications.email_notifications') }}
                                        </h4>
                                        <div class="flex flex-col gap-3">
                                             <label class="checkbox-group">
                                                  <input class="checkbox" name="email_new_booking" type="checkbox" value="1" checked />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/settings.account.notifications.new_booking') }}
                                                  </span>
                                             </label>
                                             <label class="checkbox-group">
                                                  <input class="checkbox" name="email_booking_cancelled" type="checkbox" value="1" checked />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/settings.account.notifications.booking_cancelled') }}
                                                  </span>
                                             </label>
                                             <label class="checkbox-group">
                                                  <input class="checkbox" name="email_payment_received" type="checkbox" value="1" checked />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/settings.account.notifications.payment_received') }}
                                                  </span>
                                             </label>
                                        </div>
                                   </div>

                                   <!-- SMS Notifications -->
                                   <div>
                                        <h4 class="text-sm font-semibold text-gray-900 mb-3">
                                             {{ __('provider/settings.account.notifications.sms_notifications') }}
                                        </h4>
                                        <div class="flex flex-col gap-3">
                                             <label class="checkbox-group">
                                                  <input class="checkbox" name="sms_urgent_booking" type="checkbox" value="1" checked />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/settings.account.notifications.urgent_booking') }}
                                                  </span>
                                             </label>
                                             <label class="checkbox-group">
                                                  <input class="checkbox" name="sms_booking_reminder" type="checkbox" value="1" />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/settings.account.notifications.booking_reminder') }}
                                                  </span>
                                             </label>
                                        </div>
                                   </div>
                              </div>

                              <div class="flex justify-end mt-6">
                                   <button type="submit" class="btn btn-primary">
                                        <i class="ki-filled ki-check"></i>
                                        {{ __('provider/settings.account.actions.save_preferences') }}
                                   </button>
                              </div>
                         </form>
                    </div>
               </div>
          </div>

          <!-- Privacy Section -->
          <div id="privacy-section" class="settings-section hidden">
               <div class="card">
                    <div class="card-header">
                         <h3 class="card-title">{{ __('provider/settings.account.sections.privacy_settings') }}</h3>
                    </div>
                    <div class="card-body">
                         <form method="POST" action="{{ route('provider.settings.privacy.update') }}">
                              @csrf
                              @method('PUT')
                              
                              <div class="flex flex-col gap-5">
                                   <!-- Profile Visibility -->
                                   <div>
                                        <h4 class="text-sm font-semibold text-gray-900 mb-3">
                                             {{ __('provider/settings.account.privacy.profile_visibility') }}
                                        </h4>
                                        <div class="flex flex-col gap-3">
                                             <label class="radio-group">
                                                  <input class="radio" name="profile_visibility" type="radio" value="public" checked />
                                                  <span class="radio-label">
                                                       {{ __('provider/settings.account.privacy.public_profile') }}
                                                  </span>
                                             </label>
                                             <label class="radio-group">
                                                  <input class="radio" name="profile_visibility" type="radio" value="private" />
                                                  <span class="radio-label">
                                                       {{ __('provider/settings.account.privacy.private_profile') }}
                                                  </span>
                                             </label>
                                        </div>
                                   </div>

                                   <!-- Contact Information -->
                                   <div>
                                        <h4 class="text-sm font-semibold text-gray-900 mb-3">
                                             {{ __('provider/settings.account.privacy.contact_information') }}
                                        </h4>
                                        <div class="flex flex-col gap-3">
                                             <label class="checkbox-group">
                                                  <input class="checkbox" name="show_phone" type="checkbox" value="1" checked />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/settings.account.privacy.show_phone') }}
                                                  </span>
                                             </label>
                                             <label class="checkbox-group">
                                                  <input class="checkbox" name="show_email" type="checkbox" value="1" />
                                                  <span class="checkbox-label">
                                                       {{ __('provider/settings.account.privacy.show_email') }}
                                                  </span>
                                             </label>
                                        </div>
                                   </div>
                              </div>

                              <div class="flex justify-end mt-6">
                                   <button type="submit" class="btn btn-primary">
                                        <i class="ki-filled ki-check"></i>
                                        {{ __('provider/settings.account.actions.save_privacy') }}
                                   </button>
                              </div>
                         </form>
                    </div>
               </div>
          </div>

          <!-- Preferences Section -->
          <div id="preferences-section" class="settings-section hidden">
               <div class="card">
                    <div class="card-header">
                         <h3 class="card-title">{{ __('provider/settings.account.sections.general_preferences') }}</h3>
                    </div>
                    <div class="card-body">
                         <form method="POST" action="{{ route('provider.settings.preferences.update') }}">
                              @csrf
                              @method('PUT')
                              
                              <div class="grid grid-cols-1 gap-4">
                                   <!-- Language -->
                                   <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                        <label class="form-label max-w-56">
                                             {{ __('provider/settings.account.fields.language') }}
                                        </label>
                                        <select class="select" name="language">
                                             <option value="ar">{{ __('provider/settings.account.languages.arabic') }}</option>
                                             <option value="en">{{ __('provider/settings.account.languages.english') }}</option>
                                        </select>
                                   </div>

                                   <!-- Timezone -->
                                   <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                        <label class="form-label max-w-56">
                                             {{ __('provider/settings.account.fields.timezone') }}
                                        </label>
                                        <select class="select" name="timezone">
                                             <option value="Asia/Riyadh">{{ __('provider/settings.account.timezones.riyadh') }}</option>
                                             <option value="UTC">{{ __('provider/settings.account.timezones.utc') }}</option>
                                        </select>
                                   </div>

                                   <!-- Currency -->
                                   <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                                        <label class="form-label max-w-56">
                                             {{ __('provider/settings.account.fields.currency') }}
                                        </label>
                                        <select class="select" name="currency">
                                             <option value="SAR">{{ __('provider/settings.account.currencies.sar') }}</option>
                                             <option value="USD">{{ __('provider/settings.account.currencies.usd') }}</option>
                                        </select>
                                   </div>
                              </div>

                              <div class="flex justify-end mt-6">
                                   <button type="submit" class="btn btn-primary">
                                        <i class="ki-filled ki-check"></i>
                                        {{ __('provider/settings.account.actions.save_preferences') }}
                                   </button>
                              </div>
                         </form>
                    </div>
               </div>
          </div>

          <!-- Danger Zone Section -->
          <div id="danger-section" class="settings-section hidden">
               <div class="card border-red-200">
                    <div class="card-header">
                         <h3 class="card-title text-red-600">{{ __('provider/settings.account.sections.danger_zone') }}</h3>
                    </div>
                    <div class="card-body">
                         <div class="flex flex-col gap-5">
                              <!-- Deactivate Account -->
                              <div class="p-4 border border-red-200 rounded-lg bg-red-50">
                                   <h4 class="text-sm font-semibold text-red-900 mb-2">
                                        {{ __('provider/settings.account.danger.deactivate_account') }}
                                   </h4>
                                   <p class="text-2xs text-red-700 mb-3">
                                        {{ __('provider/settings.account.danger.deactivate_description') }}
                                   </p>
                                   <button class="btn btn-sm btn-light border-red-300 text-red-600 hover:bg-red-100" onclick="deactivateAccount()">
                                        {{ __('provider/settings.account.actions.deactivate_account') }}
                                   </button>
                              </div>

                              <!-- Delete Account -->
                              <div class="p-4 border border-red-300 rounded-lg bg-red-100">
                                   <h4 class="text-sm font-semibold text-red-900 mb-2">
                                        {{ __('provider/settings.account.danger.delete_account') }}
                                   </h4>
                                   <p class="text-2xs text-red-800 mb-3">
                                        {{ __('provider/settings.account.danger.delete_description') }}
                                   </p>
                                   <button class="btn btn-sm btn-danger" onclick="deleteAccount()">
                                        {{ __('provider/settings.account.actions.delete_account') }}
                                   </button>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     </div>
</div>
@endsection

@push('scripts')
<script>
     function showSection(sectionName) {
          // Hide all sections
          document.querySelectorAll('.settings-section').forEach(section => {
               section.classList.add('hidden');
          });
          
          // Remove active class from all menu links
          document.querySelectorAll('.menu-link').forEach(link => {
               link.classList.remove('active');
          });
          
          // Show selected section
          document.getElementById(sectionName + '-section').classList.remove('hidden');
          
          // Add active class to clicked menu link
          event.target.closest('.menu-link').classList.add('active');
     }

     function deactivateAccount() {
          if (confirm('{{ __("provider/settings.account.confirmations.deactivate") }}')) {
               console.log('Deactivate account');
               // Implement deactivate account functionality
          }
     }

     function deleteAccount() {
          const confirmation = prompt('{{ __("provider/settings.account.confirmations.delete_prompt") }}');
          if (confirmation === 'DELETE') {
               console.log('Delete account');
               // Implement delete account functionality
          } else {
               alert('{{ __("provider/settings.account.confirmations.delete_failed") }}');
          }
     }
</script>
@endpush
