# Provider Dashboard System

This directory contains the comprehensive service provider dashboard system built with Laravel Blade templates, following Laravel conventions and modern web standards.

## Directory Structure

```
resources/views/provider/
├── layouts/
│   ├── app.blade.php          # Main dashboard layout
│   └── auth.blade.php         # Authentication layout
├── components/
│   ├── sidebar.blade.php      # Navigation sidebar
│   ├── header.blade.php       # Dashboard header
│   ├── footer.blade.php       # Dashboard footer
│   ├── flash-messages.blade.php # Flash message component
│   ├── language-switcher.blade.php # Language switcher
│   ├── statistics-card.blade.php # Statistics card component
│   ├── status-badge.blade.php # Status badge component
│   ├── data-table.blade.php   # Reusable data table
│   ├── pagination.blade.php   # Pagination component
│   ├── search-filters.blade.php # Search and filter component
│   └── accessibility-helpers.blade.php # Accessibility features
├── auth/
│   └── login.blade.php        # Provider login page
├── dashboard/
│   └── index.blade.php        # Main dashboard
├── profile/
│   └── basic-info.blade.php   # Profile management
├── working-hours/
│   └── index.blade.php        # Working hours management
├── services/
│   └── index.blade.php        # Services management
├── bookings/
│   └── index.blade.php        # Bookings overview
├── settings/
│   └── account.blade.php      # Account settings
└── README.md                  # This file
```

## Features

### 🎨 Design System
- **Consistent UI**: Card-based layouts with Tailwind CSS
- **KeenIcons Integration**: Consistent iconography throughout
- **Responsive Design**: Mobile-first approach with breakpoint optimization
- **RTL/LTR Support**: Full bidirectional text support for Arabic/English

### 🌐 Multilingual Support
- **Translation Structure**: `lang/{locale}/provider/{module}.php`
- **mcamara/laravel-localization**: Integration for URL localization
- **Language Switcher**: Dropdown component with native language names
- **RTL Layout**: Automatic layout direction based on language

### ♿ Accessibility Features
- **WCAG 2.1 Compliance**: Level AA accessibility standards
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Visible focus indicators and focus trapping
- **High Contrast Mode**: Support for high contrast preferences
- **Reduced Motion**: Respects user motion preferences

### 📱 Responsive Design
- **Mobile-First**: Optimized for mobile devices
- **Breakpoint System**: Consistent responsive behavior
- **Touch-Friendly**: Appropriate touch targets and interactions
- **Progressive Enhancement**: Works without JavaScript

## Components Usage

### Statistics Card
```blade
@include('provider.components.statistics-card', [
    'title' => 'Total Services',
    'value' => '25',
    'icon' => 'ki-filled ki-setting-2',
    'color' => 'primary',
    'badge' => [
        'text' => 'Active',
        'color' => 'success',
        'icon' => 'ki-filled ki-arrow-up'
    ]
])
```

### Status Badge
```blade
@include('provider.components.status-badge', [
    'status' => 'active',
    'text' => 'Active' // Optional, auto-generated if not provided
])
```

### Data Table
```blade
@include('provider.components.data-table', [
    'title' => 'Services List',
    'headers' => ['Name', 'Category', 'Price', 'Status'],
    'data' => $services,
    'actions' => ['view', 'edit', 'delete'],
    'selectable' => true,
    'pagination' => $services->links()
])
```

### Search Filters
```blade
@include('provider.components.search-filters', [
    'action' => route('provider.services.index'),
    'searchPlaceholder' => 'Search services...',
    'filters' => [
        'status' => [
            'label' => 'Status',
            'options' => [
                '' => 'All Statuses',
                'active' => 'Active',
                'inactive' => 'Inactive'
            ]
        ]
    ],
    'sorts' => [
        'name' => 'Name',
        'created_at' => 'Date Created'
    ]
])
```

## Translation Keys Structure

### Naming Convention
- **Pattern**: `provider.{module}.{context}.{specific_element}`
- **Example**: `provider.dashboard.statistics.total_services`

### Module Organization
- `provider/layout.php` - Layout elements
- `provider/navigation.php` - Navigation items
- `provider/auth.php` - Authentication pages
- `provider/dashboard.php` - Dashboard content
- `provider/messages.php` - Flash messages
- `provider/common.php` - Shared elements
- `provider/accessibility.php` - Accessibility labels

## Styling Guidelines

### CSS Classes
- **Provider-specific**: Use `provider-` prefix for custom classes
- **Tailwind CSS**: Primary styling framework
- **Custom CSS**: Located in `public/assets/css/provider.css`

### Color Scheme
- **Primary**: Blue (#3B82F6)
- **Success**: Green (#10B981)
- **Warning**: Yellow (#F59E0B)
- **Danger**: Red (#EF4444)
- **Info**: Blue (#3B82F6)

### Typography
- **English**: Inter font family
- **Arabic**: Cairo font family
- **Responsive**: Fluid typography scale

## Browser Support

### Minimum Requirements
- **Chrome**: 88+
- **Firefox**: 85+
- **Safari**: 14+
- **Edge**: 88+

### Progressive Enhancement
- Core functionality works without JavaScript
- Enhanced features require modern browser support
- Graceful degradation for older browsers

## Performance Considerations

### Optimization
- **Lazy Loading**: Images and non-critical content
- **Code Splitting**: Separate CSS/JS for provider dashboard
- **Caching**: Proper cache headers for static assets
- **Minification**: Compressed CSS and JavaScript

### Best Practices
- Minimal DOM manipulation
- Efficient CSS selectors
- Optimized images and icons
- Reduced HTTP requests

## Development Guidelines

### Code Standards
- Follow Laravel Blade conventions
- Use semantic HTML elements
- Implement proper error handling
- Include comprehensive comments

### Testing
- Test with screen readers
- Verify keyboard navigation
- Check responsive breakpoints
- Validate HTML markup

### Maintenance
- Regular accessibility audits
- Performance monitoring
- Browser compatibility testing
- Translation updates

## Future Enhancements

### Planned Features
- Dark mode support
- Advanced filtering options
- Real-time notifications
- Enhanced analytics dashboard
- Mobile app integration

### Technical Improvements
- Service Worker implementation
- Advanced caching strategies
- Performance monitoring
- Automated testing suite
