<!DOCTYPE html>
<html class="h-full" data-theme="true" data-theme-mode="light" dir="@direction" lang="@lang">

<head>
     <base href="{{ env('APP_URL')}}">
     <title>@yield('title', __('provider/auth.title'))</title>
     <meta charset="utf-8" />
     <meta content="follow, index" name="robots" />
     <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name="viewport" />
     <meta content="@yield('description', __('provider/auth.description'))" name="description" />
     <meta name="csrf-token" content="{{ csrf_token() }}">

     <!-- Favicons -->
     <link href="{{ asset('assets/media/app/apple-touch-icon.png') }}" rel="apple-touch-icon" sizes="180x180" />
     <link href="{{ asset('assets/media/app/favicon-32x32.png') }}" rel="icon" sizes="32x32" type="image/png" />
     <link href="{{ asset('assets/media/app/favicon-16x16.png') }}" rel="icon" sizes="16x16" type="image/png" />
     <link href="{{ asset('assets/media/app/favicon.ico') }}" rel="shortcut icon" />

     <!-- Fonts -->
     <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
     @isRtl
     <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet" />
     @endisRtl

     <!-- Vendor Styles -->
     <link href="{{ asset('assets/vendors/apexcharts/apexcharts.css') }}" rel="stylesheet" />
     <link href="{{ asset('assets/vendors/keenicons/styles.bundle.css') }}" rel="stylesheet" />
     <link href="{{ asset('assets/vendors/toastr/toastr.min.css') }}" rel="stylesheet" />

     <!-- Main Styles -->
     <link href="{{ asset('assets/css/styles.css') }}" rel="stylesheet" />

     <!-- Additional Styles -->
     @stack('styles')

     @isRtl
     <style>
          .select {
               line-height: inherit;
          }
     </style>
     @endisRtl
</head>

<body class="antialiased flex h-full text-base text-gray-700 dark:bg-coal-500 {{ $languageService->getDirectionClass() }}"
     style="font-family: @langFont;">

     <!-- Theme Mode Script -->
     <script>
          const defaultThemeMode = 'light'; // light|dark|system
          let themeMode;

          if (document.documentElement) {
               if (localStorage.getItem('theme')) {
                    themeMode = localStorage.getItem('theme');
               } else if (document.documentElement.hasAttribute('data-theme-mode')) {
                    themeMode = document.documentElement.getAttribute('data-theme-mode');
               } else {
                    themeMode = defaultThemeMode;
               }

               if (themeMode === 'system') {
                    themeMode = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
               }

               document.documentElement.classList.add(themeMode);
          }
     </script>

     <!-- Page Background Styles -->
     <style>
          .page-bg {
               background-image: url('{{ asset('assets/media/images/2600x1200/bg-10.png') }}');
          }

          .dark .page-bg {
               background-image: url('{{ asset('assets/media/images/2600x1200/bg-10-dark.png') }}');
          }
     </style>
     <div class="flex items-center justify-center grow bg-center bg-no-repeat page-bg">
          <div class="card max-w-[370px] w-full">
               @yield('content')
          </div>
     </div>

     <!-- Scripts -->
     <script src="{{ asset('assets/js/core.bundle.js') }}"></script>
     <script src="{{ asset('assets/vendors/apexcharts/apexcharts.min.js') }}"></script>

     <!-- jQuery (required for Toastr) -->
     <script src="https://code.jquery.com/jquery-3.7.1.min.js"
          integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>

     <!-- Toastr Scripts -->
     <script src="{{ asset('assets/vendors/toastr/toastr.min.js') }}"></script>
     <script src="{{ asset('assets/js/admin-toastr.js') }}"></script>

     <!-- Laravel Flash Data for Toastr -->
     <script>
          // Pass Laravel session data to JavaScript
          window.laravelFlashData = {
               success: @json(session('success')),
               error: @json(session('error')),
               warning: @json(session('warning')),
               info: @json(session('info')),
               errors: @json($errors->getMessages())
          };

          // Pass translations for Toastr titles
          window.translations = {
               success: @json(__('provider/messages.success')),
               error: @json(__('provider/messages.error')),
               warning: @json(__('provider/messages.warning')),
               info: @json(__('provider/messages.info')),
               validation_errors: @json(__('provider/messages.validation_errors'))
          };
     </script>

     <!-- Additional Scripts -->
     @stack('scripts')
     <!-- End of Scripts -->
</body>

</html>