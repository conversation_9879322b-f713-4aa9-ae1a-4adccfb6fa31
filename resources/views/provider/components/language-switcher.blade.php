<!-- Language Switcher -->
<div class="dropdown" data-dropdown="true" data-dropdown-offset="0, 10px" data-dropdown-placement="bottom-end"
     data-dropdown-trigger="click">
     <button
          class="dropdown-toggle btn btn-icon btn-icon-lg size-9 rounded-full hover:bg-primary-light hover:text-primary dropdown-open:bg-primary-light dropdown-open:text-primary text-gray-500">
          <i class="ki-filled ki-global text-lg"></i>
     </button>
     <div class="dropdown-content light:border-gray-300 w-full max-w-[200px]">
          <div class="flex items-center justify-between gap-2.5 text-sm text-gray-900 font-semibold px-5 py-2.5">
               {{ __('provider/layout.choose_language') }}
               <button class="btn btn-sm btn-icon btn-light btn-clear shrink-0" data-dropdown-dismiss="true">
                    <i class="ki-filled ki-cross"></i>
               </button>
          </div>
          <div class="border-b border-b-gray-200"></div>
          <div class="flex flex-col py-2.5" data-dropdown-dismiss="true">
               @foreach(LaravelLocalization::getSupportedLocales() as $localeCode => $properties)
                    <a href="{{ LaravelLocalization::getLocalizedURL($localeCode, null, [], true) }}"
                         class="flex items-center gap-2.5 px-5 py-2 hover:bg-gray-100 {{ $localeCode === LaravelLocalization::getCurrentLocale() ? 'bg-gray-50' : '' }}">
                         <div class="flex items-center justify-center size-6 rounded-full {{ $localeCode === LaravelLocalization::getCurrentLocale() ? 'bg-primary text-primary-inverse' : 'bg-gray-100' }}">
                              <span class="text-xs font-semibold">
                                   {{ strtoupper(substr($localeCode, 0, 2)) }}
                              </span>
                         </div>
                         <div class="flex flex-col">
                              <span class="text-sm font-medium text-gray-800">
                                   {{ $properties['native'] }}
                              </span>
                              @if($localeCode === LaravelLocalization::getCurrentLocale())
                                   <span class="text-xs text-primary">
                                        {{ __('provider/layout.current') }}
                                   </span>
                              @endif
                         </div>
                         @if($localeCode === LaravelLocalization::getCurrentLocale())
                              <i class="ki-filled ki-check text-primary ms-auto"></i>
                         @endif
                    </a>
               @endforeach
          </div>
     </div>
</div>
<!-- End of Language Switcher -->
