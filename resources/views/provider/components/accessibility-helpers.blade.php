{{--
Provider Accessibility Helpers Component

This component provides accessibility enhancements for the provider dashboard.
Include this at the top of your layout to ensure proper accessibility support.
--}}

<!-- Skip to main content link for screen readers -->
<a href="#main-content"
     class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-white focus:rounded focus:shadow-lg">
     {{ __('provider/accessibility.skip_to_main_content') }}
</a>

<!-- Screen reader announcements -->
<div id="sr-announcements" class="sr-only" aria-live="polite" aria-atomic="true"></div>

<!-- High contrast mode detection -->
<script>
     // Detect high contrast mode and add appropriate class
     if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
          document.documentElement.classList.add('high-contrast');
     }

     // Detect reduced motion preference
     if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
          document.documentElement.classList.add('reduce-motion');
     }

     // Function to announce messages to screen readers
     window.announceToScreenReader = function (message) {
          const announcements = document.getElementById('sr-announcements');
          if (announcements) {
               announcements.textContent = message;
               setTimeout(() => {
                    announcements.textContent = '';
               }, 1000);
          }
     };

     // Enhanced focus management
     document.addEventListener('DOMContentLoaded', function () {
          // Add focus-visible polyfill behavior
          const focusVisibleElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]');

          focusVisibleElements.forEach(element => {
               element.addEventListener('focus', function () {
                    if (this.matches(':focus-visible')) {
                         this.classList.add('provider-focus-visible');
                    }
               });

               element.addEventListener('blur', function () {
                    this.classList.remove('provider-focus-visible');
               });
          });

          // Trap focus in modals
          const modals = document.querySelectorAll('[data-modal="true"]');
          modals.forEach(modal => {
               modal.addEventListener('shown', function () {
                    trapFocus(this);
               });
          });
     });

     // Focus trap utility
     function trapFocus(element) {
          const focusableElements = element.querySelectorAll(
               'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );

          const firstFocusableElement = focusableElements[0];
          const lastFocusableElement = focusableElements[focusableElements.length - 1];

          element.addEventListener('keydown', function (e) {
               if (e.key === 'Tab') {
                    if (e.shiftKey) {
                         if (document.activeElement === firstFocusableElement) {
                              lastFocusableElement.focus();
                              e.preventDefault();
                         }
                    } else {
                         if (document.activeElement === lastFocusableElement) {
                              firstFocusableElement.focus();
                              e.preventDefault();
                         }
                    }
               }

               if (e.key === 'Escape') {
                    const closeButton = element.querySelector('[data-modal-dismiss="true"]');
                    if (closeButton) {
                         closeButton.click();
                    }
               }
          });

          // Focus first element
          if (firstFocusableElement) {
               firstFocusableElement.focus();
          }
     }
</script>

<!-- CSS for accessibility enhancements -->
<style>
     /* High contrast mode styles */
     .high-contrast {
          --tw-page-bg: #ffffff;
          --tw-page-bg-dark: #000000;
     }

     .high-contrast .card {
          border: 2px solid #000000 !important;
     }

     .high-contrast .btn {
          border: 2px solid #000000 !important;
     }

     /* Reduced motion styles */
     .reduce-motion * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
     }

     /* Screen reader only content */
     .sr-only {
          position: absolute !important;
          width: 1px !important;
          height: 1px !important;
          padding: 0 !important;
          margin: -1px !important;
          overflow: hidden !important;
          clip: rect(0, 0, 0, 0) !important;
          white-space: nowrap !important;
          border: 0 !important;
     }

     .sr-only:focus {
          position: static !important;
          width: auto !important;
          height: auto !important;
          padding: inherit !important;
          margin: inherit !important;
          overflow: visible !important;
          clip: auto !important;
          white-space: normal !important;
     }
</style>