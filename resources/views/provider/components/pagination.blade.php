{{--
    Provider Pagination Component
    
    Usage:
    @include('provider.components.pagination', [
        'paginator' => $services,
        'showInfo' => true
    ])
--}}

@if($paginator->hasPages())
     <div class="card-footer justify-center md:justify-between flex-col md:flex-row gap-3 text-gray-600 text-2sm font-medium">
          @if($showInfo ?? true)
               <div class="flex items-center gap-2 order-2 md:order-1">
                    {{ __('provider/common.pagination.showing') }} 
                    <span class="font-semibold text-gray-900">{{ $paginator->firstItem() }}</span>
                    {{ __('provider/common.pagination.to') }} 
                    <span class="font-semibold text-gray-900">{{ $paginator->lastItem() }}</span>
                    {{ __('provider/common.pagination.of') }} 
                    <span class="font-semibold text-gray-900">{{ $paginator->total() }}</span>
                    {{ __('provider/common.pagination.results') }}
               </div>
          @endif
          
          <div class="pagination order-1 md:order-2">
               <nav role="navigation" aria-label="{{ __('provider/common.pagination.navigation') }}" class="flex items-center gap-1">
                    {{-- Previous Page Link --}}
                    @if($paginator->onFirstPage())
                         <span class="btn btn-sm btn-light" aria-disabled="true" aria-label="{{ __('provider/common.pagination.previous') }}">
                              <i class="ki-filled ki-black-left @isRtl rotate-180 @endisRtl"></i>
                         </span>
                    @else
                         <a class="btn btn-sm btn-light" href="{{ $paginator->previousPageUrl() }}" rel="prev" aria-label="{{ __('provider/common.pagination.previous') }}">
                              <i class="ki-filled ki-black-left @isRtl rotate-180 @endisRtl"></i>
                         </a>
                    @endif

                    {{-- Pagination Elements --}}
                    @foreach($paginator->getUrlRange(1, $paginator->lastPage()) as $page => $url)
                         @if($page == $paginator->currentPage())
                              <span class="btn btn-sm btn-primary" aria-current="page">{{ $page }}</span>
                         @else
                              <a class="btn btn-sm btn-light" href="{{ $url }}">{{ $page }}</a>
                         @endif
                    @endforeach

                    {{-- Next Page Link --}}
                    @if($paginator->hasMorePages())
                         <a class="btn btn-sm btn-light" href="{{ $paginator->nextPageUrl() }}" rel="next" aria-label="{{ __('provider/common.pagination.next') }}">
                              <i class="ki-filled ki-black-right @isRtl rotate-180 @endisRtl"></i>
                         </a>
                    @else
                         <span class="btn btn-sm btn-light" aria-disabled="true" aria-label="{{ __('provider/common.pagination.next') }}">
                              <i class="ki-filled ki-black-right @isRtl rotate-180 @endisRtl"></i>
                         </span>
                    @endif
               </nav>
          </div>
     </div>
@endif
