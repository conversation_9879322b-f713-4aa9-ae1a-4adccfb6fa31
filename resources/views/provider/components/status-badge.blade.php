{{--
    Provider Status Badge Component
    
    Usage:
    @include('provider.components.status-badge', [
        'status' => 'active',
        'text' => 'Active'
    ])
    
    Or with automatic text:
    @include('provider.components.status-badge', [
        'status' => 'pending'
    ])
--}}

@php
    $statusConfig = [
        'active' => [
            'class' => 'kt-badge kt-badge-outline kt-badge-success',
            'text' => $text ?? __('provider/common.status.active')
        ],
        'inactive' => [
            'class' => 'kt-badge kt-badge-outline kt-badge-danger',
            'text' => $text ?? __('provider/common.status.inactive')
        ],
        'pending' => [
            'class' => 'kt-badge kt-badge-outline kt-badge-warning',
            'text' => $text ?? __('provider/common.status.pending')
        ],
        'confirmed' => [
            'class' => 'kt-badge kt-badge-outline kt-badge-info',
            'text' => $text ?? __('provider/common.status.confirmed')
        ],
        'completed' => [
            'class' => 'kt-badge kt-badge-outline kt-badge-success',
            'text' => $text ?? __('provider/common.status.completed')
        ],
        'cancelled' => [
            'class' => 'kt-badge kt-badge-outline kt-badge-danger',
            'text' => $text ?? __('provider/common.status.cancelled')
        ],
        'draft' => [
            'class' => 'kt-badge kt-badge-outline kt-badge-secondary',
            'text' => $text ?? __('provider/common.status.draft')
        ],
        'published' => [
            'class' => 'kt-badge kt-badge-outline kt-badge-primary',
            'text' => $text ?? __('provider/common.status.published')
        ],
    ];
    
    $config = $statusConfig[$status] ?? $statusConfig['active'];
@endphp

<span class="{{ $config['class'] }}">
     {{ $config['text'] }}
</span>
