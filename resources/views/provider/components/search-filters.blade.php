{{--
    Provider Search Filters Component
    
    Usage:
    @include('provider.components.search-filters', [
        'action' => route('provider.services.index'),
        'searchPlaceholder' => 'Search services...',
        'filters' => [
            'status' => [
                'label' => 'Status',
                'options' => [
                    '' => 'All Statuses',
                    'active' => 'Active',
                    'inactive' => 'Inactive'
                ]
            ],
            'category' => [
                'label' => 'Category',
                'options' => $categories
            ]
        ],
        'sorts' => [
            'name' => 'Name',
            'created_at' => 'Date Created',
            'price' => 'Price'
        ]
    ])
--}}

<div class="card mb-5 lg:mb-7.5">
     <div class="card-body">
          <form method="GET" action="{{ $action }}" class="flex flex-wrap items-center gap-4">
               <!-- Search Input -->
               <div class="flex items-center gap-2 flex-1 min-w-[200px]">
                    <i class="ki-filled ki-magnifier text-gray-500"></i>
                    <input class="input border-0 bg-transparent shadow-none flex-1" 
                         name="search" 
                         type="text" 
                         value="{{ request('search') }}" 
                         placeholder="{{ $searchPlaceholder ?? __('provider/common.search.placeholder') }}" />
               </div>

               <!-- Filters -->
               @if(isset($filters))
                    @foreach($filters as $filterKey => $filter)
                         <div class="flex flex-col gap-1">
                              @if(isset($filter['label']))
                                   <label class="form-label text-2xs text-gray-600">{{ $filter['label'] }}</label>
                              @endif
                              <select class="select w-40" name="{{ $filterKey }}">
                                   @foreach($filter['options'] as $value => $label)
                                        <option value="{{ $value }}" {{ request($filterKey) == $value ? 'selected' : '' }}>
                                             {{ $label }}
                                        </option>
                                   @endforeach
                              </select>
                         </div>
                    @endforeach
               @endif

               <!-- Sort -->
               @if(isset($sorts))
                    <div class="flex flex-col gap-1">
                         <label class="form-label text-2xs text-gray-600">{{ __('provider/common.search.sort_by') }}</label>
                         <select class="select w-40" name="sort">
                              @foreach($sorts as $value => $label)
                                   <option value="{{ $value }}" {{ request('sort') == $value ? 'selected' : '' }}>
                                        {{ $label }}
                                   </option>
                              @endforeach
                         </select>
                    </div>
               @endif

               <!-- Sort Direction -->
               @if(isset($sorts))
                    <div class="flex flex-col gap-1">
                         <label class="form-label text-2xs text-gray-600">{{ __('provider/common.search.direction') }}</label>
                         <select class="select w-32" name="direction">
                              <option value="asc" {{ request('direction') == 'asc' ? 'selected' : '' }}>
                                   {{ __('provider/common.search.ascending') }}
                              </option>
                              <option value="desc" {{ request('direction', 'desc') == 'desc' ? 'selected' : '' }}>
                                   {{ __('provider/common.search.descending') }}
                              </option>
                         </select>
                    </div>
               @endif

               <!-- Action Buttons -->
               <div class="flex items-end gap-2">
                    <button type="submit" class="btn btn-primary">
                         <i class="ki-filled ki-magnifier"></i>
                         {{ __('provider/common.search.search') }}
                    </button>

                    @if(request()->hasAny(['search', 'status', 'category', 'sort', 'direction']))
                         <a href="{{ $action }}" class="btn btn-light">
                              <i class="ki-filled ki-arrows-circle"></i>
                              {{ __('provider/common.search.reset') }}
                         </a>
                    @endif
               </div>
          </form>
     </div>
</div>
