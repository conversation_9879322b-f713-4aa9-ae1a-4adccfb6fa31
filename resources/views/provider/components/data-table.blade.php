{{--
    Provider Data Table Component
    
    Usage:
    @include('provider.components.data-table', [
        'title' => 'Services List',
        'headers' => ['Name', 'Category', 'Price', 'Status', 'Actions'],
        'data' => $services,
        'actions' => ['view', 'edit', 'delete'],
        'selectable' => true,
        'pagination' => $services->links()
    ])
--}}

<div class="card">
     @if(isset($title))
          <div class="card-header">
               <h3 class="card-title">{{ $title }}</h3>
               @if(isset($headerActions))
                    <div class="flex items-center gap-2">
                         {!! $headerActions !!}
                    </div>
               @endif
          </div>
     @endif
     
     <div class="card-body p-0">
          @if(isset($data) && $data->count() > 0)
               <div class="table-responsive">
                    <table class="table table-auto table-border">
                         <thead>
                              <tr>
                                   @if($selectable ?? false)
                                        <th class="w-[50px]">
                                             <label class="checkbox-group">
                                                  <input class="checkbox" type="checkbox" id="select_all_{{ $tableId ?? 'table' }}" />
                                                  <span class="checkbox-label"></span>
                                             </label>
                                        </th>
                                   @endif
                                   
                                   @foreach($headers as $header)
                                        <th class="min-w-[100px]">{{ $header }}</th>
                                   @endforeach
                                   
                                   @if(isset($actions) && count($actions) > 0)
                                        <th class="w-[120px]">{{ __('provider/common.table.actions') }}</th>
                                   @endif
                              </tr>
                         </thead>
                         <tbody>
                              @foreach($data as $item)
                                   <tr>
                                        @if($selectable ?? false)
                                             <td>
                                                  <label class="checkbox-group">
                                                       <input class="checkbox item-checkbox" type="checkbox" value="{{ $item->id }}" />
                                                       <span class="checkbox-label"></span>
                                                  </label>
                                             </td>
                                        @endif
                                        
                                        @if(isset($rowTemplate))
                                             {!! $rowTemplate($item) !!}
                                        @else
                                             {{-- Default row rendering --}}
                                             <td>{{ $item->name ?? $item->title ?? $item->id }}</td>
                                             @if(count($headers) > 1)
                                                  @for($i = 1; $i < count($headers) - (isset($actions) ? 1 : 0); $i++)
                                                       <td>-</td>
                                                  @endfor
                                             @endif
                                        @endif
                                        
                                        @if(isset($actions) && count($actions) > 0)
                                             <td>
                                                  <div class="flex items-center gap-1">
                                                       @foreach($actions as $action)
                                                            @if($action === 'view')
                                                                 <a href="{{ $viewRoute ?? '#' }}/{{ $item->id }}" class="btn btn-icon btn-sm btn-light" title="{{ __('provider/common.actions.view') }}">
                                                                      <i class="ki-filled ki-eye"></i>
                                                                 </a>
                                                            @elseif($action === 'edit')
                                                                 <a href="{{ $editRoute ?? '#' }}/{{ $item->id }}/edit" class="btn btn-icon btn-sm btn-light" title="{{ __('provider/common.actions.edit') }}">
                                                                      <i class="ki-filled ki-notepad-edit"></i>
                                                                 </a>
                                                            @elseif($action === 'delete')
                                                                 <button class="btn btn-icon btn-sm btn-light text-red-600 hover:text-red-700" 
                                                                         onclick="deleteItem({{ $item->id }})" 
                                                                         title="{{ __('provider/common.actions.delete') }}">
                                                                      <i class="ki-filled ki-trash"></i>
                                                                 </button>
                                                            @endif
                                                       @endforeach
                                                  </div>
                                             </td>
                                        @endif
                                   </tr>
                              @endforeach
                         </tbody>
                    </table>
               </div>

               @if(isset($pagination))
                    <div class="card-footer justify-center md:justify-between flex-col md:flex-row gap-3 text-gray-600 text-2sm font-medium">
                         <div class="flex items-center gap-2 order-2 md:order-1">
                              {{ __('provider/common.pagination.showing') }} 
                              {{ $data->firstItem() }} 
                              {{ __('provider/common.pagination.to') }} 
                              {{ $data->lastItem() }} 
                              {{ __('provider/common.pagination.of') }} 
                              {{ $data->total() }} 
                              {{ __('provider/common.pagination.results') }}
                         </div>
                         <div class="pagination order-1 md:order-2">
                              {!! $pagination !!}
                         </div>
                    </div>
               @endif
          @else
               <div class="text-center py-20">
                    <i class="{{ $emptyIcon ?? 'ki-filled ki-file' }} text-6xl text-gray-300 mb-5"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                         {{ $emptyTitle ?? __('provider/common.table.no_data') }}
                    </h3>
                    <p class="text-gray-600 mb-5">
                         {{ $emptyDescription ?? __('provider/common.table.no_data_description') }}
                    </p>
                    @if(isset($emptyAction))
                         {!! $emptyAction !!}
                    @endif
               </div>
          @endif
     </div>
</div>

@if($selectable ?? false)
     @push('scripts')
     <script>
          document.addEventListener('DOMContentLoaded', function() {
               const selectAllCheckbox = document.getElementById('select_all_{{ $tableId ?? 'table' }}');
               if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', function() {
                         const checkboxes = document.querySelectorAll('.item-checkbox');
                         checkboxes.forEach(checkbox => {
                              checkbox.checked = this.checked;
                         });
                    });
               }
          });
          
          function deleteItem(id) {
               if (confirm('{{ __("provider/common.confirmations.delete") }}')) {
                    // Implement delete functionality
                    console.log('Delete item:', id);
               }
          }
     </script>
     @endpush
@endif
