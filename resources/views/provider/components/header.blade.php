<!-- Header -->
<header
     class="header fixed top-0 z-10 start-0 end-0 flex items-stretch shrink-0 bg-[--tw-page-bg] dark:bg-[--tw-page-bg-dark]"
     data-sticky="true" data-sticky-class="shadow-sm dark:border-b dark:border-b-coal-100" data-sticky-name="header"
     id="header">
     <!-- Container -->
     <div class="container-fixed flex justify-end items-stretch lg:gap-4" id="header_container">
          <div class="flex gap-1 lg:hidden items-center -ms-1">
               <a class="shrink-0" href="{{ route('provider.dashboard') }}">
                    <img class="max-h-[25px] w-fit" src="{{ asset('assets/media/app/mini-logo.svg') }}" />
               </a>
               <div class="flex items-center">
                    <button class="btn btn-icon btn-light btn-clear btn-sm" data-drawer-toggle="#sidebar">
                         <i class="ki-filled ki-menu"></i>
                    </button>
               </div>
          </div>

          <div class="flex items-center gap-2 lg:gap-3.5">
               <!-- Search -->
               <button
                    class="btn btn-icon btn-icon-lg size-9 rounded-full hover:bg-primary-light hover:text-primary dropdown-open:bg-primary-light dropdown-open:text-primary text-gray-500"
                    data-modal-toggle="#search_modal">
                    <i class="ki-filled ki-magnifier"></i>
               </button>

               <!-- Language Switcher -->
               @include('provider.components.language-switcher')

               <!-- Notifications -->
               <div class="dropdown" data-dropdown="true" data-dropdown-offset="170px, 10px"
                    data-dropdown-placement="bottom-end" data-dropdown-trigger="click">
                    <button
                         class="dropdown-toggle btn btn-icon btn-icon-lg size-9 rounded-full hover:bg-primary-light hover:text-primary dropdown-open:bg-primary-light dropdown-open:text-primary text-gray-500">
                         <i class="ki-filled ki-notification-on"></i>
                    </button>
                    <div class="dropdown-content light:border-gray-300 w-full max-w-[460px]">
                         <div class="flex items-center justify-between gap-2.5 text-sm text-gray-900 font-semibold px-5 py-2.5"
                              id="notifications_header">
                              {{ __('provider/layout.notifications') }}
                              <button class="btn btn-sm btn-icon btn-light btn-clear shrink-0"
                                   data-dropdown-dismiss="true">
                                   <i class="ki-filled ki-cross"></i>
                              </button>
                         </div>
                         <div class="border-b border-b-gray-200"></div>
                         <div class="tabs justify-between px-5 mb-2" data-tabs="true" id="notifications_tabs">
                              <button class="tab active" data-tab-toggle="#notifications_tab_all">
                                   {{ __('provider/layout.all') }}
                              </button>
                              <button class="tab" data-tab-toggle="#notifications_tab_inbox">
                                   {{ __('provider/layout.inbox') }}
                              </button>
                              <button class="tab" data-tab-toggle="#notifications_tab_team">
                                   {{ __('provider/layout.bookings') }}
                              </button>
                         </div>
                         <div class="scrollable-y-auto" data-scrollable="true" data-scrollable-max-height="300px">
                              <div class="flex flex-col" id="notifications_tab_all">
                                   <div class="flex grow gap-2.5 px-5 py-3">
                                        <div class="relative shrink-0 mt-0.5">
                                             <img alt="" class="rounded-full size-8"
                                                  src="{{ asset('assets/media/avatars/300-4.png') }}" />
                                             <span
                                                  class="size-1.5 badge badge-circle bg-gray-400 absolute top-7 end-0.5 ring-1 ring-light transform -translate-y-1/2"></span>
                                        </div>
                                        <div class="flex flex-col gap-3.5 grow">
                                             <div class="flex flex-col gap-1">
                                                  <div class="text-2sm font-medium mb-px">
                                                       <a class="hover:text-primary-active text-gray-900 font-semibold"
                                                            href="#">{{ __('provider/layout.new_booking') }}</a>
                                                       <span
                                                            class="text-gray-700">{{ __('provider/layout.booking_received') }}</span>
                                                  </div>
                                                  <span class="flex items-center text-2xs font-medium text-gray-500">
                                                       {{ __('provider/layout.2_mins_ago') }}
                                                  </span>
                                             </div>
                                        </div>
                                   </div>
                              </div>
                         </div>
                         <div class="border-b border-b-gray-200"></div>
                         <div class="grid grid-cols-2 p-5 gap-2.5" id="notifications_all_footer">
                              <button class="btn btn-sm btn-light justify-center">
                                   {{ __('provider/layout.archive_all') }}
                              </button>
                              <button class="btn btn-sm btn-light justify-center">
                                   {{ __('provider/layout.mark_all_read') }}
                              </button>
                         </div>
                    </div>
               </div>

               <!-- User Menu -->
               <div class="dropdown" data-dropdown="true" data-dropdown-offset="11px, 10px"
                    data-dropdown-placement="bottom-end" data-dropdown-trigger="click">
                    <button
                         class="dropdown-toggle flex items-center grow gap-2.5 px-2 py-1.5 rounded-lg bg-light hover:bg-gray-200 dropdown-open:bg-gray-200 text-gray-700">
                         <div
                              class="flex items-center justify-center shrink-0 rounded-full bg-success size-9 text-success-inverse size-6 relative">
                              @if(auth('provider')->user()->image)
                                          <img src="{{ asset('storage/' . auth('provider')->user()->image) }}"
                                                alt="{{ auth('provider')->user()->name }}" class="rounded-full size-9 object-cover">
                                     @else
                                          <span class="text-sm font-semibold">
                                                {{ strtoupper(substr(auth('provider')->user()->name, 0, 1)) }}
                                          </span>
                                     @endif
                         </div>
                         <div class="flex flex-col gap-1.5 text-left">
                              <span class="text-xs leading-none text-gray-600">
                                   {{ __('provider/layout.welcome') }}
                              </span>
                              <span class="text-sm font-medium text-gray-800 leading-none">
                                   {{ auth('provider')->user()->name }}
                              </span>
                         </div>
                         <i class="ki-filled ki-down text-2xs text-gray-500"></i>
                    </button>
                    <div class="dropdown-content light:border-gray-300 w-screen max-w-[250px]">
                         <div class="flex items-center justify-between px-5 py-1.5 gap-1.5">
                              <div class="flex items-center gap-2">
                                   <img alt="" class="size-9 rounded-full border-2 border-success"
                                        src="{{ auth('provider')->user()->image ? asset('storage/' . auth('provider')->user()->image) : asset('assets/media/avatars/blank.png') }}" />
                                   <div class="flex flex-col gap-1.5">
                                        <span class="text-sm text-gray-800 font-semibold leading-none">
                                             {{ auth('provider')->user()->name }}
                                        </span>
                                        <span class="text-xs text-gray-600 leading-none">
                                             {{ auth('provider')->user()->email }}
                                        </span>
                                   </div>
                              </div>
                         </div>
                         <div class="menu-separator"></div>
                         <div class="flex flex-col" data-dropdown-dismiss="true">
                              <div class="menu-item">
                                   <a class="menu-link" href="{{ route('provider.profile.basic-info') }}">
                                        <span class="menu-icon">
                                             <i class="ki-filled ki-profile-circle"></i>
                                        </span>
                                        <span class="menu-title">{{ __('provider/navigation.profile') }}</span>
                                   </a>
                              </div>
                              <div class="menu-item">
                                   <a class="menu-link" href="{{ route('provider.settings.account') }}">
                                        <span class="menu-icon">
                                             <i class="ki-filled ki-setting-3"></i>
                                        </span>
                                        <span class="menu-title">{{ __('provider/navigation.settings') }}</span>
                                   </a>
                              </div>
                         </div>
                         <div class="menu-separator"></div>
                         <div class="flex flex-col">
                              <div class="menu-item mb-0.5">
                                   <form method="POST" action="{{ route('provider.logout') }}">
                                        @csrf
                                        <button type="submit" class="menu-link w-full text-left">
                                             <span class="menu-icon">
                                                  <i class="ki-filled ki-entrance-left"></i>
                                             </span>
                                             <span class="menu-title">{{ __('provider/auth.logout') }}</span>
                                        </button>
                                   </form>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     </div>
</header>
<!-- End of Header -->