{{--
    Provider Statistics Card Component
    
    Usage:
    @include('provider.components.statistics-card', [
        'title' => 'Total Services',
        'value' => '25',
        'icon' => 'ki-filled ki-setting-2',
        'color' => 'primary',
        'badge' => [
            'text' => 'Active',
            'color' => 'success',
            'icon' => 'ki-filled ki-arrow-up'
        ]
    ])
--}}

@php
    $colorClasses = [
        'primary' => 'bg-primary-light text-primary',
        'success' => 'bg-success-light text-success',
        'warning' => 'bg-warning-light text-warning',
        'danger' => 'bg-danger-light text-danger',
        'info' => 'bg-info-light text-info',
    ];
    
    $badgeColorClasses = [
        'primary' => 'kt-badge-primary',
        'success' => 'kt-badge-success',
        'warning' => 'kt-badge-warning',
        'danger' => 'kt-badge-danger',
        'info' => 'kt-badge-info',
    ];
    
    $iconColorClass = $colorClasses[$color] ?? $colorClasses['primary'];
    $badgeColorClass = $badgeColorClasses[$badge['color'] ?? 'primary'] ?? $badgeColorClasses['primary'];
@endphp

<div class="card">
     <div class="card-body">
          <div class="flex items-center justify-between">
               <div>
                    <div class="text-2sm font-medium text-gray-600 mb-1">
                         {{ $title }}
                    </div>
                    <div class="text-2xl font-semibold text-gray-900">
                         {{ $value }}
                    </div>
               </div>
               <div class="flex items-center justify-center size-12 rounded-lg {{ $iconColorClass }}">
                    <i class="{{ $icon }} text-xl"></i>
               </div>
          </div>
          @if(isset($badge))
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline {{ $badgeColorClass }} gap-1">
                         @if(isset($badge['icon']))
                              <i class="{{ $badge['icon'] }} text-2xs"></i>
                         @endif
                         {{ $badge['text'] }}
                    </span>
               </div>
          @endif
     </div>
</div>
