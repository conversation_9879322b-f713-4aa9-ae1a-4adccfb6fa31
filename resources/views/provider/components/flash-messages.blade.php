{{--
Provider Flash Messages Component - Replaced with Toastr.js

This component has been replaced with Toastr.js notifications for a better user experience.
Flash messages are now handled automatically by the admin-toastr.js script which:

- Displays success, error, warning, and info messages as toast notifications
- Supports RTL layout for Arabic language
- <PERSON><PERSON> validation errors with proper formatting
- Provides consistent positioning and timing
- Integrates seamlessly with Laravel session flash data

The flash messages are automatically displayed when the page loads through the
initLaravelFlashMessages() function in admin-toastr.js.

To manually display notifications in JavaScript, use:
- AdminToastr.success('Message')
- AdminToastr.error('Message')
- AdminToastr.warning('Message')
- AdminToastr.info('Message')
- AdminToastr.validationErrors(errors)
--}}