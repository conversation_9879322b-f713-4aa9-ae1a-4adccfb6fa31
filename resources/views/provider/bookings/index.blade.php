@extends('provider.layouts.app')

@section('title', __('provider/bookings.title') . ' - ' . config('app.name'))

@section('page-title', __('provider/bookings.page_title'))

@section('page-description', __('provider/bookings.page_description'))

@section('page-actions')
<div class="flex items-center gap-2.5">
     <div class="dropdown" data-dropdown="true" data-dropdown-trigger="click">
          <button class="dropdown-toggle btn btn-light">
               <i class="ki-filled ki-filter"></i>
               {{ __('provider/bookings.actions.filter') }}
               <i class="ki-filled ki-down text-2xs"></i>
          </button>
          <div class="dropdown-content w-48">
               <div class="menu menu-default">
                    <div class="menu-item">
                         <a class="menu-link" href="?status=pending">
                              <span class="menu-icon"><i class="ki-filled ki-clock"></i></span>
                              <span class="menu-title">{{ __('provider/bookings.filters.pending') }}</span>
                         </a>
                    </div>
                    <div class="menu-item">
                         <a class="menu-link" href="?status=confirmed">
                              <span class="menu-icon"><i class="ki-filled ki-check-circle"></i></span>
                              <span class="menu-title">{{ __('provider/bookings.filters.confirmed') }}</span>
                         </a>
                    </div>
                    <div class="menu-item">
                         <a class="menu-link" href="?status=completed">
                              <span class="menu-icon"><i class="ki-filled ki-check"></i></span>
                              <span class="menu-title">{{ __('provider/bookings.filters.completed') }}</span>
                         </a>
                    </div>
               </div>
          </div>
     </div>
     <button class="btn btn-light" onclick="refreshBookings()">
          <i class="ki-filled ki-arrows-circle"></i>
          {{ __('provider/bookings.actions.refresh') }}
     </button>
</div>
@endsection

@section('content')
<!-- Statistics Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
     <!-- Pending Bookings -->
     <div class="card">
          <div class="card-body">
               <div class="flex items-center justify-between">
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/bookings.statistics.pending') }}
                         </div>
                         <div class="text-2xl font-semibold text-gray-900">
                              {{ $statistics['pending'] ?? 0 }}
                         </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-warning-light">
                         <i class="ki-filled ki-clock text-xl text-warning"></i>
                    </div>
               </div>
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline kt-badge-warning gap-1">
                         <i class="ki-filled ki-arrow-up text-2xs"></i>
                         {{ __('provider/bookings.statistics.requires_action') }}
                    </span>
               </div>
          </div>
     </div>

     <!-- Confirmed Bookings -->
     <div class="card">
          <div class="card-body">
               <div class="flex items-center justify-between">
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/bookings.statistics.confirmed') }}
                         </div>
                         <div class="text-2xl font-semibold text-gray-900">
                              {{ $statistics['confirmed'] ?? 0 }}
                         </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-info-light">
                         <i class="ki-filled ki-check-circle text-xl text-info"></i>
                    </div>
               </div>
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline kt-badge-info gap-1">
                         <i class="ki-filled ki-calendar text-2xs"></i>
                         {{ __('provider/bookings.statistics.scheduled') }}
                    </span>
               </div>
          </div>
     </div>

     <!-- Completed Bookings -->
     <div class="card">
          <div class="card-body">
               <div class="flex items-center justify-between">
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/bookings.statistics.completed') }}
                         </div>
                         <div class="text-2xl font-semibold text-gray-900">
                              {{ $statistics['completed'] ?? 0 }}
                         </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-success-light">
                         <i class="ki-filled ki-check text-xl text-success"></i>
                    </div>
               </div>
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline kt-badge-success gap-1">
                         <i class="ki-filled ki-arrow-up text-2xs"></i>
                         {{ __('provider/bookings.statistics.this_month') }}
                    </span>
               </div>
          </div>
     </div>

     <!-- Total Revenue -->
     <div class="card">
          <div class="card-body">
               <div class="flex items-center justify-between">
                    <div>
                         <div class="text-2sm font-medium text-gray-600 mb-1">
                              {{ __('provider/bookings.statistics.total_revenue') }}
                         </div>
                         <div class="text-2xl font-semibold text-gray-900">
                              {{ number_format($statistics['total_revenue'] ?? 0) }} {{ __('provider/bookings.currency') }}
                         </div>
                    </div>
                    <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                         <i class="ki-filled ki-dollar text-xl text-primary"></i>
                    </div>
               </div>
               <div class="flex items-center gap-1 mt-3">
                    <span class="kt-badge kt-badge-outline kt-badge-primary gap-1">
                         <i class="ki-filled ki-arrow-up text-2xs"></i>
                         {{ __('provider/bookings.statistics.this_month') }}
                    </span>
               </div>
          </div>
     </div>
</div>

<!-- Bookings List -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
     <!-- Bookings Table -->
     <div class="lg:col-span-2">
          <div class="card">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/bookings.list.title') }}</h3>
                    <div class="flex items-center gap-2">
                         <select class="select select-sm w-32" onchange="filterByStatus(this.value)">
                              <option value="">{{ __('provider/bookings.list.all_statuses') }}</option>
                              <option value="pending">{{ __('provider/bookings.status.pending') }}</option>
                              <option value="confirmed">{{ __('provider/bookings.status.confirmed') }}</option>
                              <option value="completed">{{ __('provider/bookings.status.completed') }}</option>
                              <option value="cancelled">{{ __('provider/bookings.status.cancelled') }}</option>
                         </select>
                    </div>
               </div>
               <div class="card-body p-0">
                    @if(isset($bookings) && $bookings->count() > 0)
                         <div class="flex flex-col">
                              {{-- Sample booking items - these would be populated from actual data --}}
                              <div class="flex items-center gap-4 p-5 border-b border-gray-200 hover:bg-gray-50">
                                   <div class="relative">
                                        <div class="flex items-center justify-center size-10 rounded-full bg-primary-light text-primary">
                                             JD
                                        </div>
                                        <span class="size-2.5 kt-badge kt-badge-circle kt-badge-warning absolute top-7 end-0.5 ring-1 ring-light transform -translate-y-1/2"></span>
                                   </div>
                                   <div class="flex-1">
                                        <div class="flex items-center justify-between mb-1">
                                             <h4 class="text-sm font-semibold text-gray-900">
                                                  {{ __('provider/bookings.sample.customer_name') }}
                                             </h4>
                                             <span class="kt-badge kt-badge-outline kt-badge-warning">
                                                  {{ __('provider/bookings.status.pending') }}
                                             </span>
                                        </div>
                                        <p class="text-2xs text-gray-600 mb-2">
                                             {{ __('provider/bookings.sample.service_name') }} • 150 {{ __('provider/bookings.currency') }}
                                        </p>
                                        <div class="flex items-center gap-4 text-2xs text-gray-500">
                                             <span class="flex items-center gap-1">
                                                  <i class="ki-filled ki-calendar text-xs"></i>
                                                  {{ __('provider/bookings.sample.date') }}
                                             </span>
                                             <span class="flex items-center gap-1">
                                                  <i class="ki-filled ki-clock text-xs"></i>
                                                  {{ __('provider/bookings.sample.time') }}
                                             </span>
                                        </div>
                                   </div>
                                   <div class="flex items-center gap-1">
                                        <button class="btn btn-icon btn-sm btn-light" onclick="viewBooking(1)">
                                             <i class="ki-filled ki-eye"></i>
                                        </button>
                                        <button class="btn btn-icon btn-sm btn-success" onclick="confirmBooking(1)">
                                             <i class="ki-filled ki-check"></i>
                                        </button>
                                        <button class="btn btn-icon btn-sm btn-light text-red-600" onclick="cancelBooking(1)">
                                             <i class="ki-filled ki-cross"></i>
                                        </button>
                                   </div>
                              </div>

                              <div class="flex items-center gap-4 p-5 border-b border-gray-200 hover:bg-gray-50">
                                   <div class="relative">
                                        <div class="flex items-center justify-center size-10 rounded-full bg-success-light text-success">
                                             SW
                                        </div>
                                        <span class="size-2.5 kt-badge kt-badge-circle kt-badge-success absolute top-7 end-0.5 ring-1 ring-light transform -translate-y-1/2"></span>
                                   </div>
                                   <div class="flex-1">
                                        <div class="flex items-center justify-between mb-1">
                                             <h4 class="text-sm font-semibold text-gray-900">
                                                  {{ __('provider/bookings.sample.customer_name_2') }}
                                             </h4>
                                             <span class="kt-badge kt-badge-outline kt-badge-success">
                                                  {{ __('provider/bookings.status.completed') }}
                                             </span>
                                        </div>
                                        <p class="text-2xs text-gray-600 mb-2">
                                             {{ __('provider/bookings.sample.service_name_2') }} • 200 {{ __('provider/bookings.currency') }}
                                        </p>
                                        <div class="flex items-center gap-4 text-2xs text-gray-500">
                                             <span class="flex items-center gap-1">
                                                  <i class="ki-filled ki-calendar text-xs"></i>
                                                  {{ __('provider/bookings.sample.date_2') }}
                                             </span>
                                             <span class="flex items-center gap-1">
                                                  <i class="ki-filled ki-star text-xs text-warning"></i>
                                                  5.0 {{ __('provider/bookings.rating') }}
                                             </span>
                                        </div>
                                   </div>
                                   <div class="flex items-center gap-1">
                                        <button class="btn btn-icon btn-sm btn-light" onclick="viewBooking(2)">
                                             <i class="ki-filled ki-eye"></i>
                                        </button>
                                        <button class="btn btn-icon btn-sm btn-light" onclick="downloadInvoice(2)">
                                             <i class="ki-filled ki-document"></i>
                                        </button>
                                   </div>
                              </div>
                         </div>

                         <!-- Pagination -->
                         <div class="card-footer justify-center md:justify-between flex-col md:flex-row gap-3 text-gray-600 text-2sm font-medium">
                              <div class="flex items-center gap-2 order-2 md:order-1">
                                   {{ __('provider/bookings.pagination.showing') }} 1 {{ __('provider/bookings.pagination.to') }} 10 {{ __('provider/bookings.pagination.of') }} 25 {{ __('provider/bookings.pagination.results') }}
                              </div>
                              <div class="pagination order-1 md:order-2">
                                   <button class="btn btn-sm btn-light" disabled>
                                        <i class="ki-filled ki-black-left"></i>
                                   </button>
                                   <button class="btn btn-sm btn-primary">1</button>
                                   <button class="btn btn-sm btn-light">2</button>
                                   <button class="btn btn-sm btn-light">3</button>
                                   <button class="btn btn-sm btn-light">
                                        <i class="ki-filled ki-black-right"></i>
                                   </button>
                              </div>
                         </div>
                    @else
                         <div class="text-center py-20">
                              <i class="ki-filled ki-calendar-2 text-6xl text-gray-300 mb-5"></i>
                              <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                   {{ __('provider/bookings.empty.title') }}
                              </h3>
                              <p class="text-gray-600 mb-5">
                                   {{ __('provider/bookings.empty.description') }}
                              </p>
                         </div>
                    @endif
               </div>
          </div>
     </div>

     <!-- Quick Actions & Calendar -->
     <div class="lg:col-span-1">
          <!-- Quick Actions -->
          <div class="card mb-5">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/bookings.quick_actions.title') }}</h3>
               </div>
               <div class="card-body">
                    <div class="flex flex-col gap-3">
                         <button class="btn btn-light justify-start" onclick="showPendingBookings()">
                              <i class="ki-filled ki-clock"></i>
                              {{ __('provider/bookings.quick_actions.pending_bookings') }}
                              <span class="kt-badge kt-badge-outline kt-badge-warning ms-auto">3</span>
                         </button>
                         <button class="btn btn-light justify-start" onclick="showTodayBookings()">
                              <i class="ki-filled ki-calendar-2"></i>
                              {{ __('provider/bookings.quick_actions.today_bookings') }}
                              <span class="kt-badge kt-badge-outline kt-badge-info ms-auto">2</span>
                         </button>
                         <button class="btn btn-light justify-start" onclick="showUpcomingBookings()">
                              <i class="ki-filled ki-calendar"></i>
                              {{ __('provider/bookings.quick_actions.upcoming_bookings') }}
                              <span class="kt-badge kt-badge-outline kt-badge-primary ms-auto">5</span>
                         </button>
                         <button class="btn btn-light justify-start" onclick="exportBookings()">
                              <i class="ki-filled ki-document"></i>
                              {{ __('provider/bookings.quick_actions.export_data') }}
                         </button>
                    </div>
               </div>
          </div>

          <!-- Mini Calendar -->
          <div class="card">
               <div class="card-header">
                    <h3 class="card-title">{{ __('provider/bookings.calendar.title') }}</h3>
               </div>
               <div class="card-body">
                    <div id="mini_calendar" class="text-center">
                         <p class="text-gray-500 text-sm">
                              {{ __('provider/bookings.calendar.placeholder') }}
                         </p>
                    </div>
               </div>
          </div>
     </div>
</div>
@endsection

@push('scripts')
<script>
     function filterByStatus(status) {
          const url = new URL(window.location);
          if (status) {
               url.searchParams.set('status', status);
          } else {
               url.searchParams.delete('status');
          }
          window.location.href = url.toString();
     }

     function refreshBookings() {
          window.location.reload();
     }

     function viewBooking(id) {
          console.log('View booking:', id);
          // Implement view booking functionality
     }

     function confirmBooking(id) {
          if (confirm('{{ __("provider/bookings.messages.confirm_booking") }}')) {
               console.log('Confirm booking:', id);
               // Implement confirm booking functionality
          }
     }

     function cancelBooking(id) {
          if (confirm('{{ __("provider/bookings.messages.cancel_booking") }}')) {
               console.log('Cancel booking:', id);
               // Implement cancel booking functionality
          }
     }

     function downloadInvoice(id) {
          console.log('Download invoice for booking:', id);
          // Implement download invoice functionality
     }

     function showPendingBookings() {
          filterByStatus('pending');
     }

     function showTodayBookings() {
          const today = new Date().toISOString().split('T')[0];
          const url = new URL(window.location);
          url.searchParams.set('date', today);
          window.location.href = url.toString();
     }

     function showUpcomingBookings() {
          const url = new URL(window.location);
          url.searchParams.set('upcoming', '1');
          window.location.href = url.toString();
     }

     function exportBookings() {
          console.log('Export bookings');
          // Implement export functionality
     }
</script>
@endpush
