@extends('provider.layouts.app')

@section('title', __('provider/services.title') . ' - ' . config('app.name'))

@section('page-title', __('provider/services.page_title'))

@section('page-description', __('provider/services.page_description'))

@section('page-actions')
      <div class="flex items-center gap-2.5">
              <a href="{{ route('provider.services.create') }}" class="btn btn-primary">
                        <i class="ki-filled ki-plus"></i>
                        {{ __('provider/services.actions.add_service') }}
              </a>
              <div class="dropdown" data-dropdown="true" data-dropdown-trigger="click">
                        <button class="dropdown-toggle btn btn-light">
                                <i class="ki-filled ki-setting-2"></i>
                                {{ __('provider/services.actions.bulk_actions') }}
                                <i class="ki-filled ki-down text-2xs"></i>
                        </button>
                        <div class="dropdown-content w-48">
                                <div class="menu menu-default">
                                          <div class="menu-item">
                                                  <button class="menu-link" onclick="bulkAction('activate')">
                                                            <span class="menu-icon"><i class="ki-filled ki-check-circle"></i></span>
                                                            <span
                                                                    class="menu-title">{{ __('provider/services.actions.activate_selected') }}</span>
                                                  </button>
                                          </div>
                                          <div class="menu-item">
                                                  <button class="menu-link" onclick="bulkAction('deactivate')">
                                                            <span class="menu-icon"><i class="ki-filled ki-cross-circle"></i></span>
                                                            <span
                                                                    class="menu-title">{{ __('provider/services.actions.deactivate_selected') }}</span>
                                                  </button>
                                          </div>
                                </div>
                        </div>
              </div>
      </div>
@endsection

@section('content')
      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
              <!-- Total Services -->
              <div class="card">
                        <div class="card-body">
                                <div class="flex items-center justify-between">
                                          <div>
                                                  <div class="text-2sm font-medium text-gray-600 mb-1">
                                                            {{ __('provider/services.statistics.total_services') }}
                                                  </div>
                                                  <div class="text-2xl font-semibold text-gray-900">
                                                            {{ $statistics['total'] ?? 0 }}
                                                  </div>
                                          </div>
                                          <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                                                  <i class="ki-filled ki-setting-2 text-xl text-primary"></i>
                                          </div>
                                </div>
                        </div>
              </div>

              <!-- Active Services -->
              <div class="card">
                        <div class="card-body">
                                <div class="flex items-center justify-between">
                                          <div>
                                                  <div class="text-2sm font-medium text-gray-600 mb-1">
                                                            {{ __('provider/services.statistics.active_services') }}
                                                  </div>
                                                  <div class="text-2xl font-semibold text-gray-900">
                                                            {{ $statistics['active'] ?? 0 }}
                                                  </div>
                                          </div>
                                          <div class="flex items-center justify-center size-12 rounded-lg bg-success-light">
                                                  <i class="ki-filled ki-check-circle text-xl text-success"></i>
                                          </div>
                                </div>
                        </div>
              </div>

              <!-- Average Price -->
              <div class="card">
                        <div class="card-body">
                                <div class="flex items-center justify-between">
                                          <div>
                                                  <div class="text-2sm font-medium text-gray-600 mb-1">
                                                            {{ __('provider/services.statistics.average_price') }}
                                                  </div>
                                                  <div class="text-2xl font-semibold text-gray-900">
                                                            {{ number_format($statistics['average_price'] ?? 0) }}
                                                            {{ __('provider/services.currency') }}
                                                  </div>
                                          </div>
                                          <div class="flex items-center justify-center size-12 rounded-lg bg-info-light">
                                                  <i class="ki-filled ki-dollar text-xl text-info"></i>
                                          </div>
                                </div>
                        </div>
              </div>

              <!-- Total Bookings -->
              <div class="card">
                        <div class="card-body">
                                <div class="flex items-center justify-between">
                                          <div>
                                                  <div class="text-2sm font-medium text-gray-600 mb-1">
                                                            {{ __('provider/services.statistics.total_bookings') }}
                                                  </div>
                                                  <div class="text-2xl font-semibold text-gray-900">
                                                            {{ $statistics['total_bookings'] ?? 0 }}
                                                  </div>
                                          </div>
                                          <div class="flex items-center justify-center size-12 rounded-lg bg-warning-light">
                                                  <i class="ki-filled ki-calendar text-xl text-warning"></i>
                                          </div>
                                </div>
                        </div>
              </div>
      </div>

      <!-- Search and Filters -->
      <div class="card mb-5 lg:mb-7.5">
              <div class="card-body">
                        <form method="GET" action="{{ route('provider.services.index') }}"
                                class="flex flex-wrap items-center gap-4">
                                <!-- Search -->
                                <div class="flex items-center gap-2 flex-1 min-w-[200px]">
                                          <i class="ki-filled ki-magnifier text-gray-500"></i>
                                          <input class="input border-0 bg-transparent shadow-none flex-1" name="search" type="text"
                                                  value="{{ request('search') }}"
                                                  placeholder="{{ __('provider/services.search.placeholder') }}" />
                                </div>

                                <!-- Status Filter -->
                                <select class="select w-40" name="status">
                                          <option value="">{{ __('provider/services.filters.all_statuses') }}</option>
                                          <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>
                                                  {{ __('provider/services.filters.active') }}
                                          </option>
                                          <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>
                                                  {{ __('provider/services.filters.inactive') }}
                                          </option>
                                </select>

                                <!-- Category Filter -->
                                <select class="select w-40" name="category">
                                          <option value="">{{ __('provider/services.filters.all_categories') }}</option>
                                          {{-- Categories will be populated here --}}
                                </select>

                                <!-- Sort -->
                                <select class="select w-40" name="sort">
                                          <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>
                                                  {{ __('provider/services.sort.name') }}
                                          </option>
                                          <option value="price" {{ request('sort') === 'price' ? 'selected' : '' }}>
                                                  {{ __('provider/services.sort.price') }}
                                          </option>
                                          <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>
                                                  {{ __('provider/services.sort.newest') }}
                                          </option>
                                </select>

                                <!-- Search Button -->
                                <button type="submit" class="btn btn-primary">
                                          <i class="ki-filled ki-magnifier"></i>
                                          {{ __('provider/services.actions.search') }}
                                </button>

                                <!-- Reset Button -->
                                @if(request()->hasAny(['search', 'status', 'category', 'sort']))
                                                      <a href="{{ route('provider.services.index') }}" class="btn btn-light">
                                                                    <i class="ki-filled ki-arrows-circle"></i>
                                                                    {{ __('provider/services.actions.reset') }}
                                                      </a>
                                            @endif
                        </form>
              </div>
      </div>

      <!-- Services Table -->
      <div class="card">
              <div class="card-header">
                        <h3 class="card-title">{{ __('provider/services.table.title') }}</h3>
                        <div class="flex items-center gap-2">
                                <span class="text-2sm text-gray-600">
                                          {{ __('provider/services.table.showing_results', ['total' => $services->count() ?? 0]) }}
                                </span>
                        </div>
              </div>
              <div class="card-body p-0">
                        @if(isset($services) && $services->count() > 0)
                                          <div class="table-responsive">
                                                      <table class="table table-auto table-border">
                                                                    <thead>
                                                                                <tr>
                                                                                                <th class="w-[50px]">
                                                                                                            <label class="checkbox-group">
                                                                                                                          <input class="checkbox" type="checkbox" id="select_all" />
                                                                                                                          <span class="checkbox-label"></span>
                                                                                                            </label>
                                                                                                </th>
                                                                                                <th class="min-w-[200px]">{{ __('provider/services.table.service') }}</th>
                                                                                                <th class="min-w-[100px]">{{ __('provider/services.table.category') }}</th>
                                                                                                <th class="min-w-[100px]">{{ __('provider/services.table.price') }}</th>
                                                                                                <th class="min-w-[100px]">{{ __('provider/services.table.status') }}</th>
                                                                                                <th class="min-w-[100px]">{{ __('provider/services.table.bookings') }}</th>
                                                                                                <th class="min-w-[100px]">{{ __('provider/services.table.rating') }}</th>
                                                                                                <th class="w-[120px]">{{ __('provider/services.table.actions') }}</th>
                                                                                </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                                {{-- Sample service rows - these would be populated from actual data --}}
                                                                                <tr>
                                                                                                <td>
                                                                                                            <label class="checkbox-group">
                                                                                                                          <input class="checkbox service-checkbox" type="checkbox" value="1" />
                                                                                                                          <span class="checkbox-label"></span>
                                                                                                            </label>
                                                                                                </td>
                                                                                                <td>
                                                                                                            <div class="flex items-center gap-3">
                                                                                                                          <div
                                                                                                                                      class="flex items-center justify-center size-10 rounded-lg bg-gray-100">
                                                                                                                                      <i class="ki-filled ki-setting-2 text-gray-600"></i>
                                                                                                                          </div>
                                                                                                                          <div>
                                                                                                                                      <a href="#"
                                                                                                                                                      class="text-sm font-semibold text-gray-900 hover:text-primary">
                                                                                                                                                      {{ __('provider/services.sample.service_name') }}
                                                                                                                                      </a>
                                                                                                                                      <div class="text-2xs text-gray-600">
                                                                                                                                                      {{ __('provider/services.sample.service_description') }}
                                                                                                                                      </div>
                                                                                                                          </div>
                                                                                                            </div>
                                                                                                </td>
                                                                                                <td>
                                                                                                            <span class="kt-badge kt-badge-outline kt-badge-primary">
                                                                                                                          {{ __('provider/services.sample.category') }}
                                                                                                            </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                            <span class="text-sm font-semibold text-gray-900">
                                                                                                                          150 {{ __('provider/services.currency') }}
                                                                                                            </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                            <span class="kt-badge kt-badge-outline kt-badge-success">
                                                                                                                          {{ __('provider/services.status.active') }}
                                                                                                            </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                            <span class="text-sm text-gray-900">25</span>
                                                                                                </td>
                                                                                                <td>
                                                                                                            <div class="flex items-center gap-1">
                                                                                                                          <i class="ki-filled ki-star text-warning text-sm"></i>
                                                                                                                          <span class="text-sm text-gray-900">4.8</span>
                                                                                                                          <span class="text-2xs text-gray-500">(12)</span>
                                                                                                            </div>
                                                                                                </td>
                                                                                                <td>
                                                                                                            <div class="flex items-center gap-1">
                                                                                                                          <a href="#" class="btn btn-icon btn-sm btn-light">
                                                                                                                                      <i class="ki-filled ki-eye"></i>
                                                                                                                          </a>
                                                                                                                          <a href="#" class="btn btn-icon btn-sm btn-light">
                                                                                                                                      <i class="ki-filled ki-notepad-edit"></i>
                                                                                                                          </a>
                                                                                                                          <button
                                                                                                                                      class="btn btn-icon btn-sm btn-light text-red-600 hover:text-red-700">
                                                                                                                                      <i class="ki-filled ki-trash"></i>
                                                                                                                          </button>
                                                                                                            </div>
                                                                                                </td>
                                                                                </tr>
                                                                    </tbody>
                                                      </table>
                                          </div>

                                          <!-- Pagination -->
                                          <div
                                                      class="card-footer justify-center md:justify-between flex-col md:flex-row gap-3 text-gray-600 text-2sm font-medium">
                                                      <div class="flex items-center gap-2 order-2 md:order-1">
                                                                    {{ __('provider/services.pagination.showing') }} 1 {{ __('provider/services.pagination.to') }}
                                                                    10
                                                                    {{ __('provider/services.pagination.of') }} 25 {{ __('provider/services.pagination.results') }}
                                                      </div>
                                                      <div class="pagination order-1 md:order-2">
                                                                    {{-- Pagination links would go here --}}
                                                                    <button class="btn btn-sm btn-light" disabled>
                                                                                <i class="ki-filled ki-black-left"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-primary">1</button>
                                                                    <button class="btn btn-sm btn-light">2</button>
                                                                    <button class="btn btn-sm btn-light">3</button>
                                                                    <button class="btn btn-sm btn-light">
                                                                                <i class="ki-filled ki-black-right"></i>
                                                                    </button>
                                                      </div>
                                          </div>
                                @else
                                          <div class="text-center py-20">
                                                      <i class="ki-filled ki-setting-2 text-6xl text-gray-300 mb-5"></i>
                                                      <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                                                    {{ __('provider/services.empty.title') }}
                                                      </h3>
                                                      <p class="text-gray-600 mb-5">
                                                                    {{ __('provider/services.empty.description') }}
                                                      </p>
                                                      <a href="{{ route('provider.services.create') }}" class="btn btn-primary">
                                                                    <i class="ki-filled ki-plus"></i>
                                                                    {{ __('provider/services.empty.add_first_service') }}
                                                      </a>
                                          </div>
                                @endif
              </div>
      </div>
@endsection

@push('scripts')
      <script>
              // Select all functionality
              document.addEventListener('DOMContentLoaded', function () {
                        const selectAllCheckbox = document.getElementById('select_all');
                        if (selectAllCheckbox) {
                                selectAllCheckbox.addEventListener('change', function () {
                                          const checkboxes = document.querySelectorAll('.service-checkbox');
                                          checkboxes.forEach(checkbox => {
                                                  checkbox.checked = this.checked;
                                          });
                                });
                        }
              });

              // Bulk actions
              function bulkAction(action) {
                        const selectedServices = Array.from(document.querySelectorAll('.service-checkbox:checked')).map(cb => cb.value);

                        if (selectedServices.length === 0) {
                                alert('{{ __("provider/services.messages.no_services_selected") }}');
                                return;
                        }

                        if (confirm('{{ __("provider/services.messages.confirm_bulk_action") }}')) {
                                // Perform bulk action
                                console.log('Bulk action:', action, 'Services:', selectedServices);
                        }
              }
      </script>
@endpush