@extends('provider.layouts.app')

@section('title', __('provider/services.create.title') . ' - ' . config('app.name'))

@section('page-title', __('provider/services.create.page_title'))

@section('page-description', __('provider/services.create.page_description'))

@section('page-actions')
    <div class="flex items-center gap-2.5">
        <a href="{{ route('provider.services.index') }}" class="btn btn-light">
            <i class="ki-filled ki-black-left"></i>
            {{ __('provider/services.create.actions.cancel') }}
        </a>
        <button type="submit" form="service_form" class="btn btn-primary">
            <i class="ki-filled ki-check"></i>
            {{ __('provider/services.create.actions.save') }}
        </button>
    </div>
@endsection

@section('content')
    <!-- Flash Messages -->
    @include('provider.components.flash-messages')

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">{{ __('provider/services.create.form_title') }}</h3>
        </div>
        <div class="card-body">
            <form id="service_form" method="POST" action="{{ route('provider.services.store') }}">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Service Name -->
                    <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                        <label class="form-label max-w-56">
                            {{ __('provider/services.create.fields.name') }}
                            <span class="text-red-500">*</span>
                        </label>
                        <input class="input @if(isset($errors) && $errors->has('name')) border-red-500 @endif" name="name"
                            type="text" value="{{ old('name') }}"
                            placeholder="{{ __('provider/services.create.placeholders.name') }}" required />
                        @if(isset($errors) && $errors->has('name'))
                            <div class="text-red-500 text-sm mt-1">{{ $errors->first('name') }}</div>
                        @endif
                    </div>

                    <!-- Category -->
                    <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                        <label class="form-label max-w-56">
                            {{ __('provider/services.create.fields.category') }}
                            <span class="text-red-500">*</span>
                        </label>
                        <select class="select @if(isset($errors) && $errors->has('category_id')) border-red-500 @endif"
                            name="category_id" required>
                            <option value="">{{ __('provider/services.create.placeholders.select_category') }}</option>
                            {{-- Categories will be populated here from controller --}}
                            @if(isset($categories))
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @if(isset($errors) && $errors->has('category_id'))
                            <div class="text-red-500 text-sm mt-1">{{ $errors->first('category_id') }}</div>
                        @endif
                    </div>

                    <!-- Price -->
                    <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                        <label class="form-label max-w-56">
                            {{ __('provider/services.create.fields.price') }}
                            <span class="text-red-500">*</span>
                        </label>
                        <div class="relative flex-1">
                            <input class="input @if(isset($errors) && $errors->has('price')) border-red-500 @endif"
                                name="price" type="number" step="0.01" min="0" value="{{ old('price') }}"
                                placeholder="{{ __('provider/services.create.placeholders.price') }}" required />
                            <span class="absolute inset-y-0 right-3 flex items-center text-gray-500">
                                {{ __('provider/services.currency') }}
                            </span>
                        </div>
                        @if(isset($errors) && $errors->has('price'))
                            <div class="text-red-500 text-sm mt-1">{{ $errors->first('price') }}</div>
                        @endif
                    </div>

                    <!-- Duration -->
                    <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                        <label class="form-label max-w-56">
                            {{ __('provider/services.create.fields.duration') }}
                            <span class="text-red-500">*</span>
                        </label>
                        <input class="input @if(isset($errors) && $errors->has('duration')) border-red-500 @endif"
                            name="duration" type="number" min="1" value="{{ old('duration') }}"
                            placeholder="{{ __('provider/services.create.placeholders.duration') }}" required />
                        @if(isset($errors) && $errors->has('duration'))
                            <div class="text-red-500 text-sm mt-1">{{ $errors->first('duration') }}</div>
                        @endif
                    </div>

                    <!-- Status -->
                    <div class="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
                        <label class="form-label max-w-56">
                            {{ __('provider/services.create.fields.status') }}
                        </label>
                        <select class="select @if(isset($errors) && $errors->has('status')) border-red-500 @endif"
                            name="status">
                            <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>
                                {{ __('provider/services.status.active') }}
                            </option>
                            <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>
                                {{ __('provider/services.status.inactive') }}
                            </option>
                        </select>
                        @if(isset($errors) && $errors->has('status'))
                            <div class="text-red-500 text-sm mt-1">{{ $errors->first('status') }}</div>
                        @endif
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <div class="flex items-start flex-wrap lg:flex-nowrap gap-2.5">
                            <label class="form-label max-w-56">
                                {{ __('provider/services.create.fields.description') }}
                            </label>
                            <div class="flex-1">
                                <textarea class="input @if(isset($errors) && $errors->has('description')) border-red-500 @endif" name="description"
                                    rows="4" cols="50"
                                    placeholder="{{ __('provider/services.create.placeholders.description') }}">{{ old('description') }}</textarea>
                                @if(isset($errors) && $errors->has('description'))
                                    <div class="text-red-500 text-sm mt-1">{{ $errors->first('description') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="card-footer">
            <div class="flex items-center justify-between">
                <span class="text-2xs text-gray-500">
                    <span class="text-red-500">*</span> {{ __('provider/common.form.required') }}
                </span>
                <div class="flex items-center gap-2.5">
                    <a href="{{ route('provider.services.index') }}" class="btn btn-light">
                        {{ __('provider/services.create.actions.cancel') }}
                    </a>
                    <button type="submit" form="service_form" class="btn btn-primary">
                        <i class="ki-filled ki-check"></i>
                        {{ __('provider/services.create.actions.save') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Form validation
            const form = document.getElementById('service_form');
            if (form) {
                form.addEventListener('submit', function (e) {
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            field.classList.add('border-red-500');
                            isValid = false;
                        } else {
                            field.classList.remove('border-red-500');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        alert('{{ __("provider/common.form.required_fields_missing") }}');
                    }
                });
            }
        });
    </script>
@endpush