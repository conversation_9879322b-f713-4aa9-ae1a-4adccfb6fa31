@extends('provider.layouts.app')

@section('title', __('provider/profile.basic_info.title') . ' - ' . config('app.name'))

@section('page-title', __('provider/profile.basic_info.page_title'))

@section('page-description', __('provider/profile.basic_info.page_description'))

@section('page-actions')
      <div class="flex items-center gap-2.5">
              <button type="submit" form="basic_info_form" class="btn btn-primary">
                        <i class="ki-filled ki-check"></i>
                        {{ __('provider/profile.basic_info.actions.save_changes') }}
              </button>
      </div>
@endsection

@section('content')
      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
              <!-- Profile Completion -->
              <div class="card">
                        <div class="card-body">
                                <div class="flex items-center justify-between">
                                          <div>
                                                  <div class="text-2sm font-medium text-gray-600 mb-1">
                                                            {{ __('provider/profile.basic_info.statistics.profile_completion') }}
                                                  </div>
                                                  <div class="text-2xl font-semibold text-gray-900">
                                                            85%
                                                  </div>
                                          </div>
                                          <div class="flex items-center justify-center size-12 rounded-lg bg-info-light">
                                                  <i class="ki-filled ki-chart-pie-simple text-xl text-info"></i>
                                          </div>
                                </div>
                        </div>
              </div>

              <!-- Total Services -->
              <div class="card">
                        <div class="card-body">
                                <div class="flex items-center justify-between">
                                          <div>
                                                  <div class="text-2sm font-medium text-gray-600 mb-1">
                                                            {{ __('provider/profile.basic_info.statistics.total_services') }}
                                                  </div>
                                                  <div class="text-2xl font-semibold text-gray-900">
                                                            12
                                                  </div>
                                          </div>
                                          <div class="flex items-center justify-center size-12 rounded-lg bg-primary-light">
                                                  <i class="ki-filled ki-setting-4 text-xl text-primary"></i>
                                          </div>
                                </div>
                        </div>
              </div>

              <!-- Total Reviews -->
              <div class="card">
                        <div class="card-body">
                                <div class="flex items-center justify-between">
                                          <div>
                                                  <div class="text-2sm font-medium text-gray-600 mb-1">
                                                            {{ __('provider/profile.basic_info.statistics.total_reviews') }}
                                                  </div>
                                                  <div class="text-2xl font-semibold text-gray-900">
                                                            48
                                                  </div>
                                          </div>
                                          <div class="flex items-center justify-center size-12 rounded-lg bg-warning-light">
                                                  <i class="ki-filled ki-star text-xl text-warning"></i>
                                          </div>
                                </div>
                        </div>
              </div>

              <!-- Average Rating -->
              <div class="card">
                        <div class="card-body">
                                <div class="flex items-center justify-between">
                                          <div>
                                                  <div class="text-2sm font-medium text-gray-600 mb-1">
                                                            {{ __('provider/profile.basic_info.statistics.average_rating') }}
                                                  </div>
                                                  <div class="text-2xl font-semibold text-gray-900">
                                                            4.8
                                                  </div>
                                          </div>
                                          <div class="flex items-center justify-center size-12 rounded-lg bg-success-light">
                                                  <i class="ki-filled ki-medal-star text-xl text-success"></i>
                                          </div>
                                </div>
                        </div>
              </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
              <!-- Profile Image -->
              <div class="lg:col-span-1">
                <div class="card border-0">
                        @if($provider->logo)
                                <img alt="{{ $provider->name }}" class="w-full h-auto rounded-t-xl" src="{{ asset('storage/' . $provider->logo) }}">
                        @else
                                <div class="w-full h-64 bg-gray-100 rounded-t-xl flex items-center justify-center">
                                        <div class="text-center">
                                                <i class="ki-filled ki-picture text-4xl text-gray-400 mb-2"></i>
                                                <p class="text-gray-500">{{ __('provider/profile.basic_info.sections.no_image') }}</p>
                                        </div>
                                </div>
                        @endif
                        <div class="card-border card-rounded-b flex justify-center gap-2 px-5 py-4.5">
                                <label for="logo_upload" class="btn btn-primary cursor-pointer">
                                        <i class="ki-filled ki-cloud-upload"></i>
                                        {{ __('provider/profile.basic_info.actions.upload_image') }}
                                </label>
                                @if($provider->logo)
                                        <button type="button" class="btn btn-light" onclick="removeLogo()">
                                                <i class="ki-filled ki-trash"></i>
                                                {{ __('provider/profile.basic_info.actions.remove_image') }}
                                        </button>
                                @endif
                        </div>
                </div>
              </div>

              <!-- Basic Information Form -->
              <div class="lg:col-span-2">
                        <div class="card">
                                <div class="card-header">
                                        <h3 class="card-title">{{ __('provider/profile.basic_info.sections.basic_information') }}</h3>
                                </div>
                                <div class="card-body">
                                          <form id="basic_info_form" method="POST"
                                                  action="{{ route('provider.profile.basic-info.update') }}" enctype="multipart/form-data">
                                                  @csrf
                                                  @method('PUT')

                                                  <!-- Hidden file input for logo upload -->
                                                  <input type="file" id="logo_upload" name="logo" accept="image/jpeg,image/png,image/jpg" style="display: none;" onchange="previewLogo(this)">

                                                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <!-- Arabic Name -->
                                                            <div class="flex flex-col gap-1">
                                                                    <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.name_arabic') }}</label>
                                                                    <input type="text" name="translations[ar][name]"
                                                                              value="{{ old('translations.ar.name', $provider->translate('ar')->name ?? '') }}"
                                                                              class="input @error('translations.ar.name') border-danger @enderror"
                                                                              placeholder="{{ __('provider/profile.basic_info.placeholders.name_arabic') }}"
                                                                              required>
                                                                    @error('translations.ar.name')
                                                                              <div class="form-hint text-danger">{{ $message }}</div>
                                                                    @enderror
                                                            </div>

                                                            <!-- English Name -->
                                                            <div class="flex flex-col gap-1">
                                                                    <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.name_english') }}</label>
                                                                    <input type="text" name="translations[en][name]"
                                                                              value="{{ old('translations.en.name', $provider->translate('en')->name ?? '') }}"
                                                                              class="input @error('translations.en.name') border-danger @enderror"
                                                                              placeholder="{{ __('provider/profile.basic_info.placeholders.name_english') }}"
                                                                              required>
                                                                    @error('translations.en.name')
                                                                              <div class="form-hint text-danger">{{ $message }}</div>
                                                                    @enderror
                                                            </div>

                                                            <!-- Arabic Description -->
                                                            <div class="md:col-span-2">
                                                                    <div class="flex flex-col gap-1">
                                                                              <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.description_arabic') }}</label>
                                                                              <textarea name="translations[ar][description]" rows="4" cols="50"
                                                                                      class="textarea @error('translations.ar.description') border-danger @enderror"
                                                                                      placeholder="{{ __('provider/profile.basic_info.placeholders.description_arabic') }}">{{ old('translations.ar.description', $provider->translate('ar')->description ?? '') }}</textarea>
                                                                              @error('translations.ar.description')
                                                                                      <div class="form-hint text-danger">{{ $message }}</div>
                                                                              @enderror
                                                                    </div>
                                                            </div>

                                                            <!-- English Description -->
                                                            <div class="md:col-span-2">
                                                                    <div class="flex flex-col gap-1">
                                                                              <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.description_english') }}</label>
                                                                              <textarea name="translations[en][description]" rows="4" cols="50"
                                                                                      class="textarea @error('translations.en.description') border-danger @enderror"
                                                                                      placeholder="{{ __('provider/profile.basic_info.placeholders.description_english') }}">{{ old('translations.en.description', $provider->translate('en')->description ?? '') }}</textarea>
                                                                              @error('translations.en.description')
                                                                                      <div class="form-hint text-danger">{{ $message }}</div>
                                                                              @enderror
                                                                    </div>
                                                            </div>

                                                            <!-- Email -->
                                                            <div class="flex flex-col gap-1">
                                                                    <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.email') }}</label>
                                                                    <input type="email" name="email"
                                                                              value="{{ old('email', $provider->email) }}"
                                                                              class="input @error('email') border-danger @enderror"
                                                                              placeholder="{{ __('provider/profile.basic_info.placeholders.email') }}"
                                                                              required>
                                                                    @error('email')
                                                                              <div class="form-hint text-danger">{{ $message }}</div>
                                                                    @enderror
                                                            </div>

                                                            
                                                  </div>
                                          </form>
                                </div>
                        </div>


                        <div class="card mt-4">
                                <div class="card-header">
                                        <h3 class="card-title">{{ __('provider/profile.basic_info.sections.basic_information') }}</h3>
                                </div>
                                <div class="card-body">
                                          <form id="basic_info_form" method="POST"
                                                  action="{{ route('provider.profile.basic-info.update') }}" enctype="multipart/form-data">
                                                  @csrf
                                                  @method('PUT')
                                                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
<!-- City -->
                                                            <div class="flex flex-col gap-1">
                                                                    <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.city') }}</label>
                                                                    <select name="city_id" id="city_select"
                                                                              class="select @error('city_id') border-danger @enderror" required>
                                                                              <option value="">{{ __('provider/profile.basic_info.placeholders.select_city') }}</option>
                                                                              @foreach($cities as $city)
                                                                                      <option value="{{ $city->id }}" {{ old('city_id', $provider->city_id) == $city->id ? 'selected' : '' }}>
                                                                                              {{ $city->name }}
                                                                                      </option>
                                                                              @endforeach
                                                                    </select>
                                                                    @error('city_id')
                                                                              <div class="form-hint text-danger">{{ $message }}</div>
                                                                    @enderror
                                                            </div>

                                                            <!-- Area -->
                                                            <div class="flex flex-col gap-1">
                                                                    <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.area') }}</label>
                                                                    <div class="relative">
                                                                              <select name="area_id" id="area_select"
                                                                                      class="select @error('area_id') border-danger @enderror" required>
                                                                                      <option value="">{{ __('provider/profile.basic_info.placeholders.select_area') }}</option>
                                                                                      @foreach($areas as $area)
                                                                                              <option value="{{ $area->id }}" {{ old('area_id', $provider->area_id) == $area->id ? 'selected' : '' }}>
                                                                                                      {{ $area->name }}
                                                                                              </option>
                                                                                      @endforeach
                                                                              </select>
                                                                              <div id="area-loading" class="absolute inset-y-0 right-8 flex items-center" style="display: none;">
                                                                                      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                                                                              </div>
                                                                    </div>
                                                                    @error('area_id')
                                                                              <div class="form-hint text-danger">{{ $message }}</div>
                                                                    @enderror
                                                            </div>

                                                            <!-- Latitude -->
                                                            <div class="flex flex-col gap-1">
                                                                    <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.latitude') }}</label>
                                                                    <input type="number" name="latitude" step="any"
                                                                              value="{{ old('latitude', $provider->latitude) }}"
                                                                              class="input @error('latitude') border-danger @enderror"
                                                                              placeholder="{{ __('provider/profile.basic_info.placeholders.latitude') }}">
                                                                    @error('latitude')
                                                                              <div class="form-hint text-danger">{{ $message }}</div>
                                                                    @enderror
                                                            </div>

                                                            <!-- Longitude -->
                                                            <div class="flex flex-col gap-1">
                                                                    <label class="form-label text-gray-900">{{ __('provider/profile.basic_info.fields.longitude') }}</label>
                                                                    <input type="number" name="longitude" step="any"
                                                                              value="{{ old('longitude', $provider->longitude) }}"
                                                                              class="input @error('longitude') border-danger @enderror"
                                                                              placeholder="{{ __('provider/profile.basic_info.placeholders.longitude') }}">
                                                                    @error('longitude')
                                                                              <div class="form-hint text-danger">{{ $message }}</div>
                                                                    @enderror
                                                            </div>
                                                  </div>
                                          </form>
                                </div>
                        </div>
              </div>
      </div>
@endsection

@push('scripts')
      <script>
              // City and Area dynamic loading
              document.addEventListener('DOMContentLoaded', function () {
                        const citySelect = document.getElementById('city_select');
                        const areaSelect = document.getElementById('area_select');
                        const areaLoading = document.getElementById('area-loading');
                        const areasUrl = '{{ route('provider.profile.areas-by-city') }}';

                        function showLoading() {
                                areaLoading.style.display = 'block';
                                areaSelect.disabled = true;
                        }

                        function hideLoading() {
                                areaLoading.style.display = 'none';
                                areaSelect.disabled = false;
                        }

                        function clearAreaOptions() {
                                areaSelect.innerHTML = '<option value="">{{ __("provider/profile.basic_info.placeholders.select_area") }}</option>';
                        }

                        function populateAreas(areas) {
                                clearAreaOptions();
                                areas.forEach(function(area) {
                                        const option = document.createElement('option');
                                        option.value = area.id;
                                        option.textContent = area.name;
                                        areaSelect.appendChild(option);
                                });
                        }

                        citySelect.addEventListener('change', function () {
                                const cityId = this.value;

                                if (!cityId) {
                                        clearAreaOptions();
                                        areaSelect.disabled = true;
                                        return;
                                }

                                showLoading();

                                // Make AJAX request to fetch areas
                                fetch(`${areasUrl}?city_id=${cityId}`, {
                                        method: 'GET',
                                        headers: {
                                                'X-Requested-With': 'XMLHttpRequest',
                                                'Accept': 'application/json',
                                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                        }
                                })
                                .then(response => {
                                        if (!response.ok) {
                                                throw new Error('Network response was not ok');
                                        }
                                        return response.json();
                                })
                                .then(data => {
                                        hideLoading();

                                        if (data.success) {
                                                populateAreas(data.data);
                                        } else {
                                                clearAreaOptions();
                                                areaSelect.disabled = true;
                                                console.error('Error loading areas:', data.message);
                                        }
                                })
                                .catch(error => {
                                        hideLoading();
                                        clearAreaOptions();
                                        areaSelect.disabled = true;
                                        console.error('Error:', error);
                                });
                        });
              });

              // Logo preview and management functions
              function previewLogo(input) {
                        if (input.files && input.files[0]) {
                                const reader = new FileReader();
                                reader.onload = function(e) {
                                        const imgContainer = document.querySelector('.card.border-0');
                                        imgContainer.innerHTML = `
                                                <img alt="Logo Preview" class="w-full h-auto rounded-t-xl" src="${e.target.result}">
                                                <div class="card-border card-rounded-b flex justify-center gap-2 px-5 py-4.5">
                                                        <label for="logo_upload" class="btn btn-primary cursor-pointer">
                                                                <i class="ki-filled ki-cloud-upload"></i>
                                                                {{ __('provider/profile.basic_info.actions.upload_image') }}
                                                        </label>
                                                        <button type="button" class="btn btn-light" onclick="removeLogo()">
                                                                <i class="ki-filled ki-trash"></i>
                                                                {{ __('provider/profile.basic_info.actions.remove_image') }}
                                                        </button>
                                                </div>
                                        `;
                                };
                                reader.readAsDataURL(input.files[0]);
                        }
              }

              function removeLogo() {
                        document.getElementById('logo_upload').value = '';
                        const imgContainer = document.querySelector('.card.border-0');
                        imgContainer.innerHTML = `
                                <div class="w-full h-64 bg-gray-100 rounded-t-xl flex items-center justify-center">
                                        <div class="text-center">
                                                <i class="ki-filled ki-picture text-4xl text-gray-400 mb-2"></i>
                                                <p class="text-gray-500">{{ __('provider/profile.basic_info.sections.no_image') }}</p>
                                        </div>
                                </div>
                                <div class="card-border card-rounded-b flex justify-center gap-2 px-5 py-4.5">
                                        <label for="logo_upload" class="btn btn-primary cursor-pointer">
                                                <i class="ki-filled ki-cloud-upload"></i>
                                                {{ __('provider/profile.basic_info.actions.upload_image') }}
                                        </label>
                                </div>
                        `;
              }
      </script>
@endpush